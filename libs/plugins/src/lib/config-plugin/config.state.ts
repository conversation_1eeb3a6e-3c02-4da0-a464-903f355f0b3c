import { Action, createSelector, Selector, State, StateContext } from '@ngxs/store';
import { Config } from './config-symbols';
import { Injectable } from '@angular/core';
import { Environment, FeatureFlagEnum } from '@libs/types';
import { OverrideConfig } from './config.actions';

@Injectable()
@State<Config.State>({
  name: 'ConfigState',
  defaults: {
    environment: null,
    production: false,
    app: null,
  },
})
export class ConfigState {
  @Selector()
  static getAll(state: Config.State) {
    return state;
  }

  static isFeatureEnabled(key: FeatureFlagEnum) {
    return createSelector([ConfigState], (state: Config.State): boolean => state.featureFlags?.[key] !== false);
  }

  static getOne<T extends keyof Environment.Model>(key: T) {
    return createSelector([ConfigState], (state: Config.State): Environment.Model[T] => state[key]);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static getDeep(keys: string[] | string): any {
    if (typeof keys === 'string') {
      keys = keys.split('.');
    }

    if (!Array.isArray(keys)) {
      throw new Error('The keys should either be a dot string or an array of strings.');
    }

    return createSelector([ConfigState], (state: Config.State): unknown => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return (keys as string[]).reduce((acc, val): any => {
        if (acc) {
          return acc[val];
        }

        return undefined;
      }, state);
    });
  }

  @Action(OverrideConfig)
  overrideConfig({ patchState }: StateContext<Config.State>, { payload }: OverrideConfig) {
    patchState(payload);
  }
}
