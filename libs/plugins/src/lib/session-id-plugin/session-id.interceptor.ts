import { HttpInterceptorFn } from '@angular/common/http';
import { shouldSkipInterception } from '@libs/core';
import { SessionIdService } from './session-id.service';
import { inject } from '@angular/core';

export const SessionIdInterceptor: HttpInterceptorFn = (request, next) => {
  if (shouldSkipInterception(request)) {
    return next(request);
  }

  const sessionIdService = inject(SessionIdService);
  const sessionId = sessionIdService.getSessionId();
  if (sessionId) {
    const clonedReq = request.clone({
      headers: request.headers.set('ety-session-id', sessionId),
    });
    return next(clonedReq);
  }

  return next(request);
};
