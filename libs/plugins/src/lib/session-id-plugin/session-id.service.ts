import { Injectable, inject } from '@angular/core';
import { FaroService } from '../faro-plugin';

@Injectable({
  providedIn: 'root',
})
export class SessionIdService {
  private faroService = inject(FaroService);
  private sessionId: string | null = null;

  constructor() {
    this.initializeSessionId();
  }

  private initializeSessionId(): void {
    const faroInstance = this.faroService.getFaroInstance();
    if (faroInstance?.api?.getSession()) {
      this.sessionId = faroInstance.api.getSession().id;
    } else {
      this.sessionId = this.generateB3SessionId();
      sessionStorage.setItem('x-session-id', this.sessionId);
    }
  }

  private generateB3SessionId(): string {
    return Array.from({ length: 16 }, () =>
      Math.floor(Math.random() * 256)
        .toString(16)
        .padStart(2, '0'),
    ).join('');
  }

  getSessionId(): string | null {
    if (!this.sessionId) {
      this.sessionId = sessionStorage.getItem('x-session-id');
    }
    return this.sessionId;
  }
}
