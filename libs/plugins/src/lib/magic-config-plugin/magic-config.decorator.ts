import { MagicResolverService } from './magic-resolver.service';
import { getComponentSelector } from './utils/angular.utils';
import { Type } from '@angular/core';
import { MagicConfigOptions } from './types';

export function MagicConfig<T>(options: MagicConfigOptions) {
  return function (target: Type<T>) {
    const { resolve } = options;

    if (resolve) {
      target.prototype.magicResolvers = resolve;
      MagicResolverService.addResolver(getComponentSelector(target), resolve);
    }
  };
}
