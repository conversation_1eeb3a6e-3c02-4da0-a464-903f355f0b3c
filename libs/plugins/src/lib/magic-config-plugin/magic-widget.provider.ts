import { Router } from '@angular/router';
import { inject, Injector, provideAppInitializer, Provider, runInInjectionContext } from '@angular/core';
import { endWith, forkJoin, ignoreElements, Observable, switchMap } from 'rxjs';
import { getMatchingComponentSelectors, getResolver } from './utils/angular.utils';
import { MagicResolverService } from './magic-resolver.service';
import { MagicResolverToObservable } from './utils';

export function provideMagicWidget(): Provider {
  return [provideAppInitializer(() => MagicWidgetResolveFactory(inject(Injector)))];
}

export function MagicWidgetResolveFactory(injector: Injector) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const router: any = injector.get(Router);
  const defaultAfterPreactivation: () => Observable<unknown> = router.navigationTransitions.afterPreactivation;

  router.navigationTransitions.afterPreactivation = (): Observable<unknown> => {
    MagicResolverService.setTargetSnapshot(router.navigationTransitions.currentTransition.targetSnapshot);

    const targetSnapshot = MagicResolverService.getTargetSnapshot();
    const selectors = getMatchingComponentSelectors(targetSnapshot.root, MagicResolverService.componentSelectors());

    return forkJoin(
      MagicResolverService.getResolvers(selectors).map((resolver) => getResolver(resolver, injector)),
    ).pipe(
      switchMap((resolvers) => runInInjectionContext(injector, () => MagicResolverToObservable(resolvers.flat()))),
      ignoreElements(),
      endWith(defaultAfterPreactivation()),
    );
  };
}
