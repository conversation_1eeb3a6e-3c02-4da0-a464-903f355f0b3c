import { Injectable } from '@angular/core';
import { TranslocoService } from '@jsverse/transloco';

@Injectable({
  providedIn: 'root',
})
export class TranslateService extends TranslocoService {
  hasTranslateKey(key: string): boolean | boolean[] {
    const translation = this.getTranslation(this.getActiveLang());

    return !!translation[key];
  }

  translateWithDefault(key: string, defaultKey: string): string {
    if (this.hasTranslateKey(key)) {
      return this.translate(key);
    }
    return this.translate(defaultKey);
  }
}
