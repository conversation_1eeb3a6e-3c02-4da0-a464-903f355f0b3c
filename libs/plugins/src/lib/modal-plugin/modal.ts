import { InputSignal, OutputEmitterRef, Type } from '@angular/core';

export type ModalCLoseEventType = 'backdropClick' | 'close' | 'escape';

export type ComponentInputAndOutput<T> = {
  [K in keyof T]: T[K] extends InputSignal<infer I>
    ? I
    : T[K] extends OutputEmitterRef<infer I>
      ? (props: I) => void
      : never;
};

export interface ModalCloseEvent<T> {
  type: ModalCLoseEventType;
  data: T;
}

export type ModalContent<T = unknown> = string | Type<T>;

export enum ModalSize {
  SMALL = 'sm',
  MEDIUM = 'md',
  LARGE = 'lg',
  XLARGE = 'xl',
  XXLARGE = 'xxl',
}

export enum ModalType {
  DEFAULT = 'default',
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
}

export type ModalData<T> = Partial<{
  data: Partial<ComponentInputAndOutput<T>>;
  size: ModalSize;
  title: string;
  iconName: string;
  type: ModalType;
  windowHeight: boolean;
}>;
