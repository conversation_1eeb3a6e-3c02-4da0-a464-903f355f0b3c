<div
  [class]="
    'base ' +
    modalSizeClasses[modalData.size] +
    ' ' +
    modalData.type +
    ' ' +
    (modalData.windowHeight ? 'window-height' : '')
  "
>
  <div class="header">
    <div class="header-content">
      @if (modalData.iconName || modalData.type !== 'default') {
        <eds-icon [name]="modalData.iconName || modalTypes[modalData.type]"></eds-icon>
      }
      <eds-heading size="md" [text]="modalData.title"></eds-heading>
    </div>
    <eds-button appearance="subtle" iconOnly iconLeading="cancel" (button-click)="close()"></eds-button>
  </div>
  <div class="content">
    <ng-container #content></ng-container>
  </div>
</div>
