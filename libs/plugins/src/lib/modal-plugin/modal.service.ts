import { BlockScrollStrategy, GlobalPositionStrategy, Overlay, OverlayConfig } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { inject, Injectable, Injector } from '@angular/core';
import { ModalRef } from './modal-ref';
import { ModalComponent } from './modal.component';
import { ModalContent, ModalData, ModalSize, ModalType } from './modal';
import { MODAL_DATA } from './modal-token';
import { of, Subject } from 'rxjs';
import { MagicResolverDetect } from '../magic-config-plugin';
import { uuid } from '@libs/bss';

const DEFAULT_MODAL_OPTIONS: ModalData<unknown> = {
  size: ModalSize.MEDIUM,
  type: ModalType.DEFAULT,
};

@Injectable({
  providedIn: 'root',
})
export class ModalService {
  private overlay = inject(Overlay);
  private injector = inject(Injector);
  private modalRefs = new Map<string, ModalRef>();

  private modalOpenedSubject = new Subject<void>();
  public modalOpened$ = this.modalOpenedSubject.asObservable();

  private modalClosedSubject = new Subject<void>();
  public modalClosed$ = this.modalClosedSubject.asObservable();

  open<T>(content: ModalContent<T>, options?: ModalData<T>): ModalRef<T> {
    const mergedOptions: ModalData<unknown> = {
      ...DEFAULT_MODAL_OPTIONS,
      ...options,
    };

    const overlayConfig = this.createOverlayConfig();

    const overlayRef = this.overlay.create(overlayConfig);

    const modalRef = new ModalRef<T>(uuid(), overlayRef, content, mergedOptions);

    const injector = Injector.create({
      providers: [
        {
          provide: ModalRef,
          useValue: modalRef,
        },
        {
          provide: MODAL_DATA,
          useValue: mergedOptions,
        },
      ],
      parent: this.injector,
    });

    this.modalRefs.set(modalRef.modalId, modalRef as ModalRef);
    this.modalOpenedSubject.next();

    modalRef.onClose$.subscribe(() => {
      this.modalRefs.delete(modalRef.modalId);
      this.modalClosedSubject.next();
    });

    const resolvers =
      typeof content === 'string' ? of(undefined) : new MagicResolverDetect(content).resolveComponent(injector);

    resolvers.subscribe(() => {
      const portal = new ComponentPortal(ModalComponent, null, injector);
      const componentRef = overlayRef.attach(portal);
      modalRef.modalComponentInstance = componentRef.instance;
    });

    return modalRef;
  }

  closeWithId(modalId: string) {
    const modalRef = this.modalRefs.get(modalId);
    if (modalRef) {
      modalRef.close();
    }
  }

  closeAll() {
    this.modalRefs.forEach((modalRef) => {
      modalRef.close();
    });
  }

  private createPositionStrategy(): GlobalPositionStrategy {
    return this.overlay.position().global().centerHorizontally().centerVertically();
  }

  private createScrollStrategy(): BlockScrollStrategy {
    return this.overlay.scrollStrategies.block();
  }

  private createOverlayConfig() {
    return new OverlayConfig({
      disposeOnNavigation: true,
      hasBackdrop: true,
      positionStrategy: this.createPositionStrategy(),
      scrollStrategy: this.createScrollStrategy(),
    });
  }
}
