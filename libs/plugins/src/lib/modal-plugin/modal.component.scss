:host {
  overflow-y: auto;
  padding: var(--eds-spacing-400);

  &::-webkit-scrollbar {
    display: none;
  }
}

.base {
  display: grid;
  background-color: var(--eds-colors-surface-default);
  border-radius: var(--eds-radius-200);
  box-shadow: var(--eds-shadow-sm);
  width: 100svw;

  &.sm {
    width: clamp(calc(var(--eds-size-multiplier) * 1), 90svw, calc(var(--eds-size-multiplier) * 100));
  }

  &.md {
    width: clamp(calc(var(--eds-size-multiplier) * 71), 90svw, calc(var(--eds-size-multiplier) * 150));
  }

  &.lg {
    width: clamp(calc(var(--eds-size-multiplier) * 71), 90svw, calc(var(--eds-size-multiplier) * 188));
  }

  &.xl {
    width: clamp(calc(var(--eds-size-multiplier) * 71), 90svw, calc(var(--eds-size-multiplier) * 242));
  }

  &.window-height {
    display: flex;
    flex-direction: column;
    max-height: 80svh;

    .content {
      overflow-y: auto;
      height: 100%;
      max-height: 100%;
      flex: 1;
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  eds-icon {
    border-radius: var(--eds-radius-full);

    &::part(icon) {
      width: var(--eds-sizing-400);
      height: var(--eds-sizing-400);
    }

    @media (min-width: 834px) {
      padding: var(--eds-spacing-200);
    }
  }

  &.success {
    eds-icon {
      background-color: var(--eds-colors-success-lighter);
      color: var(--eds-colors-success-default);
    }
  }

  &.warning {
    eds-icon {
      background-color: var(--eds-colors-warning-lighter);
      color: var(--eds-colors-warning-default);
    }
  }

  &.error {
    eds-icon {
      background-color: var(--eds-colors-danger-lighter);
      color: var(--eds-colors-danger-default);
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--eds-spacing-400);
    border-bottom: 1px solid var(--eds-border-color-default);

    .header-content {
      display: flex;
      align-items: center;
      gap: var(--eds-spacing-300);
    }

    @media (min-width: 834px) {
      padding: var(--eds-spacing-600);
    }
  }

  .content {
    padding: var(--eds-spacing-400);

    @media (min-width: 834px) {
      padding: var(--eds-spacing-600);
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--eds-spacing-200);
    padding: var(--eds-spacing-600);
    border-top: 1px solid var(--eds-border-color-default);
  }
}
