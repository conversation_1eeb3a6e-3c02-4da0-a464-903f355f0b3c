import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ComponentRef,
  CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  inject,
  OutputEmitterRef,
  signal,
  Type,
  viewChild,
  ViewContainerRef,
} from '@angular/core';
import { ModalRef } from './modal-ref';
import { ModalData, ModalSize, ModalType } from './modal';
import { MODAL_DATA } from './modal-token';
import { AnimationEvent } from '@angular/animations';
import { DOCUMENT } from '@angular/common';

type ModalAnimationStateType = 'void' | 'open' | 'closed';

@Component({
  selector: 'modal',
  templateUrl: './modal.component.html',
  styleUrls: ['./modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ModalComponent implements AfterViewInit {
  private document = inject(DOCUMENT);
  private ref = inject(ModalRef);
  private contentContainer = viewChild('content', { read: ViewContainerRef });

  modalData = inject<ModalData<unknown>>(MODAL_DATA);
  componentRef?: ComponentRef<unknown>;
  animationStateChanged = new EventEmitter<AnimationEvent>();

  contentType: 'template' | 'string' | 'component';
  modalSizeClasses: Record<ModalSize, string> = {
    [ModalSize.SMALL]: 'sm',
    [ModalSize.MEDIUM]: 'md',
    [ModalSize.LARGE]: 'lg',
    [ModalSize.XLARGE]: 'xl',
    [ModalSize.XXLARGE]: 'xxl',
  };

  modalWindowHeight: { true: string; false: string } = {
    true: 'window-height',
    false: '',
  };

  modalTypes: Record<ModalType, string> = {
    [ModalType.DEFAULT]: this.modalData.iconName,
    [ModalType.INFO]: 'tick',
    [ModalType.SUCCESS]: 'tick',
    [ModalType.WARNING]: 'warning',
    [ModalType.ERROR]: 'delete',
  };

  animationStateSignal = signal<ModalAnimationStateType>('open');

  close(): void {
    this.ref.close();
  }

  startExitAnimation(): void {
    this.animationStateSignal.set('closed');
  }

  isComponent(): boolean {
    return this.ref.content instanceof Type;
  }

  ngAfterViewInit(): void {
    this.contentContainer()?.clear();
    if (this.isComponent()) {
      this.renderComponent();
    } else {
      this.renderHtml();
    }
  }

  renderComponent(): void {
    this.componentRef = this.contentContainer()?.createComponent(this.ref.content as Type<unknown>);
    Object.entries(this.componentRef.instance).forEach(([key, value]) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const inputValue = (this.modalData?.data as Record<string, any>)?.[key];
      if (inputValue === undefined) {
        return;
      }
      if (value instanceof OutputEmitterRef) {
        value.subscribe((...args) => {
          inputValue?.(...args);
        });
      } else if (typeof value === 'function') {
        this.componentRef?.setInput(key, inputValue);
      }
    });
  }

  renderHtml(): void {
    const content = this.document.createElement('div');
    content.innerHTML = this.ref.content as string;
    this.contentContainer()?.element.nativeElement.appendChild(content);
  }
}
