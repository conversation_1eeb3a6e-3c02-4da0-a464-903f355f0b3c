import { inject, Injectable } from '@angular/core';
import { Faro, getWebInstrumentations, initializeFaro } from '@grafana/faro-web-sdk';
import { TracingInstrumentation } from '@grafana/faro-web-tracing';
import { Environment, LogLevel } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class FaroService {
  private environment = inject(Environment.ENVIRONMENT);
  private faroInstance: Faro = null;

  initializeFaro(): void {
    const faroConfig = this.environment.faro;

    if (!faroConfig?.collectorUrl) {
      console.warn('Faro collector URL not configured, skipping Faro initialization');
      return;
    }

    try {
      this.faroInstance = initializeFaro({
        url: faroConfig.collectorUrl,
        app: {
          name: faroConfig.appName || this.environment.app || 'wsc',
          version: faroConfig.appVersion || '1.0.0',
          environment: faroConfig.environment || this.environment.environment || 'development',
        },
        instrumentations: [
          ...getWebInstrumentations({
            captureConsole: true,
            captureConsoleDisabledLevels: [],
          }),
          new TracingInstrumentation(),
        ],
      });
    } catch (error) {
      console.error('Failed to initialize Faro:', error);
    }
  }

  getFaroInstance() {
    return this.faroInstance;
  }

  // Helper methods for manual tracing
  pushEvent(name: string, attributes?: Record<string, string>): void {
    if (this.faroInstance) {
      this.faroInstance.api.pushEvent(name, attributes);
    }
  }

  pushError(error: Error, context?: Record<string, string>): void {
    if (this.faroInstance) {
      this.faroInstance.api.pushError(error, context);
    }
  }

  pushLog(message: string, level: LogLevel = LogLevel.INFO, context?: Record<string, string>): void {
    if (this.faroInstance) {
      this.faroInstance.api.pushLog([message], { level: level, context });
    }
  }
}
