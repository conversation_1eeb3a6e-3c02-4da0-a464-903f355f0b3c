# Faro Plugin

Bu plugin, Grafana Faro web tracing implementasyonunu Angular uygulamasına entegre eder.

## <PERSON><PERSON><PERSON><PERSON>

- Grafana Faro SDK ile web tracing
- OpenTelemetry tabanlı tracing
- Otomatik browser instrumentation
- Console log yakalama
- Error tracking
- Custom event ve log gönderimi

## Kurulum

Plugin zaten `@libs/plugins` içinde mevcut ve gerekli paketler yüklenmiş durumda:

```bash
npm install @grafana/faro-web-sdk @grafana/faro-web-tracing
```

## Kullanım

### 1. Provider'ı App Config'e Ekleme

```typescript
// apps/wsc/src/app/app.config.ts
import { provideFaro } from '@libs/plugins';

export const appConfig: ApplicationConfig = {
  providers: [
    // ... diğer provider'lar
    provideFaro(),
  ],
};
```

### 2. Environment Configuration

`environment.json` dosyasına Faro konfigürasyonunu ekleyin:

```json
{
  "faro": {
    "collectorUrl": "https://faro-collector.grafana.net/collect/YOUR_INSTANCE_ID",
    "appName": "wsc-app",
    "appVersion": "1.0.0",
    "environment": "production"
  }
}
```

### 3. Service Kullanımı

```typescript
import { FaroService } from '@libs/plugins';

export class MyComponent {
  private faroService = inject(FaroService);

  someMethod() {
    // Event gönderme
    this.faroService.pushEvent('user_action', {
      action: 'button_click',
      component: 'MyComponent',
    });

    // Log gönderme
    this.faroService.pushLog('User performed an action', 'info', {
      userId: '123',
      timestamp: new Date().toISOString(),
    });

    // Error gönderme
    try {
      // some code
    } catch (error) {
      this.faroService.pushError(error as Error, {
        context: 'MyComponent.someMethod',
      });
    }
  }
}
```

## Configuration Options

### FaroEnv Interface

```typescript
interface FaroEnv {
  collectorUrl: string; // Faro collector URL (zorunlu)
  appName?: string; // Uygulama adı (opsiyonel)
  appVersion?: string; // Uygulama versiyonu (opsiyonel)
  environment?: string; // Environment (opsiyonel)
}
```

### Otomatik Instrumentation

Plugin otomatik olarak şunları yakalar:

- Console logs
- JavaScript errors
- Network requests (fetch/XHR)
- User interactions
- Page navigation

## Grafana Cloud Setup

1. Grafana Cloud hesabınızda Faro instance oluşturun
2. Collector URL'ini alın
3. Environment configuration'ınızı güncelleyin
4. Uygulamanızı deploy edin

## Development

Development ortamında Faro'yu test etmek için:

1. Geçerli bir collector URL kullanın
2. Browser developer tools'da Network tab'ını kontrol edin
3. Console'da Faro log mesajlarını kontrol edin

## Troubleshooting

### Faro Initialize Olmuyor

- `environment.json` dosyasında `faro.collectorUrl` değerinin doğru olduğundan emin olun
- Network tab'ında collector URL'ine istek gönderilip gönderilmediğini kontrol edin
- Console'da hata mesajları olup olmadığını kontrol edin

### Events Gönderilmiyor

- Faro instance'ının doğru initialize olduğundan emin olun
- Collector URL'inin erişilebilir olduğundan emin olun
- CORS ayarlarını kontrol edin

## API Reference

### FaroService Methods

- `initializeFaro()`: Faro SDK'yı initialize eder
- `getFaroInstance()`: Faro instance'ını döner
- `pushEvent(name, attributes)`: Custom event gönderir
- `pushError(error, context)`: Error gönderir
- `pushLog(message, level, context)`: Log gönderir
