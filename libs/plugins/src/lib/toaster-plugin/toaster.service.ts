import { Injectable } from '@angular/core';
import { Toast, ToastOptions } from '@eds/components';

@Injectable({
  providedIn: 'root',
})
export class ToasterService {
  private toast: Toast = document.createElement('eds-toast') as Toast;

  defaults: Partial<ToastOptions> = {
    duration: 5,
    dismissible: true,
  };

  constructor() {
    document.body.appendChild(this.toast);
  }

  showToaster(options: ToastOptions) {
    this.toast.show({ ...this.defaults, ...options });
  }

  info(options: ToastOptions) {
    this.showToaster({
      ...options,
      appearance: 'info',
    });
  }

  success(options: ToastOptions) {
    this.showToaster({
      ...options,
      appearance: 'success',
    });
  }

  warning(options: ToastOptions) {
    this.showToaster({
      ...options,
      appearance: 'warning',
    });
  }

  error(options: ToastOptions) {
    this.showToaster({
      ...options,
      appearance: 'error',
    });
  }
}
