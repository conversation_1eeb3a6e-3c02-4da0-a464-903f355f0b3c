import { REGEX_NO_EMOJI } from './constants';
import {
  ageValidator,
  creditCardExpireDateValidator,
  dateFormatValidator,
  minDateValidator,
  regexValidator,
} from './utils';

export class BSSValidators {
  static noEmoji = regexValidator(REGEX_NO_EMOJI, 'noEmoji');
  static creditCardExpireDate = creditCardExpireDateValidator();
  static age = ageValidator;
  static minDate = minDateValidator;
  static dateFormat = dateFormatValidator;
  static regex = regexValidator;
}
