import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  ElementRef,
  forwardRef,
  input,
  OnChanges,
  signal,
  SimpleChanges,
  viewChild,
} from '@angular/core';
import { NG_VALUE_ACCESSOR, Validators } from '@angular/forms';
import { DatePicker, PhoneNumber, Select, SelectOption, TextField, TextFieldTypeValues } from '@eds/components';
import { AbstractControlValueAccessorComponent } from '../../../abstracts';
import { FormFieldErrorMessageComponent, InputType } from '@libs/plugins';
import { uuid } from '@libs/bss';
import IMask, { FactoryArg, InputMask, MaskedOptions } from 'imask';

@Component({
  selector: 'widget-form-field',
  template: `
    <eds-form-field [label]="label()" [placeholder]="placeholder()" [isRequired]="required()" #edsFormField>
      <widget-form-field-error-message [errors]="errors()"></widget-form-field-error-message>
    </eds-form-field>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FormFieldComponent),
      multi: true,
    },
  ],
  imports: [FormFieldErrorMessageComponent],
})
export class FormFieldComponent extends AbstractControlValueAccessorComponent implements OnChanges {
  id = input<string>(`field-${uuid()}`);
  label = input<string>('');
  placeholder = input<string>('');
  type = input<InputType>('text');
  maxLength = input<string>('200');
  isSearchable = input<boolean>(false);
  options = input<{ [key: string]: unknown }>({});
  mask = input<FactoryArg>();
  imaskInstance: InputMask<FactoryArg>;

  edsFormFieldRef = viewChild.required<ElementRef<HTMLElement>>('edsFormField');

  input: TextField | PhoneNumber | Select | DatePicker;

  required = signal(false);

  constructor() {
    super();
    effect(() => {
      const errors = this.errors();

      if (this.input && 'isInvalid' in this.input) {
        this.input.isInvalid = !!errors;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.formControl && !changes.mask) {
      this.onFormControlReady();
    }

    if (changes.mask) {
      const newMaskOptions = this.mask();
      if (newMaskOptions) {
        if (this.imaskInstance) {
          this.imaskInstance.updateOptions(newMaskOptions as MaskedOptions);
        } else {
          this.setupMaskInstance(newMaskOptions);
        }
      } else {
        if (this.imaskInstance) {
          this.imaskInstance.destroy();
          this.imaskInstance = undefined;
        }
      }
    }
  }

  onFormControlReady(): void {
    this.required.set(this.formControl.hasValidator(Validators.required));

    switch (this.type()) {
      case 'date':
        this.renderDatePicker();
        break;
      case 'phoneNumber':
        this.renderPhoneNumber();
        break;
      case 'select':
        this.renderSelect();
        break;
      default:
        this.renderTextField(this.type() as TextFieldTypeValues);
        break;
    }
  }

  renderPhoneNumber() {
    this.input = this.createInput<PhoneNumber>('eds-phone-number');
    this.clearElement(this.input);
    this.defaultProperty();
  }

  renderSelect() {
    this.input = this.createInput<Select>('eds-select');
    this.clearElement(this.input);
    this.setupSelectOptions();
    this.input.isSearchable = this.isSearchable();
    this.defaultProperty();
  }

  private setupSelectOptions(): void {
    const options = (this.options().options as SelectOption[]) ?? [];

    options.forEach((option) => {
      const selectOption = this.createInput<SelectOption>('eds-select-option');
      selectOption.label = option.label;
      selectOption.value = option.value;
      selectOption.name = option.name;
      selectOption.isSelected = option.isSelected;
      selectOption.isDisabled = option.isDisabled;
      this.input.appendChild(selectOption as unknown as Node);
    });
  }

  renderDatePicker() {
    this.input = this.createInput<DatePicker>('eds-date-picker');
    this.clearElement(this.input);
    this.defaultProperty();
  }

  renderTextField(type: TextFieldTypeValues) {
    this.input = this.createInput<TextField>('eds-text-field');
    this.clearElement(this.input);
    this.setupTextField(type);
    this.defaultProperty();
  }

  private setupTextField(type: TextFieldTypeValues): void {
    const textField = this.input as TextField;

    if (this.maxLength()) {
      textField.maxlength = this.maxLength();
    }
    textField.type = type;
  }

  defaultProperty() {
    this.setInputProperties();
    this.setupEventListeners();
    this.appendToContainer();
    this.setupMask();
  }

  private setInputProperties(): void {
    this.input.id = this.id();
    this.input.value = this.value() || '';
    this.input.placeholder = this.placeholder();
    this.input.isInvalid = !!this.errors();
    this.input.isDisabled = this.formControl.disabled;
    this.input.isRequired = this.required();

    // Update the real input element inside shadow DOM
    this.updateInternalInput();

    // Apply options
    Object.assign(this.input, this.options());
  }

  private updateInternalInput(): void {
    const internalInput = this.getInternalInputElement();
    if (internalInput) {
      internalInput.value = this.value() || '';
    }
  }

  private setupEventListeners(): void {
    this.input.addEventListener('input', (event: Event) => {
      this.handleInput(this.getValue((event.target as HTMLInputElement).value));
    });

    this.input.addEventListener('change', (event: Event) => {
      this.handleInput(this.getValue((event.target as HTMLInputElement).value));
    });

    this.input.addEventListener('blur', () => {
      this.onTouched();
    });
  }

  private appendToContainer(): void {
    const container = this.edsFormFieldRef().nativeElement;
    const errorMessageElement = container.querySelector('widget-form-field-error-message');

    // Remove existing input
    if (container.firstChild && container.firstChild !== errorMessageElement) {
      container.removeChild(container.firstChild);
    }

    // Add new input
    if (errorMessageElement) {
      container.insertBefore(this.input, errorMessageElement);
    } else {
      container.appendChild(this.input);
    }
  }

  private setupMask(): void {
    if (this.mask() && !this.imaskInstance) {
      this.setupMaskInstance(this.mask());
    }
  }

  private setupMaskInstance(options: FactoryArg): void {
    if (this.imaskInstance) {
      this.imaskInstance.destroy();
      this.imaskInstance = undefined;
    }

    const inputEl = this.getInternalInputElement();
    if (inputEl) {
      this.imaskInstance = IMask(inputEl, options);
    } else {
      setTimeout(() => {
        if (this.mask() === options && !this.imaskInstance) {
          this.setupMaskInstance(options);
        }
      }, 100);
    }
  }

  private getInternalInputElement(): HTMLInputElement | null {
    if (!this.input || !this.input.shadowRoot) {
      return null;
    }

    // First look for input element directly
    let internalInput = this.input.shadowRoot.querySelector('input');

    // If not found, look inside eds-text-field
    if (!internalInput) {
      const edsTextField = this.input.shadowRoot.querySelector('eds-text-field');
      if (edsTextField?.shadowRoot) {
        internalInput = edsTextField.shadowRoot.querySelector('input');
      }
    }

    return internalInput;
  }

  private getInternalSelectElement(): HTMLSelectElement | null {
    if (!this.input || !this.input.shadowRoot) {
      return null;
    }

    // First look for select element directly
    let internalSelect = this.input.shadowRoot.querySelector('select');

    // If not found, look inside eds-select
    if (!internalSelect) {
      const edsSelect = this.input.shadowRoot.querySelector('eds-select');
      if (edsSelect?.shadowRoot) {
        internalSelect = edsSelect.shadowRoot.querySelector('select');
      }
    }

    return internalSelect;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getValue(value: any) {
    if (this.type() === 'phoneNumber' && this.imaskInstance) {
      return { ...value, phone: this.imaskInstance.unmaskedValue };
    }
    return this.imaskInstance && this.type() !== 'date' ? this.imaskInstance.unmaskedValue : value;
  }

  private createInput<T>(selector: string): T {
    if (this.input?.tagName === selector.toUpperCase()) {
      return this.input as T;
    }

    return document.createElement(selector) as T;
  }

  private clearElement(element: HTMLElement) {
    element.innerHTML = '';
  }

  protected updateInputValue(value: string | null): void {
    if (!this.input) return;

    const inputValue = value || '';

    // Update based on input type
    switch (this.type()) {
      case 'text':
      case 'email':
      case 'number':
      case 'password':
        this.updateTextField(inputValue);
        break;
      case 'select': // todo: refactoring
      case 'date':
        this.updateDateField(inputValue);
        break;
      case 'phoneNumber':
        this.updatePhoneField(inputValue);
        break;
    }

    // Update IMask instance
    if (this.imaskInstance) {
      this.imaskInstance.value = inputValue;
    }
  }

  private updateTextField(inputValue: string): void {
    if (this.input && 'value' in this.input) {
      this.input.value = inputValue;

      const internalInput = this.getInternalInputElement();
      if (internalInput) {
        internalInput.value = inputValue;
      }
    }
  }

  private updateDateField(inputValue: string): void {
    if (this.input && 'value' in this.input) {
      this.input.value = inputValue;

      const internalInput = this.getInternalInputElement();
      if (internalInput) {
        internalInput.value = inputValue;
      }
    }
  }

  private updatePhoneField(inputValue: string): void {
    if (this.input && 'value' in this.input) {
      this.input.value = inputValue;

      const internalInput = this.getInternalInputElement();
      if (internalInput) {
        internalInput.value = inputValue;
      }
    }
  }
}
