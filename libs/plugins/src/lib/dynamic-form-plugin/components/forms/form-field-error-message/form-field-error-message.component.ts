import { Component, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { KeyValuePipe } from '@angular/common';
import { ValidationErrors } from '@angular/forms';
import { TranslateService } from '@libs/plugins';

@Component({
  selector: 'widget-form-field-error-message',
  template: ` @if (errors()) {
    @for (error of errors() | keyvalue; track error.key) {
      <div class="error">
        <eds-icon class="error-icon" name="alertCircle"></eds-icon>
        <eds-text
          class="error-message"
          as="p"
          [text]="getErrorText(error.key, error.value)"
          weight="regular"
          size="sm"
        ></eds-text>
      </div>
    }
  }`,
  styleUrls: ['./form-field-error-message.component.scss'],
  imports: [KeyValuePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class FormFieldErrorMessageComponent {
  errors = input<ValidationErrors | null>(null);
  translate = inject(TranslateService);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getErrorText(key: string, value: any): string {
    const translationKey = `formErrors.${key}`;
    switch (key) {
      case 'minlength':
      case 'maxlength':
        return this.translate.translate(translationKey, {
          requiredLength: value.requiredLength,
          actualLength: value.actualLength,
        });
      case 'invalidPrivacyRequirements':
        return this.translate.translate(translationKey, {
          invalidPrivacyRequirements: value.invalidPrivacyRequirements,
        });
      case 'min':
        return this.translate.translate(translationKey, { min: value.min });
      case 'max':
        return this.translate.translate(translationKey, { key: value[key] });
      case 'isNotAdult':
        return this.translate.translate(translationKey, { minAge: value.minAge });
      default:
        return this.translate.translate(translationKey);
    }
  }
}
