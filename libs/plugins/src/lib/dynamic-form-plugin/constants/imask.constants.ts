export const CREDIT_CARD_MASK = '0000 0000 0000 0000';
export const CVC_MASK = '000';
export const PHONE_NUMBER_MASK = '(*************';
export const ICCID_MASK = '0000 0000 0000 0000 00 0 0';
export const IBAN_CONFIGURATIONS = {
  TR: {
    mask: 'aa00 0000 0000 0000 0000 0000 00',
    regex: /^TR[0-9]{24}$/i,
  },
  FR: {
    mask: 'aa00 0000 0000 00** **** **** *00',
    regex: /^FR[0-9]{12}[A-Z0-9]{11}[0-9]{2}$/i,
  },
};
export const DEFAULT_IBAN_COUNTRY_CODE: keyof typeof IBAN_CONFIGURATIONS = 'TR';
export const IBAN_COUNTRY_CODE_LENGTH = 2;
export const REGEX_NO_EMOJI =
  /^[^\u{1F000}-\u{1F9FF}\u{1F300}-\u{1F5FF}\u{1F600}-\u{1F64F}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{1F1E6}-\u{1F1FF}]*$/u;

export const UNICODE_MASK = /^[\p{L}\p{M}]+(?: [\p{L}\p{M}]+)* ?$/u;
// export const MASK_ICCID = '0'.repeat(20);
export const MASK_ICCID = '00 00 00 0 00 00 00 000000 0';

export const REGEX_UNICODE_NAME = /^[\p{L}\p{M}]+(?: [\p{L}\p{M}]+)* ?$/u;
export const REGEX_EMAIL = /^[_a-zA-Z0-9-]+(\.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*(\.[a-zA-Z]{2,})$/;
export const REGEX_PASSWORD_UPPER = /^(?=.*?[A-Z])/;
export const REGEX_PASSWORD_LOWER = /^(?=.*?[a-z])/;
export const REGEX_PASSWORD_NUMERIC = /^(?=.*?\d)/;
export const REGEX_PASSWORD_SPECIAL = /^(?=.*?[!@#$%^&*()_+\-=\\?\\[\]\\.{}'])/;
export const REGEX_NUMERIC = /^[0-9]+$/;
export const REGEX_DOUBLE = /^[0-9]+(\.[0-9]{1,2})?$/;
