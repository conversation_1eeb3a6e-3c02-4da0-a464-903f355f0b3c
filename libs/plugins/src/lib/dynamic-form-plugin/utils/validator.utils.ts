import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { parse, isValid, format } from 'date-fns';
import { REGISTER_DATE_FORMAT } from '@libs/core';

export function ageValidator(minAge: number | string = 18): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    let dateStr = control.value;
    if (!dateStr || typeof dateStr !== 'string') return null;

    const today = new Date();

    dateStr = dateStr.slice(0, 10);

    if (/[a-zA-Z]/.test(dateStr)) {
      return { containsInvalidCharacters: true };
    }

    const birthDate = parse(dateStr, REGISTER_DATE_FORMAT, new Date());

    if (!isValid(birthDate)) {
      return { isNotAdult: true };
    }

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    const dayDiff = today.getDate() - birthDate.getDate();

    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
      age--;
    }

    return age >= Number(minAge)
      ? null
      : {
          isNotAdult: {
            minAge: Number(minAge),
          },
        };
  };
}

export function minDateValidator(minDate: string = '01-01-1900'): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null;
    }

    const controlDate = new Date(control.value);
    const minimumDate = new Date(minDate);

    return controlDate < minimumDate ? { min: { min: new Date(minimumDate).toLocaleDateString('en-GB') } } : null;
  };
}

export function regexValidator(regex: RegExp, msg: string): ValidatorFn {
  return (control: AbstractControl): { [key: string]: unknown } | null => {
    if (control.value && control.value.length > 0) {
      const valid = regex.test(control.value);
      return valid ? null : { [msg]: msg };
    } else {
      return null;
    }
  };
}

export function dateFormatValidator(expectedFormat: string): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null;
    }

    const parsedDate = parse(control.value, expectedFormat, new Date());

    if (!isValid(parsedDate)) {
      return { invalidFormat: true };
    }

    const formattedDate = format(parsedDate, expectedFormat);
    if (formattedDate !== control.value) {
      return { invalidFormat: true };
    }

    return null;
  };
}

export function creditCardExpireDateValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (value && value.length === 4) {
      const month = parseInt(value.slice(0, 2), 10);
      const year = parseInt(value.slice(2), 10);

      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYear = currentDate.getFullYear() % 100;

      if (isNaN(month) || month < 1 || month > 12) {
        return { invalidMonth: true };
      }

      if (isNaN(year) || year < currentYear || (year === currentYear && month < currentMonth)) {
        return { expiredDate: true };
      }

      return null;
    }

    return { invalidFormat: true };
  };
}
