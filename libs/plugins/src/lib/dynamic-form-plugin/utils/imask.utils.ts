import IMask, { FactoryArg } from 'imask';
import { IMASK_DATE_FORMAT } from '@libs/core';
import {
  CREDIT_CARD_MASK,
  CVC_MASK,
  DEFAULT_IBAN_COUNTRY_CODE,
  IBAN_CONFIGURATIONS,
  MASK_ICCID,
  PHONE_NUMBER_MASK,
  REGEX_NUMERIC,
  UNICODE_MASK,
} from '../constants';

export function createCreditCardExpireDateMask(): FactoryArg {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear() % 100;

  return {
    mask: 'MM/YY',
    blocks: {
      MM: {
        mask: IMask.MaskedRange,
        from: 1,
        to: 12,
      },
      YY: {
        mask: IMask.MaskedRange,
        from: currentYear,
        to: 99,
      },
    },
  };
}

export function createBirthDateMask(minAge?: number): FactoryArg {
  return {
    mask: IMASK_DATE_FORMAT,
    blocks: {
      DD: {
        mask: IMask.MaskedRange,
        from: 1,
        to: 31,
      },
      MM: {
        mask: IMask.MaskedRange,
        from: 1,
        to: 12,
      },
      YYYY: {
        mask: IMask.MaskedRange,
        from: 1900,
        to: minAge ? new Date().getFullYear() - minAge : new Date().getFullYear(),
      },
    },
  };
}

export function createPhoneNumberMask(): FactoryArg {
  return {
    mask: PHONE_NUMBER_MASK,
  };
}

export function createCustomerOrderIdMask(): FactoryArg {
  return {
    mask: REGEX_NUMERIC,
  };
}

export function createNumericMask(): FactoryArg {
  return {
    mask: Number,
    scale: 2,
    radix: '.',
    mapToRadix: ['.', ','],
    normalizeZeros: true,
    padFractionalZeros: false,
    min: 0,
    max: 999999999.99,
    lazy: false,
    prepare: (str: string) => str.replace(/[^\d.,]/g, ''),
  };
}

export function createICCIDMask(): FactoryArg {
  return {
    mask: MASK_ICCID,
  };
}

export function createCreditCardMask(): FactoryArg {
  return {
    mask: CREDIT_CARD_MASK,
  };
}

export function createCvcMask(): FactoryArg {
  return {
    mask: CVC_MASK,
  };
}

export function createIbanMask(countryCode: keyof typeof IBAN_CONFIGURATIONS = DEFAULT_IBAN_COUNTRY_CODE): FactoryArg {
  return {
    mask: IBAN_CONFIGURATIONS[countryCode].mask || IBAN_CONFIGURATIONS.TR.mask,
    prepare: (str) => str.toUpperCase(),
  };
}

export function createAlphabeticMask(): FactoryArg {
  return {
    mask: UNICODE_MASK,
    lazy: false,
    prepare: (str: string) => str.normalize('NFC'),
  };
}
