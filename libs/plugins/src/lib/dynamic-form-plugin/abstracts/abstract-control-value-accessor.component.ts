import { AfterViewInit, Component, inject, Injector, input, signal } from '@angular/core';
import { ControlValueAccessor, FormControl, NgControl, ValidationErrors } from '@angular/forms';
import { injectDestroy } from '@libs/core';
import { debounceTime, takeUntil } from 'rxjs';

@Component({ template: '' })
export abstract class AbstractControlValueAccessorComponent implements ControlValueAccessor, AfterViewInit {
  private _injector = inject(Injector);
  private _destroy$ = injectDestroy();

  value = signal('');
  errors = signal<ValidationErrors | null>(null);
  errorBounce = input(0);

  formControl: FormControl;
  private ngControl: NgControl;

  abstract onFormControlReady(): void;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onChange = (value: string) => {};
  onTouched = () => {};

  writeValue(value: string | null): void {
    this.value.set(value || '');
    this.updateInputValue(value);
  }

  protected abstract updateInputValue(value: string | null): void;

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  handleInput(value: string): void {
    this.value.set(value);
    this.onChange(value);
  }

  ngAfterViewInit(): void {
    this.initFormControl();
    this.listenChanges();
    this.onFormControlReady();
  }

  private initFormControl() {
    this.ngControl = this._injector.get(NgControl, null, {
      self: true,
      optional: true,
    });
    if (!this.ngControl) {
      throw new Error('widget-form-field must be used with ngModel, formControlName or formControl');
    }

    this.formControl = this.ngControl.control as FormControl;
  }

  private listenChanges() {
    const operators = this.errorBounce() ? [debounceTime(this.errorBounce())] : [];
    setTimeout(() => {
      this.errors.set(null);
    });

    this.formControl.events.pipe(takeUntil(this._destroy$), ...(operators as [])).subscribe(() => {
      if (this.formControl.touched) {
        this.errors.set(this.formControl.errors);
      } else {
        this.errors.set(null);
      }
    });
  }
}
