import { inject, Pipe, PipeTransform, LOCALE_ID } from '@angular/core';
import { DatePipe } from '@angular/common';
import { Store } from '@ngxs/store';
import { ConfigState } from '../config-plugin/config.state';
import { DATE_FORMATS, KnownLocalStorageKeys, LocalStorageService } from '@libs/core';
import { TranslateService } from '@libs/plugins';

@Pipe({
  name: 'date',
  pure: true,
})
export class CustomDatePipe implements PipeTransform {
  private readonly coreDatePipe: DatePipe;
  private readonly store = inject(Store);
  private readonly defaultLocale = inject(LOCALE_ID);
  private readonly localStorageService = inject(LocalStorageService);
  private readonly translateService = inject(TranslateService);

  constructor() {
    this.coreDatePipe = new DatePipe(this.defaultLocale);
  }

  transform(value: any, format?: string, timezone?: string, locale?: string): string | null {
    if (!value) return null;

    const resolvedFormat = format || DATE_FORMATS.SHORT_DATE;
    const environmentFormat = this.getFormatFromEnvironment(resolvedFormat);
    const finalFormat = environmentFormat || resolvedFormat;

    return this.coreDatePipe.transform(value, finalFormat, timezone, locale);
  }

  private getFormatFromEnvironment(formatKey: string): string | null {
    try {
      const localization = this.getLocalizationConfig();
      if (!localization?.date) return null;

      const currentLanguage = this.getCurrentLanguage();
      const dateFormats = localization.date[currentLanguage];

      return dateFormats?.[formatKey] || null;
    } catch (error) {
      console.warn('CustomDatePipe: Environment format could not be retrieved:', error);
      return null;
    }
  }

  private getCurrentLanguage(): string {
    // Option 1: Locale'den al
    // return this.defaultLocale.split('-')[0];

    // Option 2: Store'dan al (eğer language state varsa)
    // return this.store.selectSnapshot(LanguageState.getCurrentLanguage) || 'en';

    return (
      this.localStorageService.get<string>(KnownLocalStorageKeys.LANGUAGE) || this.translateService.getActiveLang()
    );
  }

  private getLocalizationConfig(): any {
    return this.store.selectSnapshot(ConfigState.getDeep('localization'));
  }
}
