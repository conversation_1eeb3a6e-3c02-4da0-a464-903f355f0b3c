import { inject, provideAppInitializer, Provider } from '@angular/core';
import { provideStates } from '@ngxs/store';
import { AnalyticsState, AnalyticsService } from '@libs/plugins';

export function analyticsProvider(): Provider {
  return [provideStates([AnalyticsState]), provideAppInitializer(() => analyticsInitializer())];
}

function analyticsInitializer(): void {
  const analyticsService = inject(AnalyticsService);
  analyticsService.init();
}
