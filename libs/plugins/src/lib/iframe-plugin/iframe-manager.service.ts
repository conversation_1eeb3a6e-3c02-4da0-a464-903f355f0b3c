import { inject, Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { Store } from '@ngxs/store';
import { ContextDataType, eBusinessFlow } from '@libs/types';
import { LoaderAddAction } from '@libs/core';

@Injectable({
  providedIn: 'root',
})
export class IframeManagerService {
  BIList$ = new BehaviorSubject(null);
  private store = inject(Store);

  startOldUIBI(payload: ContextDataType<eBusinessFlow.Specification>) {
    this.store.dispatch(new LoaderAddAction('REMOTE_BI_START_' + payload.flow));
    this.BIList$.next(payload);
  }
}
