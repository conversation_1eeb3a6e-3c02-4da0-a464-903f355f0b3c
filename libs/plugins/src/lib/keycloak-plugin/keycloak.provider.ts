import { inject, provideAppInitializer, Provider } from '@angular/core';
import { map, Observable, switchMap } from 'rxjs';
import { appConfigStateInitializer } from '@libs/plugins';
import { KeycloakService } from './keycloak.service';

export function provideKeycloak(): Provider {
  return [provideAppInitializer(() => keycloakInitializer())];
}

function keycloakInitializer(): Observable<boolean> {
  const keycloakService: KeycloakService = inject(KeycloakService);

  return appConfigStateInitializer().pipe(
    switchMap(() => keycloakService.initKeycloakInstance$()),
    map(() => true),
  );
}
