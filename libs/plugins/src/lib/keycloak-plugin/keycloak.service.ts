import { inject, Injectable, isDevMode, NgZone } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngxs/store';
import Keycloak, { KeycloakLoginOptions, KeycloakProfile } from 'keycloak-js';
import { from, interval, map, Observable, of, Subscription, switchMap, timer } from 'rxjs';
import { filter, tap } from 'rxjs/operators';
import { Config, ConfigState } from '@libs/plugins';
import { BrowserIdleService, removeFragmentFromUrlTree } from '@libs/core';
import { KeycloakInstance } from './keycloak.model';
import { Auth } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class KeycloakService {
  private store = inject(Store);
  private activatedRoute = inject(ActivatedRoute);
  private router = inject(Router);
  private ngZone = inject(NgZone);

  keycloakInstance: KeycloakInstance;
  tokenRefreshSub$: Subscription;
  autoLogoutTimeSub$: Subscription;

  readonly TOKEN_REFRESH_INTERVAL_TIME = 5 * 1000; // 5 seconds
  readonly TOKEN_MIN_VALIDITY_TIME = 60; // 60 seconds
  readonly AUTO_LOGOUT_TIME = 60 * 1000; // 60 seconds
  readonly IDLE_TIME = 60; // 60 seconds
  readonly browserIdleService = new BrowserIdleService(this.IDLE_TIME);

  get authenticated(): boolean {
    return this.keycloakInstance?.authenticated;
  }

  get token(): string {
    return this.keycloakInstance?.token;
  }

  constructor() {
    this.browserIdleService.listenForUserActivity();
  }

  initKeycloakInstance$(): Observable<boolean> {
    const { config, init } = this.store.selectSnapshot(ConfigState.getOne('keycloak'));
    this.keycloakInstance = new Keycloak(config);
    return from(
      this.keycloakInstance.init({
        checkLoginIframe: false,
        onLoad: 'check-sso',
        ...init,
      }),
    ).pipe(
      tap(() => this.listenRefreshToken()),
      // catchError(() => {
      //   return of(false);
      // }),
    );
  }

  login(payload: Auth.LoginOptions = {}): void {
    const loginOptions = this.buildLoginOptions(payload?.redirectUri);
    this.keycloakInstance.login({
      ...payload,
      ...loginOptions,
    });
  }

  logout(redirectTo?: string): void {
    this.keycloakInstance.logout({
      redirectUri: this.buildRedirectUri(redirectTo),
    });
  }

  loadKeycloakProfile$(): Observable<KeycloakProfile> {
    if (!this.keycloakInstance.profile) {
      return from(this.keycloakInstance.loadUserProfile()).pipe(
        map((profile) => ({
          ...profile,
        })),
      );
    }

    return of({
      ...this.keycloakInstance.profile,
    });
  }

  buildRedirectUri(redirectTo?: string): string {
    const { url }: Config.State = this.store.selectSnapshot(ConfigState.getAll);
    let redirectUri = url.app;

    if (redirectTo) {
      redirectUri += redirectTo;
    } else if (this.activatedRoute && this.router) {
      const { path } = removeFragmentFromUrlTree(
        this.router,
        this.activatedRoute.snapshot,
        this.router.routerState.snapshot,
      );
      redirectUri += path;
    }
    return redirectUri;
  }

  buildLoginOptions(redirectTo?: string): KeycloakLoginOptions {
    const { keycloak } = this.store.selectSnapshot(ConfigState.getAll);
    return {
      redirectUri: this.buildRedirectUri(redirectTo),
      idpHint: keycloak.config.idpHint,
    };
  }

  listenRefreshToken(): void {
    this.stopTokenRefreshTimer();

    this.ngZone.runOutsideAngular(() => {
      this.tokenRefreshSub$ = interval(this.TOKEN_REFRESH_INTERVAL_TIME)
        .pipe(
          switchMap(() => this.browserIdleService.isIdle$),
          filter(() => this.authenticated),
        )
        .subscribe((isIdle) => this.shouldRefreshToken(isIdle));
    });
  }

  shouldRefreshToken(isIdle: boolean): void {
    const tokenExpireTime = this.keycloakInstance.tokenParsed.exp * 1000;
    const expiryTimeInSeconds = Math.floor((tokenExpireTime - Date.now()) / 1000);
    const isTokenAboutToExpire = expiryTimeInSeconds <= this.TOKEN_MIN_VALIDITY_TIME;

    if (isTokenAboutToExpire) {
      if (isIdle && !isDevMode()) {
        this.showLogoutIdleModal();
        this.startAutoLogoutTimer();
      } else {
        this.refreshToken();
      }
    }
  }

  showLogoutIdleModal(): void {
    this.stopAutoLogoutTimer();

    console.warn('Logout modal not implemented');
  }

  startAutoLogoutTimer(): void {
    this.autoLogoutTimeSub$ = timer(this.AUTO_LOGOUT_TIME).subscribe(() => {
      this.logout();
    });
  }

  stopAutoLogoutTimer(): void {
    if (this.autoLogoutTimeSub$) {
      this.autoLogoutTimeSub$.unsubscribe();
      this.autoLogoutTimeSub$ = null;
    }
  }

  refreshToken(): void {
    this.keycloakInstance.updateToken(this.TOKEN_MIN_VALIDITY_TIME).catch(() => this.login());
  }

  stopTokenRefreshTimer(): void {
    if (this.tokenRefreshSub$) {
      this.tokenRefreshSub$.unsubscribe();
      this.tokenRefreshSub$ = null;
    }
  }
}
