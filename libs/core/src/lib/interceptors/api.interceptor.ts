import { HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';
import { KeycloakService } from '@libs/plugins';
import { KnownLocalStorageKeys } from '../enums';
import { LocalStorageService } from '../services';
import { shouldSkipInterception } from '../utils';
import { Store } from '@ngxs/store';
import { CurrentState } from '@libs/bss';

function buildHeaders(request: HttpRequest<unknown>): { [name: string]: string | string[] } {
  const headers: { [name: string]: string | string[] } = {};
  const authService = inject(KeycloakService);
  const localStorageService = inject(LocalStorageService);
  const store = inject(Store);

  const token = authService.token;
  const currentLang = localStorageService.get<string>(KnownLocalStorageKeys.LANGUAGE);

  if (!request.headers.has('Authorization') && token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  if (!request.headers.has('Accept')) {
    headers['Accept'] = 'application/json';
  }

  if (!request.headers.has('Content-Type') && !(request.body instanceof FormData)) {
    headers['Content-Type'] = 'application/json';
  }

  const businessHeaders = {
    oderId:  store.selectSnapshot(CurrentState.currentCustomerOrderId),
    businessFlowSpecShortCode: store.selectSnapshot(CurrentState.currentFlow),
  }

  headers['baggage'] = currentLang || 'en';


  headers['Collation'] = currentLang || 'en';

  return headers;
}

export const ApiInterceptor: HttpInterceptorFn = (request, next) => {
  if (shouldSkipInterception(request)) {
    return next(request);
  }

  const setHeaders = buildHeaders(request);

  return next(
    request.clone({
      setHeaders,
    }),
  );
};
