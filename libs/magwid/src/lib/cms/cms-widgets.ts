import {
  CmsAddOnSelectionMagicComponent,
  CmsCtaSupportMagicComponent,
  CmsDeviceDetailMagicComponent,
  CmsFaqMagicComponent,
  CmsGreetingMagicComponent,
  CmsGreetingWithBackMagicComponent,
  CmsHomeNavigationBarMagicComponent,
  CmsLatestArticlesMagicComponent,
  CmsMixAndMatchMagicComponent,
  CmsPlanCatalogDetailMagicComponent,
  CmsPlanCatalogMagicComponent,
  CmsPlanWithDeviceOptionsMagicComponent,
  CmsSubOffersMagicComponent,
  CmsTopOffersMagicComponent,
  ErrorMagicComponent,
  SearchMagicComponent,
} from '@libs/magwid';
import { createCmsWidget } from './create-cms-widget';
import { HomeBannerComponent, CmsBannerComponent, CmsFeaturesStripComponent } from '@libs/widgets';
import {
  CmsBannerComponentMapper,
  CmsFeaturesStripComponentMapper,
  CmsHomeBannerComponentMapper,
} from './cms-component-mappers';

export const CMS_WIDGETS = [
  createCmsWidget('widget-cms-plan-catalog-detail', CmsPlanCatalogDetailMagicComponent),
  createCmsWidget('widget-cms-phone-catalog-detail', CmsDeviceDetailMagicComponent),
  createCmsWidget('widget-cms-plan-catalog', CmsPlanCatalogMagicComponent),
  createCmsWidget('widget-cms-banner', CmsBannerComponent, CmsBannerComponentMapper),
  createCmsWidget('widget-cms-features-strip', CmsFeaturesStripComponent, CmsFeaturesStripComponentMapper),
  createCmsWidget('widget-cms-sub-offers', CmsSubOffersMagicComponent),
  createCmsWidget('widget-cms-greeting', CmsGreetingMagicComponent),
  createCmsWidget('magic-widget-cms-cta-support', CmsCtaSupportMagicComponent),
  createCmsWidget('widget-cms-greeting-with-back', CmsGreetingWithBackMagicComponent),
  createCmsWidget('widget-cms-faq', CmsFaqMagicComponent),
  createCmsWidget('widget-cms-buy-with-devices', CmsPlanWithDeviceOptionsMagicComponent),
  createCmsWidget('widget-error', ErrorMagicComponent),
  createCmsWidget('widget-search', SearchMagicComponent),
  createCmsWidget('widget-cms-top-offers', CmsTopOffersMagicComponent),
  createCmsWidget('widget-cms-home-banner', HomeBannerComponent, CmsHomeBannerComponentMapper),
  createCmsWidget('widget-cms-home-navigation-bar', CmsHomeNavigationBarMagicComponent),
  createCmsWidget('widget-cms-latest-articles', CmsLatestArticlesMagicComponent),
  createCmsWidget('widget-cms-cta-support', CmsCtaSupportMagicComponent),
  createCmsWidget('widget-cms-mix-and-match', CmsMixAndMatchMagicComponent),
  createCmsWidget('widget-cms-addon-catalog', CmsAddOnSelectionMagicComponent),
];
