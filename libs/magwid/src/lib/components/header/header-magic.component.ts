import { Component, computed, model } from '@angular/core';
import { UserState } from '@libs/bss';
import { select } from '@ngxs/store';
import { ConfigState, MagicConfig } from '@libs/plugins';
import { HeaderComponent as WidgetHeaderComponent } from '@libs/widgets';
import { ProfileMenuMagicComponent } from '../profile-status/profile-menu-magic.component';
import { SearchMagicComponent } from '../search/search-magic.component';
import { LanguagePreferenceMagicComponent } from '../language-preference/language-preference-magic.component';
import { FeatureFlagDirective } from '../../directives';
import { FeatureFlagEnum } from '@libs/types';
import { loggedInUserResolver } from '@libs/magwid';

@Component({
  selector: 'magic-widget-header',
  templateUrl: './header-magic.component.html',
  imports: [
    WidgetHeaderComponent,
    ProfileMenuMagicComponent,
    SearchMagicComponent,
    LanguagePreferenceMagicComponent,
    FeatureFlagDirective,
  ],
})
@MagicConfig({
  resolve: [loggedInUserResolver],
})
export class HeaderMagicComponent {
  firstName = select(UserState.getFirstName);
  configLogo = select(ConfigState.getDeep('theme.logoNew'));
  logoSrc = computed(() => (this.configLogo() as string) || 'assets/images/logo/logo.svg');
  loggedIn = computed(() => !!this.firstName());

  languagePreferences = model<boolean>(false);
  protected readonly FeatureFlagEnum = FeatureFlagEnum;
}
