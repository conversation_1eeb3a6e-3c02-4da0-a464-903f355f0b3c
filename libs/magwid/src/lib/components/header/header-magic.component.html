<widget-header
  [loggedIn]="loggedIn()"
  [name]="firstName()"
  logoHref="#"
  [logoSrc]="logoSrc()"
  brandName="Etiyacell"
  [(languagePreferences)]="languagePreferences"
>
  <magic-widget-language-preference
    languagePreference
    [(open)]="languagePreferences"
  ></magic-widget-language-preference>
  <magic-widget-profile-menu login></magic-widget-profile-menu>
  <magic-widget-search *featureFlag="FeatureFlagEnum.search" search></magic-widget-search>
  <!--  <eds-button notification class="notifications-item" appearance="subtle" iconOnly iconLeading="notification"></eds-button>-->
</widget-header>
