import { Component, computed, effect, inject, OnDestroy, signal } from '@angular/core';
import { AccountFilterComponent, AccountSummaryCardComponent, EmptyStateComponent } from '@libs/widgets';
import { getColorByProductStatus } from '@libs/core';
import { select } from '@ngxs/store';
import { AccountState } from '@libs/bss';
import { BillingAccountInfo } from '@libs/types';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { Router } from '@angular/router';
import { accountInformationResolver } from './account-information-magic.resolver';

export interface ActiveAccountFilters {
  billingAccountId: string;
  type: string;
  status: string;
}

@Component({
  selector: 'magic-widget-account-information',
  templateUrl: './account-information-magic.component.html',
  styleUrls: ['./account-information-magic.component.scss'],
  imports: [AccountSummaryCardComponent, AccountFilterComponent, EmptyStateComponent, TranslatePipe],
})
@MagicConfig({
  resolve: [accountInformationResolver],
})
export class AccountInformationMagicComponent implements OnDestroy {
  private router = inject(Router);
  protected readonly getColorByProductStatus = getColorByProductStatus;

  billingAccounts = select(AccountState.billingAccountInfos);

  parentBillingAccountIds: string[] = [];

  isFilterVisible = signal(true);

  activeFilters = signal<ActiveAccountFilters>({
    billingAccountId: '',
    type: '',
    status: '',
  });

  filteredAccounts = computed(() => {
    const allAccounts = this.billingAccounts();
    const filters = this.activeFilters();

    if (!filters.billingAccountId && !filters.type && !filters.status) {
      return allAccounts;
    }

    return allAccounts.filter((account) => {
      const billingAccountMatch =
        !filters.billingAccountId || account.billingAccountId.toString() === filters.billingAccountId;
      const typeMatch = !filters.type || account.paymentType?.shortCode === filters.type;
      const statusMatch = !filters.status || account.accountStatus?.shortCode === filters.status;

      return billingAccountMatch && typeMatch && statusMatch;
    });
  });

  constructor() {
    effect(() => {
      const currentAccounts = this.billingAccounts();

      if (currentAccounts) {
        const childAccountIds = new Set<string>();
        currentAccounts.forEach((account) => {
          this.collectChildAccountIds(account, childAccountIds);
        });

        this.parentBillingAccountIds = currentAccounts
          .filter((account) => !childAccountIds.has(account.billingAccountId.toString()))
          .map((account) => account.billingAccountId.toString());
      }
    });
  }

  private collectChildAccountIds(account: BillingAccountInfo, childIds: Set<string>) {
    if (account.childBillingAccounts?.length > 0) {
      for (const childAccount of account.childBillingAccounts) {
        childIds.add(childAccount.billingAccountId.toString());
        this.collectChildAccountIds(childAccount, childIds);
      }
    }
  }

  onCardClick(billingAccountId: string) {
    const account = this.filteredAccounts().find((acc) => acc.billingAccountId.toString() === billingAccountId);
    if (account) {
      this.router.navigate(['/my/account/information', account.billingAccountId]);
    }
  }

  getSubscriptionsOrServiceAddressForAccount(account: BillingAccountInfo): BillingAccountInfo[] {
    const subscriptionsWithIdentity = new Set<BillingAccountInfo>();

    const findSubscriptionsRecursively = (currentAccount: BillingAccountInfo) => {
      if (!currentAccount) {
        return;
      }

      if (currentAccount.subscriptionIdentities?.length > 0 || currentAccount.serviceAddress) {
        subscriptionsWithIdentity.add(currentAccount);
      }

      if (currentAccount.childBillingAccounts?.length > 0) {
        for (const childAccount of currentAccount.childBillingAccounts) {
          findSubscriptionsRecursively(childAccount);
        }
      }
    };

    findSubscriptionsRecursively(account);

    const result = Array.from(subscriptionsWithIdentity);

    return result;
  }

  onFiltersChanged(selectedFilter: ActiveAccountFilters) {
    this.activeFilters.set(selectedFilter);
  }

  onButtonResetFilter() {
    this.activeFilters.set({
      billingAccountId: '',
      type: '',
      status: '',
    });

    this.isFilterVisible.set(false);
    setTimeout(() => this.isFilterVisible.set(true));
  }

  ngOnDestroy(): void {
    this.onButtonResetFilter();
  }
}
