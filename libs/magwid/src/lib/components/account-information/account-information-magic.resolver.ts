import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { AccountGetInquireBillingAccountInfos, CurrentState } from '@libs/bss';
import { Store } from '@ngxs/store';
import { Observable } from 'rxjs';

export const accountInformationResolver: ResolveFn<Observable<void>> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return store.dispatch(new AccountGetInquireBillingAccountInfos(customerId, null, true, true));
};
