<div class="account-information-magic">
  @if (isFilterVisible()) {
    <widget-account-filter
      [rawBillingAccountIds]="parentBillingAccountIds"
      [activeFilters]="activeFilters()"
      (filtersChanged)="onFiltersChanged($event)"
    >
    </widget-account-filter>
  }

  <div class="card-list">
    @for (account of filteredAccounts(); track account.billingAccountId) {
      <widget-account-summary-card
        [title]="account.accountName || ''"
        [details]="[
          {
            label: 'billingAccId',
            value: account.billingAccountId.toString() || '',
          },
          { label: 'accountType', value: account.paymentType?.name || '' },
        ]"
        [statusText]="account.accountStatus?.name"
        [statusAppearance]="account.accountStatus?.shortCode"
        [subscriptions]="getSubscriptionsOrServiceAddressForAccount(account)"
        (cardClick)="onCardClick($event)"
      >
      </widget-account-summary-card>
    } @empty {
      <widget-empty-state
        [text]="'noInvoiceFound' | translate"
        [icons]="[]"
        buttonText="resetFilter"
        (buttonAction)="onButtonResetFilter()"
      ></widget-empty-state>
    }
  </div>
</div>
