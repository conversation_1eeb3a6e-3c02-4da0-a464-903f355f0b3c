import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';
import { CurrentState, InquireBillingAccountListAction } from '@libs/bss';

export const billingListResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: false,
      action: () => {
        return new InquireBillingAccountListAction({
          customerId,
          accountType: 'BILL_ACCT',
          offset: 0,
          limit: 6,
        });
      },
    },
  ];
};
