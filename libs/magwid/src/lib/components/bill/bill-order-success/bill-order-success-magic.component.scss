.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--eds-spacing-600);
}

.success-desc {
  color: var(--eds-colors-text-default);
}

.success-title {
  color: var(--eds-colors-text-dark);
}

.success-section {
  width: 100%;
}

.alert {
  &::part(wrapper) {
    align-items: center;
  }

  &::part(icon) {
    width: var(--eds-sizing-400);
    height: var(--eds-sizing-400);
  }

  &::part(title) {
    --eds-font-size-heading-sm: var(--eds-font-size-body-md);
    --eds-heading-font-weight: regular;
  }

  &::part(description) {
    padding-inline-start: 24px;
    --eds-font-size-heading-sm: var(--eds-font-size-body-md);
    --eds-heading-font-weight: regular;
  }
}

