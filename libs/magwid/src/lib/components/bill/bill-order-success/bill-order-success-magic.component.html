<eds-card class="success-card">
  <div class="success-content">
    <widget-success-icon></widget-success-icon>
    <eds-heading class="success-title" as="h2" size="xl" [text]="'orderSuccessTitle' | translate"></eds-heading>

    <eds-text
      class="success-desc"
      size="lg"
      weight="regular"
      align="center"
      [text]="'orderSuccessSubtitle' | translate"
    ></eds-text>

    <div class="success-section">
      <eds-heading
        as="h4"
        class="success-title"
        size="md"
        weight="medium"
        [text]="'paidBillsSummary' | translate"
      ></eds-heading>
    </div>
    <div class="success-section">
      <magic-widget-bill-card
        [billingAccountId]="currentBillingAccountId()"
        [invoiceId]="currentInvoiceId()"
        [showAccountName]="true"
        [isShowAction]="false"
      ></magic-widget-bill-card>
    </div>
    <div class="success-section">
      <magic-widget-bill-card
        [billingAccountId]="currentBillingAccountId()"
        [isShowAction]="false"
        [isShowTotalBalance]="true"
        [paymentAmountPrice]="paymentAmountPrice()"
      ></magic-widget-bill-card>
    </div>
    @if (alertDetail() && alertDetail()?.title) {
      <div class="success-section">
        <eds-alert
          showIcon="true"
          [iconName]="alertDetail().iconName"
          [title]="alertDetail().title | translate"
          [description]="alertDetail().description | translate"
          [appearance]="alertDetail().appearance"
          class="alert"
        ></eds-alert>
      </div>
    }
    <div class="success-section">
      <eds-card>
        <eds-data-list
          [itemsSize]="orderDetails().itemsSize"
          [trim]="orderDetails().trim"
          [expandedText]="orderDetails().expandedText"
          [unexpandedText]="orderDetails().unexpandedText"
          [items]="orderDetails().items"
        >
        </eds-data-list>
      </eds-card>
    </div>
    @if (isShowDownloadPdf()) {
      <div class="success-section">
        <eds-button (click)="(downloadPdf)" appearance="default" shouldFitContainer>
          {{ 'downloadPDF' | translate }}
        </eds-button>
      </div>
    }

    <div class="success-section">
      <eds-button (click)="goToBills()" appearance="secondary" shouldFitContainer>
        {{ 'goToBills' | translate }}
      </eds-button>
    </div>
  </div>
</eds-card>
