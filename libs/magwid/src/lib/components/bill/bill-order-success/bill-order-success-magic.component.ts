import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, signal } from '@angular/core';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { DatePipe } from '@angular/common';
import { DataList, SuccessIconComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { CurrentState, InvoiceState, QuoteState } from '@libs/bss';
import { Router } from '@angular/router';
import { DEFAULT_DATE_FORMAT } from '@libs/core';
import { BillCardMagicComponent } from '../bill-card/bill-card-magic.component';
import { CurrentBillOrderResolver } from '../../../resolvers';
import { Invoice } from '@libs/types';
import { useAmountAlert } from '@libs/magwid';

@Component({
  selector: 'magic-widget-bill-order-success',
  templateUrl: './bill-order-success-magic.component.html',
  styleUrls: ['./bill-order-success-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [SuccessIconComponent, TranslatePipe, BillCardMagicComponent],
  providers: [DatePipe],
})
@MagicConfig({
  resolve: [CurrentBillOrderResolver],
})
export class BillOrderSuccessMagicComponent {
  private store = inject(Store);
  private datePipe = inject(DatePipe);
  private router = inject(Router);

  billingAccountId = signal(this.store.selectSnapshot(CurrentState.currentBillingAccountId));
  invoiceId = signal(this.store.selectSnapshot(CurrentState.currentInvoiceId));

  bills = select(InvoiceState.invoicingBillingAccounts);
  bill = computed(() => {
    return this.bills()?.findBill(this.billingAccountId(), this.invoiceId());
  });

  quote = select(QuoteState.quote);
  payBillAmount = computed(() => {
    return this.quote().payBillAmountValue;
  });

  paymentAmountPrice = computed<Invoice.InvoiceExternalPrice>(() => {
    return {
      title: 'paymentAmount',
      amount: this.quote().payBillAmountValue,
    };
  });

  openAmount = computed(() => {
    return this.bills()?.openAmount(this.billingAccountId(), this.invoiceId());
  });

  totalOpenAmount = computed(() => {
    return this.bills()?.totalOpenAmount(this.billingAccountId());
  });

  orderDetails = computed<DataList>(() => {
    return {
      itemsSize: 5,
      trim: true,
      expandedText: '',
      unexpandedText: '',
      items: [
        {
          key: 'Order ID',
          value: '#' + this.quote().quote.customerOrderId.toString(),
        },
        {
          key: 'Payment Date',
          value: this.datePipe.transform(this.quote().quote?.submitDate || new Date(), DEFAULT_DATE_FORMAT),
        },
      ],
    };
  });

  currentBillingAccountId = signal(this.store.selectSnapshot(CurrentState.currentBillingAccountId));
  currentInvoiceId = signal(this.store.selectSnapshot(CurrentState.currentInvoiceId));
  isShowDownloadPdf = signal(false);

  private amountAlert = useAmountAlert({
    amount: () => this.payBillAmount(),
    openAmount: () => this.openAmount(),
    totalOpenAmount: () => this.totalOpenAmount(),
    hasInvoiceId: () => !!this.invoiceId(),
  });

  alertDetail = computed(() => this.amountAlert.alertDetail());

  downloadPdf() {}

  goToBills() {
    this.router.navigate([`/my/bills`]);
  }
}
