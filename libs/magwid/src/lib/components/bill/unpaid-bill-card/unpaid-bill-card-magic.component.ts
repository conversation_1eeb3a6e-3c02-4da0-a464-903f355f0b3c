import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';
import { select } from '@ngxs/store';
import { AccountState, CustomerState, InvoiceState } from '@libs/bss';
import { UnpaidBillCardComponent } from '@libs/widgets';
import { Invoice } from '@libs/types';

@Component({
  selector: 'magic-widget-unpaid-bill-card',
  templateUrl: './unpaid-bill-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [UnpaidBillCardComponent],
})
export class UnpaidBillCardMagicComponent {
  billingAccountId = input<number>();

  payNow = output<void>();

  payClick = output<number>();

  downloadClick = output<Invoice.InvoiceDefinitionType>();

  financialInfo = select(CustomerState.getFinancialInfo);

  billingAccounts = select(AccountState.billingAccounts);

  bills = select(InvoiceState.invoicingBillingAccounts);

  billingAccount = computed(() => {
    return this.billingAccounts()?.findBillingAccountByAccountId(this.billingAccountId());
  });

  billCurrencyCode = computed(() => {
    return this.bills().billCurrencyCode;
  });

  unpaidBills = computed(() => {
    return this.bills().unpaidInvoices(this.billingAccountId());
  });

  totalOpenAmount = computed(() => {
    return this.bills()?.totalOpenAmount(this.billingAccountId());
  });
}
