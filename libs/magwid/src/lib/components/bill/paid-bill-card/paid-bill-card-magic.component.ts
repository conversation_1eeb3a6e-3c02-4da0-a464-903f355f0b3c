import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';
import { select } from '@ngxs/store';
import { InvoiceState } from '@libs/bss';
import { PaidBillCardComponent } from '@libs/widgets';
import { Invoice } from '@libs/types';

@Component({
  selector: 'magic-widget-paid-bill-card',
  templateUrl: './paid-bill-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [PaidBillCardComponent],
})
export class PaidBillCardMagicComponent {
  billingAccountId = input<number>();

  downloadClick = output<Invoice.InvoiceDefinitionType>();

  bills = select(InvoiceState.invoicingBillingAccounts);

  paidBills = computed(() => {
    return this.bills().paidInvoices(this.billingAccountId());
  });

  handleDownloadClick(invoiceDefinition: Invoice.InvoiceDefinitionType) {
    this.downloadClick.emit(invoiceDefinition);
  }
}
