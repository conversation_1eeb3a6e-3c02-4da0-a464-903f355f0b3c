<eds-card class="bill-amount-card">
  <div class="sim-card-selection">
    <widget-enter-bill-amount
      (onPayBillAmountChange)="onPayBillAmountChange($event)"
      [form]="payBillAmountForm()"
    ></widget-enter-bill-amount>

    @if (selectedPayBillMethod() === PayAmountEnum.PARTIAL && paymentAmountPrice()?.amount > 0) {
      <magic-widget-bill-card
        [isShowTotalBalance]="true"
        [showAccountName]="false"
        [isShowAction]="false"
        [paymentAmountPrice]="paymentAmountPrice()"
      ></magic-widget-bill-card>
      <eds-alert
        showIcon="true"
        [iconName]="alertDetail().iconName"
        [title]="alertDetail().title | translate"
        [description]="alertDetail().description | translate"
        [appearance]="alertDetail().appearance"
        class="alert"
      ></eds-alert>
    }
  </div>
</eds-card>
