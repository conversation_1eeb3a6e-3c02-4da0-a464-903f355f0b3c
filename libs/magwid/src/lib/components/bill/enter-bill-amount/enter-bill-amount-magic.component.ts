import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  input,
  model,
  signal,
} from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { InvoiceState, QuoteState } from '@libs/bss';
import { BSSValidators, REGEX_DOUBLE, ToasterService, TranslatePipe, TranslateService } from '@libs/plugins';
import { Invoice, PayAmountEnum } from '@libs/types';
import { Alert, EnterBillAmountComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { BillCardMagicComponent } from '@libs/magwid';

@Component({
  selector: 'magic-widget-enter-bill-amount',
  templateUrl: './enter-bill-amount-magic.component.html',
  styleUrl: './enter-bill-amount-magic.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [EnterBillAmountComponent, TranslatePipe, BillCardMagicComponent],
})
export class EnterBillAmountMagicComponent {
  protected store = inject(Store);
  protected toasterService = inject(ToasterService);
  protected translateService = inject(TranslateService);

  billingAccountId = input<number>(null);

  invoiceId = input<number>(null);

  validatedPayBillAmount = model<string>(null);
  selectedPayBillMethod = input<PayAmountEnum>(null);

  quote = select(QuoteState.quote);

  bills = select(InvoiceState.invoicingBillingAccounts);
  bill = computed(() => {
    return this.bills()?.findBill(this.billingAccountId(), this.invoiceId());
  });

  openAmount = computed(() => {
    return this.bills()?.openAmount(this.billingAccountId(), this.invoiceId());
  });

  totalOpenAmount = computed(() => {
    return this.bills()?.totalOpenAmount(this.billingAccountId());
  });

  payBillAmountForm = computed(() => {
    return new FormGroup({
      payBillAmount: new FormControl(null, [
        Validators.required,
        BSSValidators.noEmoji,
        BSSValidators.regex(REGEX_DOUBLE, 'invalidFormat'),
      ]),
    });
  });

  alertDetail = signal<Alert>({});
  paymentAmountPrice = signal<Invoice.InvoiceExternalPrice>(null);

  onPayBillAmountChange(formValue: string = null) {
    const payBillAmount = formValue ? +formValue : 0;
    if (!this.payBillAmountForm().valid) {
      this.validatedPayBillAmount.set(null);
    }

    this.validatedPayBillAmount.set(formValue);
    this.handlePartialPaymentAmount(payBillAmount);

    if (this.isAmountUnderOpenAmount(payBillAmount, this.openAmount(), this.totalOpenAmount())) {
      this.handleUnderAmount();
    } else {
      this.handleOverAmount();
    }
  }

  private handlePartialPaymentAmount(payBillAmount: number): void {
    this.paymentAmountPrice.set({
      title: 'specifiedAmountPay',
      amount: payBillAmount,
      currencyCode: this.bill()?.currencyCode,
    });
  }

  private isAmountUnderOpenAmount(amount: number, openAmount: number, totalOpenAmount?: number): boolean {
    return this.invoiceId() ? amount <= openAmount : amount <= totalOpenAmount;
  }

  private handleUnderAmount(): void {
    this.alertDetail.set({
      appearance: 'info',
      iconName: 'informationCircle',
      title: 'underAmountInfoTitle',
      description: 'underAmountInfoDescription',
    });
  }

  private handleOverAmount(): void {
    this.alertDetail.set({
      appearance: 'error',
      iconName: 'alertCircle',
      title: 'overAmountWarningTitle',
      description: 'overAmountWarningDescription',
    });
  }

  protected readonly PayAmountEnum = PayAmountEnum;
}
