.bill-amount-card {
  --eds-card-padding: var(--eds-spacing-400);
  --eds-card-border-radius: var(--eds-radius-300);
  --eds-card-border: 1px solid var(--eds-border-color-default);
}
.sim-card-selection {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);

  @media (min-width: 834px) {
    gap: var(--eds-spacing-600);
  }
}

.alert {
  &::part(wrapper) {
    align-items: center;
  }

  &::part(icon) {
    width: var(--eds-sizing-400);
    height: var(--eds-sizing-400);
  }

  &::part(title) {
    --eds-font-size-heading-sm: var(--eds-font-size-body-md);
    --eds-heading-font-weight: regular;
  }

  &::part(description) {
    padding-inline-start: 24px;
    --eds-font-size-heading-sm: var(--eds-font-size-body-md);
    --eds-heading-font-weight: regular;
  }
}

