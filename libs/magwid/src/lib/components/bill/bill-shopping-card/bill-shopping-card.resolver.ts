import { MagicResolverModel } from '@libs/plugins';
import { ResolveFn } from '@angular/router';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import { CurrentState, GetInvoicingBillingAccount, QuoteGetQuoteAction } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';

export const billShoppingCardResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);

  return [
    {
      selector: false,
      action: () => {
        return [
          new QuoteGetQuoteAction({
            customerOrderId,
            currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.BILL_PAYMENT,
          }),
        ];
      },
      next: [
        {
          selector: false,
          action: () => {
            return new GetInvoicingBillingAccount({
              billingAccountId: store.selectSnapshot(CurrentState.currentBillingAccountId),
              ...(store.selectSnapshot(CurrentState.currentInvoiceId) && {
                invoiceNumber: store.selectSnapshot(CurrentState.currentInvoiceId),
              }),
            });
          },
        },
      ],
    },
  ];
};
