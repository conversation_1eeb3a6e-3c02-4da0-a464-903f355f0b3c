import { ChangeDetectionStrategy, Component, computed, effect, inject, input, output, signal } from '@angular/core';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import { ShoppingCardComponent, ShoppingCardItem, ShoppingCardTotal, SummaryCardComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { BusinessWorkflowStepService, InvoiceState, QuoteState } from '@libs/bss';
import { CurrencyPipe } from '@angular/common';
import { eBusinessFlow } from '@libs/types';
import { billShoppingCardResolver } from './bill-shopping-card.resolver';

@Component({
  selector: 'magic-widget-bill-shopping-card',
  imports: [ShoppingCardComponent, SummaryCardComponent, TranslatePipe],
  templateUrl: './bill-shopping-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [CurrencyPipe],
})
@MagicConfig({
  resolve: [billShoppingCardResolver],
})
export class BillShoppingCardMagicComponent {
  private store = inject(Store);
  private translateService = inject(TranslateService);
  private currencyPipe = inject(CurrencyPipe);
  protected businessWorkflowStepService = inject(BusinessWorkflowStepService);

  quote = select(QuoteState.quote);
  bills = select(InvoiceState.invoicingBillingAccounts);
  billCurrencyCode = computed(() => {
    return this.bills().billCurrencyCode;
  });

  payBillAmount = computed(() => {
    return this.quote().payBillAmountValue ?? 0;
  });

  totalAmount = computed(() => {
    return this.payBillAmount();
  });

  actionButtonText = input<string>(this.translateService.translate('completeOrder'));

  actionButtonDisabled = signal<boolean>(true);

  onAction = output();

  onButtonClick() {
    this.onAction.emit();
  }

  items = computed(() => {
    return this.buildItems([
      {
        name: 'paymentAmount',
        amount: this.payBillAmount(),
        class: '',
      },
    ]);
  });

  total = computed(
    () =>
      ({
        key: this.translateService.translate('totalAmount'),
        amount: this.currencyPipe.transform(this.totalAmount(), this.billCurrencyCode()),
      }) as ShoppingCardTotal,
  );

  constructor() {
    effect(() => {
      this.handleDisabledStatus();
    });
  }

  handleDisabledStatus() {
    const activeStep = this.businessWorkflowStepService.activeStep()?.shortCode;
    if (
      activeStep &&
      !this.businessWorkflowStepService.isInActiveStep([eBusinessFlow.WorkflowStateType.BILL_PAYMENT])
    ) {
      this.actionButtonDisabled.set(true);
    }
    this.actionButtonDisabled.set(this.totalAmount() <= 0);
  }

  buildItems(items: ShoppingCardItem[]): ShoppingCardItem[] {
    return items.map((data) => ({
      name: this.translateService.translate(data.name),
      amount: this.currencyPipe.transform(data.amount, this.billCurrencyCode()),
      class: data.class,
    }));
  }
}
