<div class="sim-card-selection">
  <widget-titled-section [sectionTitle]="'selectBillPaymentAmount' | translate">
    <eds-radio-group name="payAmount" (radio-change)="valueChange($event)">
      @for (char of selectionOptions(); track $index) {
        <eds-radio
          [id]="char.id"
          [value]="char.value"
          [name]="char.name"
          [isChecked]="char.isChecked"
          [isDisabled]="char.isDisabled"
        >
          <label [for]="char.id">
            <div>{{ char.label | translate }}</div>
          </label>
        </eds-radio>
      }
    </eds-radio-group>
  </widget-titled-section>
  @if (selectedBillPaymentSelection() === PayAmountEnum.FULL) {
    <magic-widget-bill-card
      [billingAccountId]="billingAccountId()"
      [invoiceId]="invoiceId()"
      [isShowAction]="false"
      [isShowTotalBalance]="true"
      [(validatedPayBillAmount)]="validatedNumber"
    ></magic-widget-bill-card>
  }
  @if (selectedBillPaymentSelection() === PayAmountEnum.PARTIAL) {
    <magic-widget-enter-bill-amount
      [billingAccountId]="billingAccountId()"
      [invoiceId]="invoiceId()"
      [selectedPayBillMethod]="selectedBillPaymentSelection()"
      [(validatedPayBillAmount)]="validatedNumber"
    ></magic-widget-enter-bill-amount>
  }
</div>
