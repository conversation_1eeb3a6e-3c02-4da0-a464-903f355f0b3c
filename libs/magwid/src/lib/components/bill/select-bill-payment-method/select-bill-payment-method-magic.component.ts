import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  input,
  model,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslatePipe } from '@libs/plugins';
import { PayAmountEnum } from '@libs/types';
import { Radio, TitledSectionComponent } from '@libs/widgets';
import { BillCardMagicComponent } from '../bill-card/bill-card-magic.component';
import { EnterBillAmountMagicComponent } from '../enter-bill-amount/enter-bill-amount-magic.component';

@Component({
  selector: 'magic-widget-select-bill-payment-method',
  imports: [TitledSectionComponent, FormsModule, TranslatePipe, BillCardMagicComponent, EnterBillAmountMagicComponent],
  templateUrl: './select-bill-payment-method-magic.component.html',
  styleUrl: './select-bill-payment-method-magic.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SelectBillPaymentMethodMagicComponent {
  billingAccountId = input<number>(null);

  invoiceId = input<number>(null);

  selectionOptions = input<Radio[]>([]);

  validatedNumber = model<string>(null);

  selectedBillPaymentSelection = signal<PayAmountEnum>(null);

  constructor() {
    effect(() => {
      const selected = this.selectionOptions().find((item) => item.isChecked);
      if (!selected) {
        return;
      }
      this.selectedBillPaymentSelection.set(selected?.value as PayAmountEnum);
    });
  }

  valueChange($event: Event) {
    const eventTargetValue = ($event.target as HTMLInputElement)?.value;
    if (!eventTargetValue) {
      return;
    }

    const selectedRadio = this.selectionOptions().find((radio) => radio.value === eventTargetValue);

    if (selectedRadio && this.selectedBillPaymentSelection() !== selectedRadio.value) {
      selectedRadio.data = null;
      this.validatedNumber.set(null);
      this.selectedBillPaymentSelection.set(selectedRadio.value as PayAmountEnum);
    }
  }

  protected readonly PayAmountEnum = PayAmountEnum;
}
