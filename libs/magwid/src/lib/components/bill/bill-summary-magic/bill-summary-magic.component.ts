import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, signal } from '@angular/core';
import { BillSummaryComponent } from '@libs/widgets';
import { MagicConfig } from '@libs/plugins';
import { select, Store } from '@ngxs/store';
import { CurrentState, InvoiceState, QuoteState } from '@libs/bss';
import { BillCardMagicComponent } from '../bill-card/bill-card-magic.component';
import { PaymentSelectedMethodsMagicComponent } from '../../checkout';
import { CurrentBillOrderResolver } from '../../../resolvers';
import { Invoice } from '@libs/types';
import { useAmountAlert } from '@libs/magwid';

@Component({
  selector: 'magic-widget-bill-summary',
  templateUrl: './bill-summary-magic.component.html',
  styleUrl: './bill-summary-magic.component.scss',
  imports: [BillSummaryComponent, BillCardMagicComponent, PaymentSelectedMethodsMagicComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [CurrentBillOrderResolver],
})
export class BillSummaryMagicComponent {
  private store = inject(Store);

  billingAccountId = signal<number>(this.store.selectSnapshot(CurrentState.currentBillingAccountId));
  invoiceId = signal<number>(this.store.selectSnapshot(CurrentState.currentInvoiceId));

  bills = select(InvoiceState.invoicingBillingAccounts);
  bill = computed(() => {
    return this.bills()?.findBill(this.billingAccountId(), this.invoiceId());
  });
  billCurrencyCode = computed(() => {
    return this.bills().billCurrencyCode;
  });

  quote = select(QuoteState.quote);
  payBillAmount = computed(() => {
    return this.quote().payBillAmountValue;
  });

  paymentAmountPrice = computed<Invoice.InvoiceExternalPrice>(() => {
    return {
      title: 'paymentAmount',
      amount: this.quote().payBillAmountValue,
    };
  });

  openAmount = computed(() => {
    return this.bills()?.openAmount(this.billingAccountId(), this.invoiceId());
  });

  totalOpenAmount = computed(() => {
    return this.bills()?.totalOpenAmount(this.billingAccountId());
  });

  private amountAlert = useAmountAlert({
    amount: () => this.payBillAmount() || null,
    openAmount: () => this.openAmount(),
    totalOpenAmount: () => this.totalOpenAmount(),
    hasInvoiceId: () => !!this.invoiceId(),
  });

  alertDetail = computed(() => this.amountAlert.alertDetail());
}
