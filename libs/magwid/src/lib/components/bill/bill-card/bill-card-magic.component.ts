import { ChangeDetectionStrategy, Component, computed, effect, inject, input, model, output } from '@angular/core';
import { select } from '@ngxs/store';
import { AccountState, InvoiceState } from '@libs/bss';
import { BillCardComponent } from '@libs/widgets';
import { Invoice } from '@libs/types';
import { MagicConfig, TranslateService } from '@libs/plugins';
import { billingListResolver } from '../list/billing-list.resolver';

@Component({
  selector: 'magic-widget-bill-card',
  templateUrl: './bill-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [BillCardComponent],
})
@MagicConfig({
  resolve: [billingListResolver],
})
export class BillCardMagicComponent {
  translate = inject(TranslateService);

  validatedPayBillAmount = model<string>(null);

  billingAccountId = input<number>(null);

  invoiceId = input<number>(null);

  showAccountName = input<boolean>(false);

  isShowAction = input<boolean>(true);

  isShowTotalBalance = input<boolean>(false);

  paymentAmountPrice = input<Invoice.InvoiceExternalPrice>(null);

  downloadClick = output<number>();

  payClick = output<number>();

  bills = select(InvoiceState.invoicingBillingAccounts);
  bill = computed(() => {
    return this.bills()?.findBill(this.billingAccountId(), this.invoiceId());
  });

  billingAccounts = select(AccountState.billingAccounts);
  accountName = computed(() => {
    return (
      (this.showAccountName() &&
        this.billingAccounts()?.findBillingAccountByAccountId(this.billingAccountId())?.accountName) ||
      null
    );
  });

  billPrice = computed(() => {
    let amount = this.invoiceId()
      ? this.bills()?.openAmount(this.billingAccountId(), this.invoiceId())
      : this.bills()?.totalOpenAmount(this.billingAccountId());

    switch (true) {
      case !!this.paymentAmountPrice()?.amount:
        return {
          title: this.translate.translate(this.paymentAmountPrice().title),
          amount: this.paymentAmountPrice().amount,
          currencyCode: this.paymentAmountPrice().currencyCode ?? this.bills().billCurrencyCode,
        } as Invoice.InvoiceExternalPrice;
      case this.isShowTotalBalance():
        return {
          title: this.translate.translate('billTotalAmount'),
          amount,
          currencyCode: this.bills().billCurrencyCode,
        } as Invoice.InvoiceExternalPrice;
      case !!this.invoiceId():
        return {
          title: this.translate.translate('openAmount'),
          amount,
          currencyCode: this.bills().billCurrencyCode,
        } as Invoice.InvoiceExternalPrice;
      default:
        return {
          title: this.translate.translate('payTotalAmount'),
          amount,
          currencyCode: this.bills().billCurrencyCode,
        } as Invoice.InvoiceExternalPrice;
    }
  });

  constructor() {
    effect(() => {
      if (this.billPrice()?.amount) {
        this.validatedPayBillAmount.set(this.billPrice()?.amount?.toString());
      }
    });
  }
}
