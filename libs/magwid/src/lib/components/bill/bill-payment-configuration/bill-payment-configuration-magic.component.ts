import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, signal } from '@angular/core';
import { BusinessWorkflowStepService, CurrentState, InvoiceState, QuoteService, QuoteState } from '@libs/bss';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { eBusinessFlow, PayAmountEnum } from '@libs/types';
import { Radio, SectionComponent, SeperatedComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { switchMap } from 'rxjs';
import { BillPaymentConfigurationResolver } from './bill-payment-configuration.resolver';
import {
  BillCardMagicComponent,
  PaymentMethodMagicComponent,
  SelectBillPaymentMethodMagicComponent,
} from '@libs/magwid';

@Component({
  selector: 'magic-widget-bill-payment-configuration',
  imports: [
    TranslatePipe,
    SectionComponent,
    BillCardMagicComponent,
    SeperatedComponent,
    SelectBillPaymentMethodMagicComponent,
    PaymentMethodMagicComponent,
  ],
  templateUrl: './bill-payment-configuration-magic.component.html',
  styleUrls: ['./bill-payment-configuration-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [BillPaymentConfigurationResolver],
})
export class BillPaymentConfigurationMagicComponent {
  private store = inject(Store);
  private quoteService = inject(QuoteService);
  private businessWorkflowStepService = inject(BusinessWorkflowStepService);

  quote = select(QuoteState.quote);

  bills = select(InvoiceState.invoicingBillingAccounts);
  currentBillingAccountId = signal(this.store.selectSnapshot(CurrentState.currentBillingAccountId));
  currentInvoiceId = signal(this.store.selectSnapshot(CurrentState.currentInvoiceId));

  validatedNumber = signal<string>(null);
  selectedPaymentMethod = signal<number>(null);

  completed = computed(() => {
    return this.validatedNumber() !== null && Number(this.validatedNumber()) > 0;
  });

  payAmountSelectionChars = computed<Radio[]>(() => {
    const createRadioOption = (type: PayAmountEnum, isChecked: boolean, data: number): Radio => ({
      id: type,
      value: type,
      name: type,
      label: type === PayAmountEnum.FULL ? 'payFullAmount' : 'payPartialAmount',
      isChecked,
      data,
      isDisabled: false,
    });

    return [createRadioOption(PayAmountEnum.FULL, true, 1), createRadioOption(PayAmountEnum.PARTIAL, false, 2)];
  });

  next() {
    this.validatedNumber.set(null);
  }

  onContinue() {
    this.businessWorkflowStepService.nextStep();
  }

  handleSelectPaymentMethod(paymentMethod: { id: number }) {
    this.selectedPaymentMethod.set(paymentMethod.id);
  }

  saveAndContinue() {
    this.quoteService
      .updatePayBillAmount$({
        nextState: eBusinessFlow.WorkflowStateType.ORDER_SUMMARY,
        paymentReferencePaymentId: this.selectedPaymentMethod(),
        payBillAmount: this.validatedNumber(),
        invoiceNumber: this.currentInvoiceId(),
      })
      .pipe(switchMap(() => this.quoteService.inquireQuote(eBusinessFlow.WorkflowStateType.ORDER_SUMMARY)))
      .subscribe(() => {
        this.onContinue();
      });
  }
}
