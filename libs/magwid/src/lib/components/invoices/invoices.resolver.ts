import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';
import { CurrentState, GetCustomerLastInvoicesAction, InvoiceState } from '@libs/bss';

export const invoicesCardResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);
  return [
    {
      selector: store.selectSnapshot(InvoiceState.customerLastInvoices)?.invoices?.length,
      action: () => {
        if (!customerId) {
          return [];
        }
        return new GetCustomerLastInvoicesAction(customerId);
      },
    },
  ];
};
