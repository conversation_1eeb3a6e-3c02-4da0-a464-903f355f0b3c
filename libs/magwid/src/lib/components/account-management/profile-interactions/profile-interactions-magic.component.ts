import { Component, inject, input } from '@angular/core';
import { TranslateService } from '@libs/plugins';
import { Interaction, InteractionListCardComponent } from '@libs/widgets';
import { Router } from '@angular/router';

@Component({
  selector: 'magic-widget-profile-interactions',
  templateUrl: './profile-interactions-magic.component.html',
  imports: [InteractionListCardComponent],
})
export class ProfileInteractionsMagicComponent {
  private translateService = inject(TranslateService);
  private router = inject(Router);

  titleText = input<string>(this.translateService.translate('profile'));

  profileInteractions = input<Interaction[]>([
    {
      text: this.translateService.translate('personalInformation'),
      onClick: () => {
        this.router.navigate(['/my/account/personal-information']);
      },
    },
  ]);
}
