import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { Router } from '@angular/router';
import { CustomerState, UpdateCustomerUsernameAction, UserState } from '@libs/bss';
import { injectDestroy, StartLoginAction } from '@libs/core';
import { loggedInUserResolver } from '@libs/magwid';
import { MagicConfig, ToasterService, TranslatePipe, TranslateService } from '@libs/plugins';
import { UpdateMyEmailFormComponent, UpdateMyEmailFormFields } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { switchMap, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'magic-widget-update-my-email',
  templateUrl: './update-my-email-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe, UpdateMyEmailFormComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [loggedInUserResolver],
})
export class UpdateMyEmailMagicComponent {
  private store = inject(Store);
  private destroy$ = injectDestroy();
  private toasterService = inject(ToasterService);
  private translateService = inject(TranslateService);
  private router = inject(Router);

  user = select(UserState.getLoggedInUser);

  existingEmail = computed(() => {
    return this.user()?.username;
  });

  updateEmail(formFields: UpdateMyEmailFormFields) {
    const request = {
      custId: this.user().customerId,
      username: formFields.newEmail,
    };

    this.store
      .dispatch(new UpdateCustomerUsernameAction(request))
      .pipe(
        takeUntil(this.destroy$),
        switchMap(() => this.store.selectOnce(CustomerState.updatedCustomerUsername)),
      )
      .subscribe((result) => {
        if (result) {
          this.router.navigate(['/my/account/personal-information']).then(() => {
            this.toasterService.success({
              title: this.translateService.translate('toast.SUCCESS_TITLE'),
              description: this.translateService.translate('updateEmailSuccess'),
            });
          });
        }
      });
  }

  goToLogin() {
    this.store.dispatch(
      new StartLoginAction({
        redirectUri: '/my/account/personal-information',
      }),
    );
  }
}
