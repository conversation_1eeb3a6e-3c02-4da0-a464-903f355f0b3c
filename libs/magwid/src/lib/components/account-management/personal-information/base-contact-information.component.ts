import { computed, Directive, inject, OnInit, Signal } from '@angular/core';
import { ContactMediumType, eBusinessFlow, eCommon } from '@libs/types';
import {
  CommunicationPreferencesState,
  CreateCustomerContactMediumAction,
  CurrentState,
  CustomerProfileService,
  DeleteCustomerContactMediumAction,
  GetCommunicationPreferencesAction,
  GetCustomerContactMediumConsentAction,
  InquireCustomerContactMediumAction,
  LovState,
  UpdateCustomerContactMediumAction,
} from '@libs/bss';
import { Observable, takeUntil } from 'rxjs';
import { ModalService, ModalSize, ToasterService, TranslateService } from '@libs/plugins';
import { ManageContactInfoModalComponent } from '../../modals/manage-contact-info-modal/manage-contact-info-modal.component';
import { select, Store } from '@ngxs/store';
import { getCountryValueFromPrefix, injectDestroy } from '@libs/core';

@Directive()
export abstract class BaseContactInformationComponent implements OnInit {
  abstract contactMediumTypeGroupCode: ContactMediumType.ContactMediumTypeGroupCode;
  abstract modalAddTitle: string;
  abstract modalEditTitle: string;

  protected store = inject(Store);
  protected customerProfileService = inject(CustomerProfileService);
  protected modalService = inject(ModalService);
  protected translateService = inject(TranslateService);
  protected toasterService = inject(ToasterService);
  protected destroy$ = injectDestroy();

  countryTypes = select(LovState.countryType);
  countryOptions = computed(() => this.countryTypes()?.countryOptions('phoneNumber', 0) ?? []);

  filteredContactList!: Signal<ContactMediumType.ContactMediumDTO[]>;

  hasPermissionToEditContact = this.customerProfileService.hasPermissionFor(
    eBusinessFlow.Levels.CUST_CONTACT_MEDIUM,
    eCommon.BsnInterSpecShortCode.UPDT_CNTC_MEDIUM,
  );

  hasPermissionToCreateContact = this.customerProfileService.hasPermissionFor(
    eBusinessFlow.Levels.CUST_CONTACT_MEDIUM,
    eCommon.BsnInterSpecShortCode.CREATE_CNTC_MEDIUM,
  );

  hasPermissionToRemoveContact = this.customerProfileService.hasPermissionFor(
    eBusinessFlow.Levels.CUST_CONTACT_MEDIUM,
    eCommon.BsnInterSpecShortCode.RMV_CNTC_MEDIUM,
  );

  ngOnInit(): void {
    this.filteredContactList = this.customerProfileService.getContactMediumListByType(this.contactMediumTypeGroupCode);
  }

  protected beforeAdd(): Observable<void> {
    const customerId = this.store.selectSnapshot(CurrentState.customerId);
    const contactMediumType =
      this.contactMediumTypeGroupCode === ContactMediumType.ContactMediumTypeGroupCode.EMAIL
        ? ContactMediumType.ContactMediumTypeShortCode.EMAIL
        : ContactMediumType.ContactMediumTypeShortCode.GSM;

    return this.store.dispatch(new GetCustomerContactMediumConsentAction(customerId, contactMediumType));
  }

  handleAdd(): void {
    this.beforeAdd()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        const isPrimaryLocked = this.filteredContactList().length < 1;

        const modalRef = this.modalService.open(ManageContactInfoModalComponent, {
          title: this.modalAddTitle,
          size: ModalSize.MEDIUM,
          data: {
            isPrimaryLocked,
            type: this.contactMediumTypeGroupCode as
              | ContactMediumType.ContactMediumTypeGroupCode.EMAIL
              | ContactMediumType.ContactMediumTypeGroupCode.PHONE,
            closeModal: () => {
              modalRef.close();
            },
            onSubmit: (payload: ContactMediumType.CreateCustomerContactMediumRequest) => {
              this.store.dispatch(new CreateCustomerContactMediumAction(payload)).subscribe(() => {
                this.handleSuccess();
                modalRef.close();
              });
            },
          },
        });
      });
  }

  handleEdit(contactMedium: ContactMediumType.ContactMediumDTO): void {
    this.store
      .dispatch(
        new GetCommunicationPreferencesAction({
          contactMediumIdList: [contactMedium.id],
          dataType: contactMedium.owner.dataTypeId,
          dataRowId: contactMedium.rowId,
        }),
      )
      .subscribe(() => {
        const isPrimaryLocked = this.filteredContactList().length === 1 || contactMedium.isPrimary;

        const communicationPreferencesState = this.store.selectSnapshot(
          CommunicationPreferencesState.communicationPreferences,
        )?.sortedCommunicationPreferences;
        const privacySpecs = communicationPreferencesState?.privacyList;

        const modalRef = this.modalService.open(ManageContactInfoModalComponent, {
          title: this.modalEditTitle,
          size: ModalSize.MEDIUM,
          data: {
            isPrimaryLocked,
            type: this.contactMediumTypeGroupCode as
              | ContactMediumType.ContactMediumTypeGroupCode.EMAIL
              | ContactMediumType.ContactMediumTypeGroupCode.PHONE,
            ...(this.contactMediumTypeGroupCode === ContactMediumType.ContactMediumTypeGroupCode.EMAIL
              ? {
                  emailData: {
                    email: contactMedium.contactData,
                    isPrimary: contactMedium.isPrimary,
                    privacySpecs,
                  },
                }
              : {
                  phoneNumberData: {
                    phoneNumber: {
                      country: getCountryValueFromPrefix(this.countryOptions(), contactMedium.contactDataPrefix),
                      phoneNumber: contactMedium.contactData,
                    },
                    isPrimary: contactMedium.isPrimary,
                    extension: contactMedium.contactDataExtension,
                    privacySpecs,
                    phoneType: contactMedium.contactMediumType.shortCode,
                  },
                }),
            contactMediumId: contactMedium.id,
            closeModal: () => {
              modalRef.close();
            },
            onSubmit: (payload: ContactMediumType.CreateCustomerContactMediumRequest) => {
              this.store.dispatch(new UpdateCustomerContactMediumAction(payload)).subscribe(() => {
                this.handleSuccess();
                modalRef.close();
              });
            },
          },
        });
      });
  }

  handleDelete(contactMedium: ContactMediumType.ContactMediumDTO): void {
    this.store.dispatch(new DeleteCustomerContactMediumAction(contactMedium.id)).subscribe(() => {
      this.handleSuccess();
    });
  }

  private handleSuccess(): void {
    this.toasterService.success({
      title: this.translateService.translate('toast.SUCCESS_TITLE'),
      description: this.translateService.translate('toast.OPERATION_SUCCESS'),
    });
    const customerId = this.store.selectSnapshot(CurrentState.customerId);
    this.store.dispatch(new InquireCustomerContactMediumAction({ customerId }));
  }
}
