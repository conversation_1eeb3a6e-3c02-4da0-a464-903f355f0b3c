<widget-information-card
  [title]="'contactPhoneNumber' | translate"
  [addLabel]="'addPhoneNumber' | translate"
  (add)="handleAdd()"
  [canAdd]="hasPermissionToCreateContact()"
>
  @for (phoneNumber of filteredContactList(); track phoneNumber.id) {
    <widget-information-card-item
      [isPrimary]="phoneNumber.isPrimary"
      (edit)="handleEdit(phoneNumber)"
      (delete)="handleDelete(phoneNumber)"
      [showEdit]="hasPermissionToEditContact()"
      [showDelete]="!phoneNumber.isPrimary && filteredContactList().length > 1 && hasPermissionToRemoveContact()"
    >
      <eds-text
        size="lg"
        weight="medium"
        [text]="'contactMediumTypes.' + phoneNumber.contactMediumType.name | translate"
      ></eds-text>
      <eds-text
        size="md"
        weight="regular"
        [text]="phoneNumber.contactData | phoneNumber: { prefix: phoneNumber.contactDataPrefix }"
      ></eds-text>
    </widget-information-card-item>
  }
</widget-information-card>
