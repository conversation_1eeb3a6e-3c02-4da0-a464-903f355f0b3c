import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import {
  CurrentState,
  CustomerState,
  GetApplicableInteractionsAction,
  InquireCustomerContactMediumAction,
  LovGetCountryTypesAction,
  LovState,
} from '@libs/bss';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import { eBusinessFlow } from '@libs/types';

export const phoneNumberInformationResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: store.selectSnapshot(LovState.countryType)?.countryTypes?.length,
      action: () => {
        return new LovGetCountryTypesAction();
      },
    },
    {
      selector: store.selectSnapshot(CustomerState.getContactMediumList)?.length,
      action: () => {
        return new InquireCustomerContactMediumAction({ customerId });
      },
    },
    {
      selector: false,
      action: () => {
        return new GetApplicableInteractionsAction({
          level: eBusinessFlow.Levels.CUST_CONTACT_MEDIUM,
          customerId,
        });
      },
    },
  ];
};
