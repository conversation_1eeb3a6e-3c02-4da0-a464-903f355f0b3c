import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { InformationCardComponent, InformationCardItemComponent } from '@libs/widgets';
import { ContactMediumType } from '@libs/types';
import { PhoneNumberPipe } from '@libs/core';
import { BaseContactInformationComponent } from '../base-contact-information.component';
import { phoneNumberInformationResolver } from './phone-number-information.resolver';

@Component({
  selector: 'magic-widget-phone-number-information',
  imports: [InformationCardComponent, InformationCardItemComponent, TranslatePipe, PhoneNumberPipe],
  templateUrl: './phone-number-information-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [PhoneNumberPipe],
})
@MagicConfig({
  resolve: [phoneNumberInformationResolver],
})
export class PhoneNumberInformationMagicComponent extends BaseContactInformationComponent {
  contactMediumTypeGroupCode = ContactMediumType.ContactMediumTypeGroupCode.PHONE;

  override modalAddTitle = this.translateService.translate('addPhoneNumber');
  override modalEditTitle = this.translateService.translate('editPhoneNumber');
}
