<widget-information-card
  [title]="'contactEmail' | translate"
  [addLabel]="'addEmail' | translate"
  (add)="handleAdd()"
  [canAdd]="hasPermissionToCreateContact()"
>
  @for (email of filteredContactList(); track email.id) {
    <widget-information-card-item
      [isPrimary]="email.isPrimary"
      (edit)="handleEdit(email)"
      (delete)="handleDelete(email)"
      [showEdit]="hasPermissionToEditContact()"
      [showDelete]="hasPermissionToRemoveContact() && !email.isPrimary && filteredContactList().length > 1"
    >
      <eds-text class="email-address" size="lg" weight="medium" [text]="email.contactData"></eds-text>
    </widget-information-card-item>
  }
</widget-information-card>
