import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { InformationCardComponent, InformationCardItemComponent } from '@libs/widgets';
import { ContactMediumType } from '@libs/types';
import { BaseContactInformationComponent } from '../base-contact-information.component';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { customerContactMediumResolver } from '../../../../resolvers/customer-contact-medium.resolver';

@Component({
  selector: 'magic-widget-email-information',
  imports: [InformationCardComponent, InformationCardItemComponent, TranslatePipe],
  templateUrl: './email-information-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [customerContactMediumResolver],
})
export class EmailInformationMagicComponent extends BaseContactInformationComponent {
  contactMediumTypeGroupCode = ContactMediumType.ContactMediumTypeGroupCode.EMAIL;

  override modalAddTitle = this.translateService.translate('addEmail');
  override modalEditTitle = this.translateService.translate('editEmail');
}
