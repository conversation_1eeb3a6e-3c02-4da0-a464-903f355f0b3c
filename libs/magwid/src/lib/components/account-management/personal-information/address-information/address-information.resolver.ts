import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import { CurrentState, CustomerGetAddressListAction, GetApplicableInteractionsAction } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';

export const addressInformationResolver = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: false,
      action: () => {
        return new CustomerGetAddressListAction(customerId);
      },
    },
    {
      selector: false,
      action: () => {
        return new GetApplicableInteractionsAction({
          level: eBusinessFlow.Levels.CUST_ADDRESS,
          customerId: customerId,
        });
      },
    },
  ];
};
