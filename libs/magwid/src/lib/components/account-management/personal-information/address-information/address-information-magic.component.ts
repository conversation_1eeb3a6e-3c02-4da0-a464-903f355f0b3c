import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { MagicConfig, ModalService, ModalSize, ToasterService, TranslatePipe, TranslateService } from '@libs/plugins';
import { InformationCardComponent, InformationCardItemComponent } from '@libs/widgets';
import { Store, select } from '@ngxs/store';
import {
  CurrentState,
  CustomerCreateAddressAction,
  CustomerGetAddressListAction,
  CustomerProfileService,
  RemoveCustomerAddressAction,
  UpdateCustomerAddressAction,
} from '@libs/bss';
import { addressInformationResolver } from './address-information.resolver';
import { Address, CreateAddressWrapper, eBusinessFlow, eCommon } from '@libs/types';
import { FormatAddressPipe, injectDestroy } from '@libs/core';
import { takeUntil } from 'rxjs';
import { ManageAddressModalComponent } from '../../../modals/manage-address-modal/manage-address-modal.component';

@Component({
  selector: 'magic-widget-address-information',
  imports: [InformationCardComponent, InformationCardItemComponent, TranslatePipe, FormatAddressPipe],
  templateUrl: './address-information-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [addressInformationResolver],
})
export class AddressInformationMagicComponent {
  private modalService = inject(ModalService);
  private translateService = inject(TranslateService);
  private store = inject(Store);
  private toasterService = inject(ToasterService);
  private customerProfileService = inject(CustomerProfileService);
  private destroy$ = injectDestroy();
  private customerId = select(CurrentState.customerId);

  addresses = this.customerProfileService.addresses;

  hasPermissionToEditAddress = this.customerProfileService.hasPermissionFor(
    eBusinessFlow.Levels.CUST_ADDRESS,
    eCommon.BsnInterSpecShortCode.ADDR_UPDATE,
  );

  hasPermissionToDeleteAddress = this.customerProfileService.hasPermissionFor(
    eBusinessFlow.Levels.CUST_ADDRESS,
    eCommon.BsnInterSpecShortCode.ADDR_REMOVE,
  );

  hasPermissionToCreateAddress = this.customerProfileService.hasPermissionFor(
    eBusinessFlow.Levels.CUST_ADDRESS,
    eCommon.BsnInterSpecShortCode.ADDR_CREATE,
  );

  handleDelete(address: Address) {
    this.store
      .dispatch(new RemoveCustomerAddressAction({ addressId: Number(address.id) }))
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.handleSuccess();
      });
  }

  handleEdit(address: Address) {
    const modalRef = this.modalService.open(ManageAddressModalComponent, {
      title: this.translateService.translate('editAddress'),
      size: ModalSize.MEDIUM,
      data: {
        address: address,
        isPrimaryLocked: this.addresses().length === 1 || address.isPrimary,
        closeModal: () => {
          modalRef.close();
        },
        onSubmit: (addressData: CreateAddressWrapper | Address) => {
          const payload = {
            customerId: this.customerId(),
            address: addressData as Address,
          };
          this.store
            .dispatch(new UpdateCustomerAddressAction(payload))
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
              this.handleSuccess();
              modalRef.close();
            });
        },
      },
    });
  }

  handleAdd() {
    const modalRef = this.modalService.open(ManageAddressModalComponent, {
      title: this.translateService.translate('addNewAddress'),
      size: ModalSize.MEDIUM,
      data: {
        isPrimaryLocked: this.addresses().length < 1,
        closeModal: () => {
          modalRef.close();
        },
        onSubmit: (addressData: CreateAddressWrapper | Address) => {
          this.store
            .dispatch(new CustomerCreateAddressAction(addressData as CreateAddressWrapper))
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
              this.handleSuccess();
              modalRef.close();
            });
        },
      },
    });
  }

  private handleSuccess(): void {
    this.store
      .dispatch(new CustomerGetAddressListAction(this.customerId()))
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.toasterService.success({
          title: this.translateService.translate('toast.SUCCESS_TITLE'),
          description: this.translateService.translate('toast.OPERATION_SUCCESS'),
        });
      });
  }
}
