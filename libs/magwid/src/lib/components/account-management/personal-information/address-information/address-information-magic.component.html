<widget-information-card
  [title]="'contactAddress' | translate"
  [addLabel]="'addAddress' | translate"
  (add)="handleAdd()"
  [canAdd]="hasPermissionToCreateAddress()"
>
  @for (address of addresses(); track address.id) {
    <widget-information-card-item
      [isPrimary]="address.isPrimary"
      (edit)="handleEdit(address)"
      (delete)="handleDelete(address)"
      [showEdit]="hasPermissionToEditAddress()"
      [showDelete]="hasPermissionToDeleteAddress() && !address.isPrimary && addresses().length > 1"
    >
      <eds-text size="lg" weight="medium" [text]="address.addressLabel"></eds-text>
      <eds-text size="md" weight="regular" [text]="address | formatAddress"></eds-text>
    </widget-information-card-item>
  } @empty {
    <div class="no-content-container">
      <eds-icon name="location"></eds-icon>
      <eds-text size="md" weight="medium" [text]="'noAddress' | translate"></eds-text>
    </div>
  }
</widget-information-card>
