import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import {
  CurrentState,
  CustomerState,
  GetApplicableInteractionsAction,
  InquireCustomerInformationAction,
  InquireIndividualCustomerFormLovContentAction,
  LovGetLanguageTypeAction,
  LovState,
  UtilityGetPublicGeneralParameterAction,
  UtilityState,
} from '@libs/bss';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';
import { eBusinessFlow, eCommon, GeneralParameterEnum } from '@libs/types';

export const myProfileResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: false,
      action: () => {
        return new InquireCustomerInformationAction({
          customerId,
        });
      },
    },
    {
      selector: store.selectSnapshot(LovState.languageType)?.languageTypes?.length,
      action: () => {
        return new LovGetLanguageTypeAction();
      },
    },
    {
      selector: store.selectSnapshot(UtilityState.generalParameter),
      action: () => {
        return new UtilityGetPublicGeneralParameterAction(GeneralParameterEnum.CUSTOMER_MIN_AGE);
      },
    },
    {
      selector: store.selectSnapshot(CustomerState.getIndividualCustomerFormLovContent)?.isLoaded,
      action: () => {
        return new InquireIndividualCustomerFormLovContentAction(
          eCommon.BsnInterSpecShortCode.CUSTOMER_DEMOGRAPHIC_INFO,
        );
      },
    },
    {
      selector: false,
      action: () => {
        return new GetApplicableInteractionsAction({
          level: eBusinessFlow.Levels.CUST_DEMOGRAPHIC_INFO,
          customerId: customerId,
        });
      },
    },
  ];
};
