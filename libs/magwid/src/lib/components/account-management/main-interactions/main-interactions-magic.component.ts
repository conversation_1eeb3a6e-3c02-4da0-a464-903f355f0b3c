import { Component, inject, input } from '@angular/core';
import { ActionListItem, MainInteractionsComponent } from '@libs/widgets';
import { Router } from '@angular/router';
import { ConfigState, TranslateService } from '@libs/plugins';
import { select } from '@ngxs/store';
import { FeatureFlagEnum } from '@libs/types';

@Component({
  selector: 'magic-widget-main-interactions',
  templateUrl: './main-interactions-magic.component.html',
  imports: [MainInteractionsComponent],
})
export class MainInteractionsMagicComponent {
  private router = inject(Router);
  private translateService = inject(TranslateService);

  ordersInteraction = input<ActionListItem>({
    text: this.translateService.translate('orders'),
    iconLeading: 'packageIcon',
    iconTrailing: 'arrowRight',
    onClick: () => {
      this.router.navigate(['/my/orders-and-quotes']);
    },
  });
  showTicketsFlag = select(ConfigState.isFeatureEnabled(FeatureFlagEnum.tickets));

  ticketsInteraction = input<ActionListItem>({
    text: this.translateService.translate('tickets'),
    iconLeading: 'ticket',
    iconTrailing: 'arrowRight',
    onClick: () => {
      this.router.navigate(['/']);
    },
  });
}
