import { Component, computed, inject, input } from '@angular/core';
import { Router } from '@angular/router';
import { ConfigState, TranslateService } from '@libs/plugins';
import { Interaction, InteractionListCardComponent } from '@libs/widgets';
import { Store } from '@ngxs/store';
import { FeatureFlagEnum } from '@libs/types';

@Component({
  selector: 'magic-widget-account-interactions',
  templateUrl: './account-interactions-magic.component.html',
  imports: [InteractionListCardComponent],
})
export class AccountInteractionsMagicComponent {
  private translateService = inject(TranslateService);
  private router = inject(Router);
  private store = inject(Store);
  titleText = input<string>(this.translateService.translate('account'));

  accountInteractions = computed<Interaction[]>(() => {
    return [
      ...(this.store.selectSnapshot(ConfigState.isFeatureEnabled(FeatureFlagEnum.accountInformation))
        ? [
            {
              text: this.translateService.translate('accountInformation'),
              onClick: () => {
                this.router.navigate(['/my/account/information']);
              },
            },
          ]
        : []),
      ...(this.store.selectSnapshot(ConfigState.isFeatureEnabled(FeatureFlagEnum.paymentMethods))
        ? [
            {
              text: this.translateService.translate('paymentMethods'),
            },
          ]
        : []),
      ...(this.store.selectSnapshot(ConfigState.isFeatureEnabled(FeatureFlagEnum.transactionHistory))
        ? [
            {
              text: this.translateService.translate('transactionHistory'),
            },
          ]
        : []),
      ...(this.store.selectSnapshot(ConfigState.isFeatureEnabled(FeatureFlagEnum.benefitsAndRewards))
        ? [
            {
              text: this.translateService.translate('benefitsAndRewards'),
            },
          ]
        : []),
      ...(this.store.selectSnapshot(ConfigState.isFeatureEnabled(FeatureFlagEnum.endUser))
        ? [
            {
              text: this.translateService.translate('endUser'),
            },
          ]
        : []),
    ];
  });
}
