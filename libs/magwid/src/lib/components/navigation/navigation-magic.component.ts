import { Component, computed, effect, inject, signal, untracked } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { MagicConfig, TranslateService } from '@libs/plugins';
import { NavigationComponent as WidgetNavigationComponent, NavigationItem } from '@libs/widgets';
import { takeUntil } from 'rxjs';
import { injectDestroy } from '@libs/core';
import { navigationResolver } from './navigation.resolver';
import { eEmployee } from '@libs/types';
import { PermissionState } from '@libs/bss';
import { select } from '@ngxs/store';

@Component({
  selector: 'magic-widget-navigation',
  templateUrl: './navigation-magic.component.html',
  imports: [WidgetNavigationComponent],
})
@MagicConfig({
  resolve: [navigationResolver],
})
export class NavigationMagicComponent {
  private router = inject(Router);
  private translateService = inject(TranslateService);
  private destroy$ = injectDestroy();

  currentUrl = signal(this.router.url);
  menuPermissions = select(PermissionState.getPermission(eEmployee.UIPermission.WSC_MENU));

  navigationItems = signal<NavigationItem[]>([
    {
      name: this.translateService.translate('overview'),
      url: '/my/overview',
      status: '',
      iconName: 'dashboardSquare',
      shortCode: 'wsc-menu-overview',
    },
    {
      name: this.translateService.translate('products'),
      url: '/my/products',
      status: '',
      iconName: 'propertyNew',
      shortCode: 'wsc-menu-product',
    },
    {
      name: this.translateService.translate('bills'),
      url: '/my/bills',
      status: '',
      iconName: 'invoice',
      shortCode: 'wsc-menu-invoice',
    },
    {
      name: this.translateService.translate('shop'),
      url: '',
      status: '',
      iconName: 'shoppingBag',
      shortCode: 'wsc-menu-shopping',
    },
    {
      name: this.translateService.translate('account'),
      url: '/my/account',
      status: '',
      iconName: 'userSquare',
      shortCode: 'wsc-menu-profile',
    },
  ]);

  visibleNavigations = computed(() => {
    return this.navigationItems()
      .map((item) => {
        const permissionItem = this.menuPermissions()?.items.find(
          (permission) => permission.shortCode === item.shortCode,
        );
        return {
          ...item,
          visible: permissionItem?.visible ?? false,
          disabled: permissionItem?.disabled ?? false,
        };
      })
      .filter((item) => item.visible && !item.disabled);
  });

  constructor() {
    this.router.events.pipe(takeUntil(this.destroy$)).subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.currentUrl.set(event.urlAfterRedirects);
      }
    });

    effect(() => {
      const currentUrl = this.currentUrl();
      const allItems = untracked(this.visibleNavigations);

      const matchingItems = allItems.filter(
        (item) => item.url && item.url.length > 0 && currentUrl.startsWith(item.url),
      );

      let bestMatch: NavigationItem | undefined;
      if (matchingItems.length > 0) {
        bestMatch = matchingItems.reduce((prev, current) => (prev.url.length > current.url.length ? prev : current));
      }

      const updatedItems = allItems.map((item) => ({
        ...item,
        status: item.url === bestMatch?.url ? 'active' : '',
      }));
      this.navigationItems.set(updatedItems);
    });
  }
}
