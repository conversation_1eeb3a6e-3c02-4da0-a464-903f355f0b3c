import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { PermissionLoadAction, PermissionState } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eEmployee } from '@libs/types';
import { Store } from '@ngxs/store';

export const navigationResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: store.selectSnapshot(PermissionState.getPermission(eEmployee.UIPermission.WSC_MENU)),
      action: () => {
        return new PermissionLoadAction(eEmployee.UIPermission.WSC_MENU);
      },
    },
  ];
};
