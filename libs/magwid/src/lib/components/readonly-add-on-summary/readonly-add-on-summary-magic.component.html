<eds-card>
  <div class="readonly-addon-header">
    <eds-text size="lg" weight="medium" [text]="'addOns' | translate"></eds-text>
    <eds-text size="lg" weight="medium" [text]="'Total Price'" class="total-price-title"></eds-text>
    <eds-text size="lg" weight="medium" [text]="'Quantity'" class="quantity-title"></eds-text>
  </div>

  <div class="add-on-list">
    @for (addOn of addOns(); track $index) {
      <div class="add-on">
        <eds-text size="lg" weight="medium" [text]="addOn?.offerName"></eds-text>
        <eds-text size="lg" weight="medium" [text]="getAddonPrice(addOn)" class="price-text"></eds-text>
        <eds-text size="lg" [text]="addOn?.quantity" class="addon-quantity"></eds-text>
      </div>
    }
  </div>
</eds-card>
