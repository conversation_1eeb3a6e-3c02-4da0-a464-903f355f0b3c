import { Component, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { CurrentState, QuoteState } from '@libs/bss';
import { TranslatePipe } from '@libs/plugins';
import { QuotePrice } from '@libs/types';
import { select } from '@ngxs/store';
import { CurrencyPipe } from '@angular/common';

@Component({
  selector: 'magic-widget-readonly-add-on-summary',
  templateUrl: './readonly-add-on-summary-magic.component.html',
  styleUrls: ['./readonly-add-on-summary-magic.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe],
  providers: [CurrencyPipe],
})
export class ReadonlyAddonSummaryMagicComponent {
  private currencyPipe = inject(CurrencyPipe);

  addOns = input<QuotePrice.CalculatedAddons[]>([]);

  customerOrderId = select(CurrentState.currentCustomerOrderId);
  quotePriceData = select(QuoteState.priceDetail);

  getAddonPrice(addOn: QuotePrice.CalculatedAddons) {
    return this.currencyPipe.transform(
      addOn?.totalDiscount ? addOn.totalDiscount : addOn?.totalPrice,
      addOn?.currencyCode,
    );
  }
}
