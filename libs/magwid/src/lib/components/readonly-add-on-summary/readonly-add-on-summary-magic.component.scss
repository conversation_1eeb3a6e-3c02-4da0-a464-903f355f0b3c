eds-card {
  --eds-card-overflow: visible;

  &::part(base) {
    padding: 0;
    gap: 0;
    border-radius: 12px;
  }

  .readonly-addon-header {
    border-bottom: 1px solid var(--eds-border-color-default);
    padding: 1rem 1.5rem;
    display: grid;
    grid-template-columns: 3fr 1fr 1fr;
    align-items: center;

    .total-price-title {
      justify-self: center;
    }

    .quantity-title {
      justify-self: end;
    }
  }

  .add-on-list {
    .add-on {
      padding: 1rem 1.5rem;
      display: grid;
      grid-template-columns: 3fr 1fr 1fr;
      align-items: center;
      border-bottom: 1px solid var(--eds-colors-primary-light);

      .price-text {
        justify-self: center;
      }

      .addon-quantity {
        justify-self: end;
        margin-right: 1rem;

        &::part(base) {
          font-size: 18px;
        }
      }
    }
  }
}
