import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  input,
  signal,
} from '@angular/core';
import { TranslateService } from '@libs/plugins';
import { InteractionsMagicComponent } from '../interactions/interactions-magic.component';
import { eBusinessFlow } from '@libs/types';
import { select } from '@ngxs/store';
import { CurrentState, MyProductsState } from '@libs/bss';

@Component({
  selector: 'magic-widget-quick-actions',
  templateUrl: './quick-actions-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [InteractionsMagicComponent],
})
export class QuickActionsMagicComponent {
  private translateService = inject(TranslateService);

  title = input<string>(this.translateService.translate('quickActions'));

  interactionsLevel = signal<eBusinessFlow.Levels>(eBusinessFlow.Levels.PRODUCT_DETAIL_QUICK_ACTIONS);

  billingAccountId = select(CurrentState.currentBillingAccountId);
  products = select(MyProductsState.products);
  product = computed(() => this.products().getProduct(this.billingAccountId()));
}
