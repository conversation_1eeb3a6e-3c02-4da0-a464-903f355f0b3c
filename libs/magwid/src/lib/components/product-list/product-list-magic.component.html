@if (productListData().length) {
  <widget-product-summary
    [products]="productListData()"
    [allText]="'all' | translate"
    [hasMore]="hasMore()"
    [loading]="loading()"
    (loadMore)="onLoadMore()"
    [(includeCancelled)]="includeCancelled"
    [totalCount]="count()"
    [showCount]="productListData().length"
  >
  </widget-product-summary>
} @else {
  <widget-empty-state
    [text]="(includeCancelledButtonText() ? 'noActiveProducts' : 'noProducts') | translate"
    [buttonText]="includeCancelledButtonText() | translate"
    (buttonAction)="showDeactivatedProducts()"
  ></widget-empty-state>
}
