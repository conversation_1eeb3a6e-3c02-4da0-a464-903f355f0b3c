import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { CurrentState, MyServicesGetProductListAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eProduct } from '@libs/types';
import { Store } from '@ngxs/store';

export const productListResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: false,
      action: () => {
        return new MyServicesGetProductListAction({
          customerId: store.selectSnapshot(CurrentState.customerId),
          statusCodes: [
            eProduct.ProductStatusShortCodes.ACTV,
            eProduct.ProductStatusShortCodes.PNDG,
            eProduct.ProductStatusShortCodes.SPND,
            // eProduct.ProductStatusShortCodes.CNCL,
          ],
          planBundleSummary: 1,
          page: 0,
          size: 3,
        });
      },
    },
  ];
};
