import { Component, inject, input } from '@angular/core';
import { Router } from '@angular/router';
import { ErrorComponent } from '@libs/widgets';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'magic-widget-error',
  templateUrl: './error-magic.component.html',
  imports: [ErrorComponent, TranslatePipe],
})
export class ErrorMagicComponent {
  title = input<string>('generalErrorTitle');
  message = input<string>('generalErrorMessage');
  icon = input<string>('alertCircle');
  buttonText = input<string>('generalErrorGoBack');
  showButton = input<boolean>(true);

  private router = inject(Router);

  goBack(): void {
    this.router.navigate(['/']);
  }
}
