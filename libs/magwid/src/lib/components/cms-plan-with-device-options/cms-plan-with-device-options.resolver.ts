import { ResolveFn } from '@angular/router';
import { CmsGetPhoneVariationWithPoqAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { ActivatedRouteSnapshot } from '@angular/router';

export const cmsPlanWithDeviceOptionsResolver: ResolveFn<MagicResolverModel[]> = (
  activatedRoute: ActivatedRouteSnapshot,
) => {
  return [
    {
      selector: false,
      action: () => {
        return new CmsGetPhoneVariationWithPoqAction({
          queryParams: {
            selectedOfferId: activatedRoute.queryParams.selectedOfferId,
          },
        });
      },
    },
  ];
};
