import { Component, computed, inject } from '@angular/core';
import { MagicConfig, TranslateService } from '@libs/plugins';
import {
  CmsPlanPreviewCardComponent,
  CmsPlanDeviceOptionsComponent,
  CmsLayoutContentDefaultComponent,
} from '@libs/widgets';
import { Cms } from '@libs/widgets';
import { cmsPlanWithDeviceOptionsResolver } from './cms-plan-with-device-options.resolver';
import { BusinessFlowInteractionService, CmsState, injectCmsSelectedOffer } from '@libs/bss';
import { select } from '@ngxs/store';
import { ActivatedRoute, Router } from '@angular/router';
import { CurrencyPipe } from '@angular/common';

@Component({
  selector: 'magic-widget-cms-plan-with-device-options',
  templateUrl: './cms-plan-with-device-options-magic.component.html',
  styleUrls: ['./cms-plan-with-device-options-magic.component.scss'],
  providers: [CurrencyPipe],
  imports: [CmsPlanPreviewCardComponent, CmsPlanDeviceOptionsComponent, CmsLayoutContentDefaultComponent],
})
@MagicConfig({
  resolve: [cmsPlanWithDeviceOptionsResolver],
})
export class CmsPlanWithDeviceOptionsMagicComponent {
  private router = inject(Router);
  private translateService = inject(TranslateService);
  private activatedRoute = inject(ActivatedRoute);
  private currencyPipe = inject(CurrencyPipe);
  private businessFlowInteractionService = inject(BusinessFlowInteractionService);
  private selectedOffer = injectCmsSelectedOffer();

  cmsDevices = select(CmsState.devices);

  devices = computed<Cms.Device[]>(() => {
    return this.cmsDevices().map((phone) => ({
      id: phone.id,
      brand: phone.brand,
      model: phone.title,
      price: this.currencyPipe.transform(phone.price),
      deviceImage: phone.image,
      oldPrice: phone.regularPrice === phone.price ? undefined : this.currencyPipe.transform(phone.regularPrice),
      detailPath: phone.detailPath,
      color: phone.color,
    }));
  });

  itemAmount = computed(() =>
    this.translateService.translate('deviceListDeviceAmount', { amount: this.devices().length }),
  );

  previewCard = computed(() =>
    this.selectedOffer()?.getPlanCard(
      {
        translateService: this.translateService,
        currencyPipe: this.currencyPipe,
      },
      {
        title: this.translateService.translate('yourPlanIsReadyNowChooseYourSmartphone'),
        icon: 'simCard',
      },
    ),
  );

  continueWithoutDevice() {
    this.businessFlowInteractionService
      .byodInitialize$(Number(this.activatedRoute.snapshot.queryParams.selectedOfferId))
      .subscribe();
  }

  selectDevice(deviceId: string) {
    const device = this.devices().find((device) => device.id === deviceId);
    if (!device) {
      return;
    }
    this.router.navigate([device.detailPath], {
      queryParams: { selectedOfferId: this.selectedOffer()?.offerId, deviceId },
    });
  }

  back() {
    this.selectedOffer()?.goToHomePage(this.router);
  }
}
