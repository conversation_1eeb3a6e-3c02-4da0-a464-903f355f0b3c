<layout-cms-content-default>
  <section class="base">
    <widget-cms-plan-catalog
      [initialPlansCount]="3"
      [loading]="plansResponse()?.loading()"
      [loadMoreCount]="3"
      [tabs]="tabs()"
      [hasMorePlans]="plansResponse()?.hasNextPage()"
      (onTabChange)="getPlans($event)"
      (onLoadMore)="getMorePlans($event)"
    >
      @if (showMixAndMatchPlans()) {
        <eds-tab slot="tabs" id="mix-and-match"> {{ mixAndMatchTitle() }} </eds-tab>
        <eds-tab-panel id="panelmix-and-match" tab="mix-and-match">
          <magic-widget-cms-mix-and-match></magic-widget-cms-mix-and-match>
        </eds-tab-panel>
      }
    </widget-cms-plan-catalog>
  </section>
</layout-cms-content-default>
