.search-container {
  //position: relative;
  width: 100%;
}

.search-results {
  position: absolute;
  top: 100%;
  right: 0;
  width: 100%;
  max-height: 400px;
  overflow-y: auto;
  background-color: var(--eds-colors-surface-level-1);
  border: 1px solid var(--eds-border-color-light);
  border-radius: var(--eds-radius-400);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 8px;
}

.search-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--eds-spacing-400);
  border-bottom: 1px solid var(--eds-border-color-light);

  h3 {
    margin: 0;
    font-size: var(--eds-font-size-md);
    font-weight: var(--eds-font-weight-medium);
  }
}

.search-loading,
.search-no-results {
  padding: var(--eds-spacing-600);
  text-align: center;
  color: var(--eds-colors-text-light);
}

.search-results-list {
  padding: var(--eds-spacing-200);
}

.search-result-item {
  padding: var(--eds-spacing-400);
  border-bottom: 1px solid var(--eds-border-color-light);
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: var(--eds-colors-surface-level-2);
  }

  p {
    margin: 0;
    font-size: var(--eds-font-size-sm);
    color: var(--eds-colors-text-default);
    line-height: 1.5;
  }

  strong {
    background-color: rgba(255, 122, 0, 0.2);
    color: var(--eds-colors-primary-default);
    font-weight: var(--eds-font-weight-medium);
  }
}

:host ::ng-deep {
  .search-highlight {
    animation: highlight-pulse 3s;
  }
}

@keyframes highlight-pulse {
  0% {
    background-color: rgba(255, 122, 0, 0.1);
  }
  50% {
    background-color: rgba(255, 122, 0, 0.3);
  }
  100% {
    background-color: transparent;
  }
}
