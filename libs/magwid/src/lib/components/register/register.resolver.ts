import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import {
  GetPartyPrivacySpecificationAction,
  LovGetContactMediumTypeAction,
  LovGetCountryTypesAction,
  LovGetLanguageTypeAction,
  LovState,
  PrivacySpecificationState,
  UtilityGetPublicGeneralParameterAction,
  UtilityState,
} from '@libs/bss';
import { ContactMediumType, eBusinessFlow, eCustomer, ePrivacySpecification, GeneralParameterEnum } from '@libs/types';

export const registerResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: store.selectSnapshot(LovState.languageType)?.languageTypes?.length,
      action: () => {
        return new LovGetLanguageTypeAction();
      },
    },
    {
      selector: store.selectSnapshot(LovState.countryType)?.countryTypes?.length,
      action: () => {
        return new LovGetCountryTypesAction();
      },
    },
    {
      selector: store.selectSnapshot(UtilityState.generalParameter),
      action: () => {
        return new UtilityGetPublicGeneralParameterAction(GeneralParameterEnum.CUSTOMER_MIN_AGE);
      },
    },
    {
      selector: store.selectSnapshot(LovState.contactMediumType)?.contactMediumTypes?.length,
      action: () => {
        return new LovGetContactMediumTypeAction({
          predicates: {
            groupTypeCode: { value: 'PHONE' },
          },
        });
      },
      next: [
        {
          selector: store.selectSnapshot(PrivacySpecificationState.partyPrivacySpecificationInfo)
            ?.partyPrivacySpecification?.length,
          action: () => {
            const contactMediumTypeList = store
              .selectSnapshot(LovState.contactMediumType)
              .mappedContactMediumTypes(ContactMediumType.ContactMediumTypeGroupCode.PHONE);

            return new GetPartyPrivacySpecificationAction({
              contactMediumTypeList,
              businessInteractionSpecification: eBusinessFlow.Specification.CUST_CREATE,
              customerType: eCustomer.CustomerTypeShortCode.Residential,
              roleType: ePrivacySpecification.RoleType.CUST,
            });
          },
        },
      ],
    },
  ];
};
