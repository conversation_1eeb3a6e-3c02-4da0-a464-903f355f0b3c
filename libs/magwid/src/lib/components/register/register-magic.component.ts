import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, signal } from '@angular/core';
import { Router } from '@angular/router';
import {
  GetInquirePartyPrivacyDocumentAction,
  LovState,
  PrivacySpecificationState,
  RegisterCreateCustomerAccountAction,
  RegisterState,
  UserCreateCredentialEcaTokenAction,
  UserState,
  UtilityState,
} from '@libs/bss';
import { REGISTER_REQUEST_DATE_FORMAT, StartLoginAction } from '@libs/core';
import {
  Config,
  ConfigState,
  MagicConfig,
  ModalService,
  ModalSize,
  TranslatePipe,
  ToasterService,
  TranslateService,
} from '@libs/plugins';
import { ContactMediumType, Register } from '@libs/types';
import { Checkbox, CreateAccountFormComponent, TermsAndConditionsComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { format } from 'date-fns';
import { switchMap } from 'rxjs';
import { registerResolver } from './register.resolver';

@Component({
  selector: 'magic-widget-register',
  templateUrl: './register-magic.component.html',
  styleUrls: ['./register-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CreateAccountFormComponent, TranslatePipe],
})
@MagicConfig({
  resolve: [registerResolver],
})
export class RegisterMagicComponent {
  private store = inject(Store);
  private modal = inject(ModalService);
  private router = inject(Router);
  private toasterService = inject(ToasterService);
  private translateService = inject(TranslateService);

  languageTypes = select(LovState.languageType);
  mappedLanguageTypes = computed(() => this.languageTypes()?.mappedLanguageTypes());

  partyPrivacySpecificationInfo = select(PrivacySpecificationState.partyPrivacySpecificationInfo);
  mappedPartyPrivacySpecificationInfo = computed(() =>
    this.partyPrivacySpecificationInfo().mappedPartyPrivacySpecification(),
  );

  customerMinAgeParameter = select(UtilityState.generalParameter);

  countryTypes = select(LovState.countryType);
  countryOptions = computed(() => this.countryTypes()?.countryOptions('phoneNumber', 0));

  isRegistrationRequestFailed = signal(false);

  partyPrivacyDocument = select(PrivacySpecificationState.partyPrivacyDocument);

  register(formValue: Register.FormFieldValue) {
    const password = formValue.password as string;

    this.store
      .dispatch(new UserCreateCredentialEcaTokenAction({ password }))
      .pipe(switchMap(() => this.store.selectOnce(UserState.credentialToken)))
      .subscribe({
        next: (credentialToken) => {
          if (credentialToken) {
            this.store
              .dispatch(new RegisterCreateCustomerAccountAction(this.buildRequest(formValue, credentialToken)))
              .pipe(switchMap(() => this.store.selectOnce(RegisterState.registerResponse)))
              .subscribe({
                next: (registerResponse) => {
                  if (registerResponse?.custId) {
                    this.isRegistrationRequestFailed.set(false);
                    this.handleRegistrationResult();
                  }
                },
                error: () => {
                  this.isRegistrationRequestFailed.set(true);
                },
              });
          }
        },
      });
  }

  handleRegistrationResult() {
    this.router.navigate(['/']).then(() => {
      this.toasterService.success({
        title: this.translateService.translate('toast.SUCCESS_TITLE'),
        description: this.translateService.translate('successfulRegistration'),
      });
    });
  }

  buildRequest(formModel: Register.FormFieldValue, credentialToken: string): Register.RegisterRequest {
    const contactMedium: ContactMediumType.ContactMediumDTO[] = [
      {
        contactData: formModel?.phoneNumber,
        contactDataExtension: formModel?.country,
        isPrimary: true,
        contactMediumType: {
          shortCode: 'GSM',
        },
      } as ContactMediumType.ContactMediumDTO,
      {
        contactData: formModel?.email,
        contactDataExtension: formModel?.country,
        isPrimary: true,
        contactMediumType: {
          shortCode: 'EMAIL',
        },
      } as ContactMediumType.ContactMediumDTO,
    ];

    const partyPrivacySpecList = this.partyPrivacySpecificationInfo()?.partyPrivacySpecification.map((spec) => ({
      ...spec,
      authorized: !!formModel.partyPrivacySpecList.find((item: Checkbox) => +item.id === spec.id)?.isChecked,
    }));

    const {
      keycloak: { config },
      url,
    }: Config.State = this.store.selectSnapshot(ConfigState.getAll);

    return {
      partyTypeShortCode: 'INDV',
      customerTypeShortCode: 'RSDNTL',
      employeeNumber: null,
      langShortCode: formModel?.langShortCode,
      email: formModel?.email,
      firstName: formModel?.firstName,
      lastName: formModel?.lastName,
      maidenName: null,
      secretKeyword: null,
      birthDate: format(new Date(formModel.birthDate), REGISTER_REQUEST_DATE_FORMAT),
      contactMedium,
      address: null,
      consentEmail: false,
      consentSms: false,
      announcementConsent: false,
      newsletterConsent: false,
      idpVerifyEmailUserId: config.clientId,
      idpVerifyEmailRedirectUri: `${url['app']}/`,
      passtoken: credentialToken,
      partyPrivacySpecList,
    } as Register.RegisterRequest;
  }

  goToLogin() {
    this.store.dispatch(
      new StartLoginAction({
        redirectUri: '/',
      }),
    );
  }

  openTermsAndConditionsModal(specId: number) {
    this.store.dispatch(new GetInquirePartyPrivacyDocumentAction(specId)).subscribe(() => {
      const modalService = this.modal.open(TermsAndConditionsComponent, {
        title: this.partyPrivacyDocument().partyPrivacyDocumentTmpl.name,
        size: ModalSize.SMALL,
        data: {
          text: this.partyPrivacyDocument().partyPrivacyDocumentTmpl.tmplBody,
          onAcceptClick: () => {
            modalService.close();
          },
        },
      });
    });
  }
}
