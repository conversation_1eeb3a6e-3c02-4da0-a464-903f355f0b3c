import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { select, Store } from '@ngxs/store';
import { QuoteState } from '@libs/bss';
import { ModalService, ModalSize, TranslateService } from '@libs/plugins';
import { PricingAndAgreementTermsModalComponent } from '../modals/pricing-and-agreement-terms-modal/pricing-and-agreement-terms-modal.component';

@Component({
  selector: 'magic-widget-offer-pricing-and-agreement-terms',
  templateUrl: './offer-pricing-and-agreement-terms-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class OfferPricingAndAgreementTermsMagicComponent {
  protected store = inject(Store);
  protected translateService = inject(TranslateService);
  protected modalService = inject(ModalService);

  quote = select(QuoteState.quote);

  planCustomerOrderItemId = input<number>();

  showTerm = input(false);

  termLinkText = input<string>(this.translateService.translate('pricingAndAgreementTerms'));

  plan = computed(() => {
    return this.quote()?.findPlan(this.planCustomerOrderItemId());
  });

  openTermsModal() {
    const productOfferId = this.plan().plan.offerId;
    const productDeviceId = this.plan().device?.offerId ?? null;

    this.modalService.open(PricingAndAgreementTermsModalComponent, {
      title: this.translateService.translate('pricingAndAgreementTermsModalTitle'),
      size: ModalSize.MEDIUM,
      data: {
        productOfferId,
        productDeviceId,
      },
    });
  }
}
