/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, computed, input } from '@angular/core';
import { CmsFaqComponent as WidgetCmsFaqComponent, CmsLayoutContentDefaultComponent, FaqItem } from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-faq',
  templateUrl: './cms-faq-magic.component.html',
  imports: [WidgetCmsFaqComponent, CmsLayoutContentDefaultComponent],
})
export class CmsFaqMagicComponent {
  title = input('Frequently Asked Questions');
  faqContent = input<any>([]);

  faqs = computed<FaqItem[]>(() =>
    this.faqContent()?.map((faq: any) => ({
      question: faq.title,
      answer: faq.body?.value ?? '',
      isOpen: false,
    })),
  );
}
