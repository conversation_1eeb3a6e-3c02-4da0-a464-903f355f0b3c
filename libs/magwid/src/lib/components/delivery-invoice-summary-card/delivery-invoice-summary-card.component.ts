import { ChangeDetectionStrategy, Component, computed, inject, input, OnInit, output } from '@angular/core';
import { CurrentState, QuoteGetDeliverInstallationRetrieveConfigAction, QuoteState } from '@libs/bss';
import { MagicConfig } from '@libs/plugins';
import { DeliveryInvoiceSummaryComponent, SectionComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { DeliveryBasketMagicComponent } from '../checkout/delivery-invoice/delivery-basket/delivery-basket-magic.component';
import { DeliveryInvoiceSummaryCardResolver } from './delivery-invoice-summary-card.resolver';

@Component({
  selector: 'magic-widget-delivery-invoice-summary-card',
  templateUrl: './delivery-invoice-summary-card.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [DeliveryInvoiceSummaryComponent, DeliveryBasketMagicComponent, SectionComponent],
})
@MagicConfig({
  resolve: [DeliveryInvoiceSummaryCardResolver],
})
export class DeliveryInvoiceSummaryCardComponent implements OnInit {
  private store = inject(Store);

  showDeliveryInfo = input(true);
  cardTitle = input<string>();
  customerOrderItemId = input<number>();

  quote = select(QuoteState.quote);

  plan = computed(() => {
    return this.quote()?.findPlan(this.customerOrderItemId());
  });

  deliveryOptions = select(QuoteState.getOfferDeliverInstallationRetrieveConfig);

  editClick = output<void>();

  ngOnInit(): void {
    this.callDeliveryOptionSelection();
  }

  private callDeliveryOptionSelection() {
    const request = {
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      customerOrderId: this.store.selectSnapshot(CurrentState.currentCustomerOrderId),
      deliveryAddress: this.plan()?.deliveryInfo?.deliveryAddress,
    };

    this.store.dispatch(new QuoteGetDeliverInstallationRetrieveConfigAction(request));
  }
}
