import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import {
  CurrentState,
  InquireCustomerSubOrdersAction,
  QuoteGetDeliverInstallationRetrieveConfigAction,
  QuoteGetQuoteAction,
} from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { CustomerOrder, eBusinessFlow } from '@libs/types';

export const orderItemsMagicResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);
  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);

  return [
    {
      selector: false,
      action: () => {
        return new QuoteGetQuoteAction({
          customerOrderId,
        });
      },
      next: [
        {
          selector: false,
          action: () => {
            return new InquireCustomerSubOrdersAction({
              customerId,
              customerOrderId,
              searchType: CustomerOrder.SearchType.SUB_ORDER,
              orderTypeShortCode: eBusinessFlow.Specification.DELIVERY_ORDER,
              page: 0,
              size: 5,
            });
          },
        },
        {
          selector: false,
          action: () => {
            return new QuoteGetDeliverInstallationRetrieveConfigAction({
              customerId,
              customerOrderId,
            });
          },
        },
      ],
    },
  ];
};
