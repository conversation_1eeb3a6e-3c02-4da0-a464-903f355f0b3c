<eds-card [title]="'orderItems' | translate">
  <widget-checkout-card [title]="'mobile' | translate" icon="simCard" [isActive]="true">
    @if (addons()?.items?.length) {
      <widget-offer-addon-card [addons]="addons()"></widget-offer-addon-card>
    }

    @if (plans()?.length && !addons()?.items?.length) {
      @for (plan of plans(); track $index) {
        <widget-section padding="none">
          @if (plan.plan) {
            <widget-offer-plan-card
              [orderItemId]="plan?.customerOrderItemId"
              [offerName]="plan?.plan?.offerName"
              [phoneNumber]="plan.findMsisdnNumber()"
              [simType]="plan.getSimType()"
              [showDetails]="true"
              [isOrderSummary]="true"
              [showDelete]="false"
            ></widget-offer-plan-card>
          }

          @if (plan.device) {
            <widget-offer-device-card
              [offerName]="plan.device?.offerName"
              [chars]="plan.deviceChars"
            ></widget-offer-device-card>
          }

          @if (
            (quote().isOrderFullyCompleted || quote().isOrderInPreparation) &&
            plan.hasDeliveryAddress &&
            hasDeliveryOrder()
          ) {
            <magic-widget-delivery-status
              [customerOrderItemId]="plan?.plan.customerOrderItemId"
            ></magic-widget-delivery-status>
          }

          @if (plan.plan) {
            <widget-titled-section [sectionTitle]="'deliveryInvoice' | translate">
              <widget-delivery-invoice-summary
                [showDeliveryInfo]="!plan.hasESimOfferInstance()"
                [deliveryAddress]="!plan.hasESimOfferInstance() && plan.formatedDeliveryAddress()"
                [deliveryOption]="
                  !plan.hasESimOfferInstance() &&
                  deliveryOptions().estimatedDeliveryInfo(plan?.deliveryInformation()?.deliveryMethod)
                "
                [invoiceAccount]="quote().invoiceAccount()"
              ></widget-delivery-invoice-summary>
            </widget-titled-section>
          }
        </widget-section>
      }
    }
  </widget-checkout-card>
</eds-card>
