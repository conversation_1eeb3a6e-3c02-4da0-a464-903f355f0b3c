import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { OrderItemsMagicComponent } from '../order-items/order-items-magic.component';
import { orderItemsMagicResolver } from '../order-items/order-items-magic.resolver';
import { MagicConfig } from '@libs/plugins';
import { OrderInformationMagicComponent } from '../order-information/order-information-magic-component';
import { PaymentDetailsMagicComponent } from '../payment-details/payment-details-magic.component';
import { ChangedServiceMagicComponent } from '../changed-service/changed-service-magic.component';
import { select, Store } from '@ngxs/store';
import { QuoteState } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';

@Component({
  selector: 'magic-widget-order-and-quote-detail',
  templateUrl: './orders-and-quotes-detail-magic.component.html',
  styleUrls: ['./orders-and-quotes-detail-magic.component.scss'],
  imports: [
    OrderItemsMagicComponent,
    OrderInformationMagicComponent,
    PaymentDetailsMagicComponent,
    ChangedServiceMagicComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
@MagicConfig({
  resolve: [orderItemsMagicResolver],
})
export class OrdersAndQuotesDetailMagicComponent {
  store = inject(Store);

  quote = select(QuoteState.quote);

  get eFlowSpecCode(): typeof eBusinessFlow.Specification {
    return eBusinessFlow.Specification;
  }
}
