import { Component, computed, inject } from '@angular/core';
import { CustomDatePipe, MagicConfig, TranslateService } from '@libs/plugins';
import { orderInformationResolver } from './order-information.resolver';
import { DataList, OrderInformationComponent } from '@libs/widgets';
import { select } from '@ngxs/store';
import { QuoteState } from '@libs/bss';
import { DEFAULT_DATE_FORMAT, getColorByProductStatus } from '@libs/core';

@Component({
  selector: 'magic-widget-order-information',
  templateUrl: './order-information-magic.component.html',
  imports: [OrderInformationComponent],
  providers: [CustomDatePipe],
})
@MagicConfig({
  resolve: [orderInformationResolver],
})
export class OrderInformationMagicComponent {
  private datePipe = inject(CustomDatePipe);
  private translate = inject(TranslateService);

  quote = select(QuoteState.quote);
  orderInformation = computed<DataList>(() => {
    return {
      items: [
        {
          className: '',
          key: this.translate.translate('orderStatus'),
          value: `<eds-tag part="status"
                    content="${this.translate.translate(this.quote().quote?.statusShortCode?.toUpperCase())}"
                    appearance="${getColorByProductStatus(this.quote().quote?.statusShortCode)}"
                 ></eds-tag>`,
        },
        this.quote().quote?.submitDate && {
          className: '',
          key: this.translate.translate('submitDate'),
          value: this.datePipe.transform(this.quote().quote?.submitDate, DEFAULT_DATE_FORMAT),
        },
        {
          className: '',
          key: this.translate.translate('orderType'),
          value: this.translate.translate(this.quote().quote?.flowSpecCode),
        },
      ],
    };
  });
}
