import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import {
  CurrentState,
  PaymentGetCustomerPaymentMethodsAction,
  PaymentState,
  QuoteGetQuoteAction,
  QuoteState,
} from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';

export const paymentDetailsResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);
  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);

  return [
    {
      selector: store.selectSnapshot(QuoteState.quote)?.quote,
      action: () => {
        return new QuoteGetQuoteAction({
          customerOrderId,
        });
      },
      next: [
        {
          selector: store.selectSnapshot(PaymentState.customerPaymentMethods)?.paymentMethods?.length,
          action: () => {
            return new PaymentGetCustomerPaymentMethodsAction(customerId);
          },
        },
      ],
    },
  ];
};
