import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { select, Store } from '@ngxs/store';
import { QuoteState } from '@libs/bss';
import { MagicConfig } from '@libs/plugins';
import { changedServiceMagicResolver } from './changed-service-magic.resolver';
import { OfferPlanCardComponent, PlanChangeCartSummaryComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-changed-service',
  templateUrl: './changed-service-magic.component.html',
  styleUrls: ['./changed-service-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [OfferPlanCardComponent, PlanChangeCartSummaryComponent],
})
@MagicConfig({
  resolve: [changedServiceMagicResolver],
})
export class ChangedServiceMagicComponent {
  store = inject(Store);

  quote = select(QuoteState.quote);

  deactivatedPlan = computed(() => {
    return this.quote().deactivationBundleOffer;
  });

  deactivatedPlanChars = computed(() => {
    const quote = this.quote();
    const dataBucket = quote?.currentPlanData;
    const smsBucket = quote?.currentPlanSms;
    const voiceBucket = quote?.currentPlanVoice;
    return [dataBucket?.offerName, smsBucket?.offerName, voiceBucket?.offerName];
  });

  activatedPlan = computed(() => {
    return this.quote().activationBundleOffers[0];
  });

  activatedPlanChars = computed(() => {
    return this.quote()?.findPlan(this.activatedPlan().customerOrderItemId)?.getPlanChars();
  });
}
