import { CmsGetMixAndMatchAction, CmsState } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { ResolveFn } from '@angular/router';
import { CatalogGroupContractType } from '@libs/types';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';

export const cmsMixAndMatchResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const cmsBlocks = store.selectSnapshot(CmsState.page)?.blocks;

  const widgetMixAndMatchPlanType = cmsBlocks?.find((block) => block.selector === 'widget-cms-mix-and-match')?.data
    ?.widgetPlanType;
  const widgetPlanCatalogPlanType = cmsBlocks?.find((block) => block.selector === 'widget-cms-plan-catalog')?.data
    ?.widgetPlanType;

  const contractType = widgetMixAndMatchPlanType ?? widgetPlanCatalogPlanType ?? CatalogGroupContractType.POSTPAID;

  return [
    {
      selector: false,
      action: () => {
        return new CmsGetMixAndMatchAction({
          filters: [{ id: 'saleType', value: [contractType], options: [] }],
        });
      },
    },
  ];
};
