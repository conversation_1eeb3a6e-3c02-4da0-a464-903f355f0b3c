import { ChangeDetectionStrategy, Component, computed, inject, input, signal } from '@angular/core';
import { MagicConfig } from '@libs/plugins';
import { cmsMixAndMatchResolver } from './cms-mix-and-match.resolver';
import {
  CmsLayoutContentDefaultComponent,
  CmsMixAndMatchComponent,
  CmsMixAndMatchDescription,
  CmsMixAndMatchMixer,
  CmsMixAndMatchPlanBenefit,
} from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { BusinessFlowInteractionService, CmsGetMixAndMatchAction, CmsState } from '@libs/bss';
import { CatalogGroupContractType, CMS, OfferInstanceCharEnum } from '@libs/types';
import { finalize } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'magic-widget-cms-mix-and-match',
  templateUrl: './cms-mix-and-match-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CmsMixAndMatchComponent, CmsLayoutContentDefaultComponent],
})
@MagicConfig({
  resolve: [cmsMixAndMatchResolver],
})
export class CmsMixAndMatchMagicComponent {
  private store = inject(Store);
  private businessFlowInteractionService = inject(BusinessFlowInteractionService);
  private route = inject(ActivatedRoute);

  title = input<string>();
  body = input<{ value: string }>();
  planBenefits = input([]);

  cmsData = select(CmsState.mixAndMatch);
  isLoading = signal(false);

  private _cachedOptions: CMS.MixAndMatchFilter[] = null;
  private _firstResult: CMS.MixAndMatchOffer = this.cmsData().results[0];

  allOptions = computed(() => {
    if (this._cachedOptions === null) {
      this._cachedOptions = this.cmsData().filters;
    }
    return this._cachedOptions;
  });

  private setVariables(key: string): { unit: string; icon: string; value: number | string } {
    switch (key) {
      case 'data':
        return {
          unit: 'GB',
          icon: 'internet',
          value: this._firstResult.dataCharValue,
        };
      case 'voice':
        return {
          unit: 'min',
          icon: 'calling',
          value: this._firstResult.voiceCharValue,
        };
      case 'sms':
        return {
          unit: 'SMS',
          icon: 'message',
          value: this._firstResult.smsCharValue,
        };
      default:
        return {
          unit: key,
          icon: key,
          value: 0,
        };
    }
  }

  mixer = computed((): CmsMixAndMatchMixer[] => {
    const data = this.cmsData();
    return data?.filters
      ?.filter((item: CMS.MixAndMatchFilter) => item.id !== OfferInstanceCharEnum.SALE_TYPE)
      .map((item: CMS.MixAndMatchFilter) => {
        const variables = this.setVariables(item.id);
        const originalOptions = this.allOptions().find(
          (option: CMS.MixAndMatchFilter) => option.id === item.id,
        )?.options;
        return {
          label: item.id.charAt(0).toUpperCase() + item.id.slice(1),
          value: variables.value,
          min: parseInt(Object.keys(originalOptions)[0]),
          max: parseInt(Object.keys(originalOptions)[Object.keys(originalOptions).length - 1]),
          step: 1,
          customValues: originalOptions,
          unit: variables.unit,
          icon: variables.icon,
          disabled: false,
          showButtons: true,
          showValue: true,
          unlimitedOffset: 100,
        };
      });
  });

  description = computed<CmsMixAndMatchDescription[]>(() =>
    this.planBenefits()?.map((item: CmsMixAndMatchPlanBenefit) => ({
      title: item.title,
      description: item.body?.value,
      image: item.image?.mediaImage?.url,
    })),
  );

  onJoinNow() {
    const data = this.cmsData();
    const type = data.filters.find((item) => item.id === OfferInstanceCharEnum.SALE_TYPE)
      ?.value?.[0] as CatalogGroupContractType;

    if (this.route.snapshot.queryParams.flow) {
      return this.businessFlowInteractionService.byodInitialize$(Number(this.cmsData().offerId)).subscribe();
    }
    return this.businessFlowInteractionService.buyWithDevice(this.cmsData().offerId, type);
  }

  onSliderChange(label: string, value: number) {
    const data = this.cmsData();
    const filters = data.filters.map((item: CMS.MixAndMatchFilter) => {
      if (item.id === label.toLowerCase()) {
        return { ...item, value: [value.toString()] };
      }
      return item;
    });
    this.isLoading.set(true);
    this.store
      .dispatch(new CmsGetMixAndMatchAction({ filters: filters as CMS.MixAndMatchFilter[] }))
      .pipe(finalize(() => this.isLoading.set(false)))
      .subscribe();
  }
}
