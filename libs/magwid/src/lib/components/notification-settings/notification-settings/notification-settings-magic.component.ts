import { Component, computed, inject } from '@angular/core';
import { CustomerProfileService, CustomerState } from '@libs/bss';
import { NotificationSettingsMarketingMagicComponent } from '../notification-settings-marketing/notification-settings-marketing-magic.component';
import { select } from '@ngxs/store';
import { MagicConfig } from '@libs/plugins';
import { customerContactMediumResolver } from '../../../resolvers/customer-contact-medium.resolver';
import { ContactMediumType } from '@libs/types';

@Component({
  selector: 'magic-widget-notification-settings',
  templateUrl: './notification-settings-magic.component.html',
  imports: [NotificationSettingsMarketingMagicComponent],
})
@MagicConfig({
  resolve: [customerContactMediumResolver],
})
export class NotificationSettingsMagicComponent {
  private readonly customerProfileService = inject(CustomerProfileService);
  private readonly phoneContacts = this.customerProfileService.getContactMediumListByType(
    ContactMediumType.ContactMediumTypeGroupCode.PHONE,
  );
  private readonly emailContacts = this.customerProfileService.getContactMediumListByType(
    ContactMediumType.ContactMediumTypeGroupCode.EMAIL,
  );

  customerContacts = computed(() => [...this.phoneContacts(), ...this.emailContacts()]);
  customerSubscriptions = select(CustomerState.getSubscriptionList);
}
