import { Component, computed, effect, inject, input, signal } from '@angular/core';
import {
  CommunicationPreferencesData,
  CustomerProfileService,
  GetCommunicationPreferencesAction,
  SavePartyRoleDefaultPartyPrivacyAction,
  UpdateCommunicationPreferencesAction,
} from '@libs/bss';
import { ModalService, ModalSize, ToasterService, TranslateService } from '@libs/plugins';
import { NotificationPreferencesMarketingComponent } from '@libs/widgets';
import { Store } from '@ngxs/store';
import { catchError, finalize, map, switchMap, take, tap } from 'rxjs/operators';
import { CapturedPartyPrivacy, ContactMediumType, eBusinessFlow, eCommon } from '@libs/types';
import { DefaultPreferencesModalComponent } from '../../modals/default-preferences-modal/default-preferences-modal.component';
import { CommunicationPreferencesState } from '@libs/bss';
import { EMPTY, OperatorFunction } from 'rxjs';

export type ItemState = {
  id: number;
  contactInfo: ContactMediumType.ContactMediumDTO;
  preferences: CapturedPartyPrivacy.Result | null;
  draftPreferences: CapturedPartyPrivacy.Result | null;
  isLoading: boolean;
};

@Component({
  selector: 'magic-widget-notification-settings-marketing',
  templateUrl: './notification-settings-marketing-magic.component.html',
  imports: [NotificationPreferencesMarketingComponent],
})
export class NotificationSettingsMarketingMagicComponent {
  private translateService = inject(TranslateService);
  private store = inject(Store);
  private toasterService = inject(ToasterService);
  private modalService = inject(ModalService);
  private customerProfileService = inject(CustomerProfileService);

  hasPermissionToEdit = this.customerProfileService.hasPermissionFor(
    eBusinessFlow.Levels.CUST_CONTACT_MEDIUM,
    eCommon.BsnInterSpecShortCode.UPDT_CNTC_MEDIUM,
  );

  contacts = input<ContactMediumType.ContactMediumDTO[]>();
  subscriptions = input<ContactMediumType.ContactMediumDTO[]>();

  private allItems = computed(() => [...(this.contacts() ?? []), ...(this.subscriptions() ?? [])]);

  public itemStates = signal<ItemState[]>([]);

  public displayItemStates = computed(() => {
    return this.itemStates().map((state) => {
      const displayPrefs = state.draftPreferences ?? state.preferences;
      return {
        ...state,
        displayData: displayPrefs
          ? {
              ...displayPrefs,
              privacyList: displayPrefs.privacyList ?? [],
            }
          : null,
      };
    });
  });

  public contactsForChild = computed(() =>
    this.displayItemStates().filter((item) => item.contactInfo.owner.dataTypeId === eCommon.EntityDataType.CUST),
  );

  public subscriptionsForChild = computed(() =>
    this.displayItemStates().filter((item) => item.contactInfo.owner.dataTypeId === eCommon.EntityDataType.CUST_ACCT),
  );

  constructor() {
    effect(() => {
      const newItems = this.allItems();
      this.itemStates.update((currentStates) => {
        return newItems.map((item) => {
          const existingState = currentStates.find((s) => s.id === item.id);
          return (
            existingState ?? {
              id: item.id,
              contactInfo: item,
              preferences: null,
              draftPreferences: null,
              isLoading: false,
            }
          );
        });
      });
    });
  }

  onAccordionChange({ isOpen, itemId }: { isOpen: boolean; itemId: number }) {
    const targetState = this.itemStates().find((s) => s.id === itemId);
    if (!targetState) return;

    if (isOpen && !targetState.preferences) {
      this.updateItemState(itemId, { isLoading: true });

      const { contactInfo } = targetState;

      const dataType =
        contactInfo.owner.dataTypeId === eCommon.EntityDataType.CUST
          ? contactInfo.owner.dataTypeId
          : eCommon.BusinessInteractionLevelType.CUST_ACCT;

      const action = new GetCommunicationPreferencesAction({
        contactMediumIdList: [contactInfo.id],
        dataType,
        dataRowId: contactInfo.rowId,
        showLoader: false,
      });

      this.store
        .dispatch(action)
        .pipe(
          switchMap(() => this.store.select(CommunicationPreferencesState.communicationPreferences).pipe(take(1))),
          map(
            (communicationPreferences: CommunicationPreferencesData) =>
              communicationPreferences.sortedCommunicationPreferences,
          ),
          tap((preferences) => {
            this.updateItemState(itemId, { preferences });
          }),
          finalize(() => {
            this.updateItemState(itemId, { isLoading: false });
          }),
          this.handleApiError(),
        )
        .subscribe();
    }

    if (!isOpen) {
      this.updateItemState(itemId, { draftPreferences: null });
    }
  }

  onPreferenceChange({ item: changedItem, itemId }: { item: CapturedPartyPrivacy.Item; itemId: number }) {
    this.itemStates.update((states) =>
      states.map((state) => {
        if (state.id !== itemId) {
          return state;
        }

        const basePrefs = state.draftPreferences ?? state.preferences;
        if (!basePrefs) return state;

        const newDraft = {
          ...basePrefs,
          privacyList: basePrefs.privacyList.map((content) => ({
            ...content,
            items: content.items.map((item) => {
              if (item.partyPrivacyItemId !== changedItem.partyPrivacyItemId) {
                return item;
              }
              return { ...item, authorizedFlag: !item.authorizedFlag };
            }),
          })),
        };

        return { ...state, draftPreferences: newDraft };
      }),
    );
  }

  onSave(itemId: number) {
    const targetState = this.itemStates().find((s) => s.id === itemId);
    if (!targetState || !targetState.draftPreferences) return;

    const draft = targetState.draftPreferences;
    const dataType =
      targetState.contactInfo.owner.dataTypeId === eCommon.EntityDataType.CUST
        ? eCommon.BusinessInteractionLevelType.CUST
        : eCommon.BusinessInteractionLevelType.CUST_ACCT;

    const payload: CapturedPartyPrivacy.Result = {
      ...draft,
      dataRowId: targetState.contactInfo.rowId,
      dataType,
    };

    this.store
      .dispatch(new UpdateCommunicationPreferencesAction(payload))
      .pipe(
        tap(() => {
          this.toasterService.success({
            title: this.translateService.translate('toast.SUCCESS_TITLE'),
            description: this.translateService.translate('toast.OPERATION_SUCCESS'),
          });

          this.updateItemState(itemId, { preferences: draft, draftPreferences: null });
        }),
        this.handleApiError(),
      )
      .subscribe();
  }

  onDefaultPreferencesClick() {
    const modalRef = this.modalService.open(DefaultPreferencesModalComponent, {
      title: this.translateService.translate('setDefaultPreferences'),
      size: ModalSize.MEDIUM,
      data: {
        onClose: () => {
          modalRef.close();
        },
        onSubmit: (payload) => {
          this.store
            .dispatch(new SavePartyRoleDefaultPartyPrivacyAction(payload))
            .pipe(
              tap(() => {
                this.toasterService.success({
                  title: this.translateService.translate('toast.SUCCESS_TITLE'),
                  description: this.translateService.translate('toast.OPERATION_SUCCESS'),
                });
                modalRef.close();
              }),
              this.handleApiError(),
            )
            .subscribe();
        },
      },
    });
  }

  private updateItemState(itemId: number, changes: Partial<ItemState>) {
    this.itemStates.update((states) => states.map((s) => (s.id === itemId ? { ...s, ...changes } : s)));
  }

  private handleApiError<T>(): OperatorFunction<T, T> {
    return catchError(({ error }) => {
      this.toasterService.error({
        title: this.translateService.translate(`error.title`),
        description: error?.message
          ? this.translateService.translate(`error.${error.message}`)
          : this.translateService.translate(`error.${error.code}`),
      });
      return EMPTY;
    });
  }
}
