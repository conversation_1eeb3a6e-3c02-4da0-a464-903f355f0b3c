import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { CmsLayoutContentDefaultComponent, CmsNavigationItem, HomeNavigationBarComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-home-navigation-bar',
  templateUrl: './cms-home-navigation-bar-magic.component.html',
  styleUrls: ['./cms-home-navigation-bar-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CmsLayoutContentDefaultComponent, HomeNavigationBarComponent],
})
export class CmsHomeNavigationBarMagicComponent {
  iconTextNavigationItem = input([]);

  navigationItems = computed<CmsNavigationItem[]>(() => {
    return this.iconTextNavigationItem().map((item) => {
      return {
        icon: item.navIcon,
        title: item.iconText?.value,
        link: item.iconLink?.url,
      };
    });
  });
}
