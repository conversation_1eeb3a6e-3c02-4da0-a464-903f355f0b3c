<eds-card>
  <div class="title">
    <eds-icon name="dashboardSquare" class="dashboard-square-icon"></eds-icon>
    <div class="title-text-content">
      <eds-text size="lg" weight="medium" [text]="'enhanceAddons' | translate"></eds-text>
      <eds-text size="md" [text]="'personalizeAddons' | translate"></eds-text>
      <eds-button
        [size]="'compact'"
        [appearance]="'default'"
        iconLeading="plusCircle"
        class="add-ons-btn"
        (button-click)="openAddonModal()"
      >
        {{ 'addOns' | translate }}
      </eds-button>
    </div>
  </div>
  <eds-card class="add-on-summary-content">
    <div class="title">
      <eds-text size="md" [text]="'addOns' | translate"></eds-text>
      <widget-item-delete-action
        class="card-action"
        slot="actionButton"
        [buttonText]="'clearAll' | translate"
        [deleteConfirmationText]="'yesDelete' | translate"
        (deleteItem)="clearAllAddOn()"
      ></widget-item-delete-action>
    </div>

    <div class="add-on-list">
      @for (addOn of addOns(); track $index) {
        <div class="add-on">
          <eds-text size="md" [text]="addOn?.offerName"></eds-text>
          <eds-text size="lg" weight="medium" [text]="getAddonPrice(addOn)" class="price-text"></eds-text>
          <div style="justify-self: end">
            <widget-eds-button-with-counter
              class="counter-widget"
              [counter]="addOn.quantity"
              [controlled]="true"
              (onAction)="handleAddOnCounter($event, addOn)"
            ></widget-eds-button-with-counter>
          </div>
        </div>
      }
    </div>
  </eds-card>
</eds-card>
