import { Component, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { CurrentState, GetQuotePriceDetailAction, QuoteGetQuoteAction, QuoteService, QuoteState } from '@libs/bss';
import { ModalService, ModalSize, TranslatePipe, TranslateService } from '@libs/plugins';
import { eBusinessFlow, QuotePrice } from '@libs/types';
import { select, Store } from '@ngxs/store';
import { CmsAddOnSelectionMagicComponent } from '@libs/magwid';
import { EdsButtonWithCounterComponent, ItemDeleteActionComponent } from '@libs/widgets';
import { CurrencyPipe } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'magic-widget-add-on-summary',
  templateUrl: './add-on-summary-magic.component.html',
  styleUrls: ['./add-on-summary-magic.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [EdsButtonWithCounterComponent, TranslatePipe, ItemDeleteActionComponent],
  providers: [CurrencyPipe],
})
export class AddonSummaryMagicComponent {
  private router = inject(Router);
  private quoteService = inject(QuoteService);
  private store = inject(Store);
  private modalService = inject(ModalService);
  private currencyPipe = inject(CurrencyPipe);
  private translateService = inject(TranslateService);
  private activatedRoute = inject(ActivatedRoute);

  addOns = input<QuotePrice.CalculatedAddons[]>([]);

  customerOrderId = select(CurrentState.currentCustomerOrderId);
  quotePriceData = select(QuoteState.priceDetail);
  quote = select(QuoteState.quote);

  openAddonModal() {
    const modalRef = this.modalService.open(CmsAddOnSelectionMagicComponent, {
      title: this.translateService.translate('chooseAddons'),
      size: ModalSize.XLARGE,
      windowHeight: true,
      data: {
        handleOrderNow: () => {
          this.getQuote();
          modalRef.close();
        },
        orderNowButtonText: 'save',
      },
    });
  }

  clearAllAddOn() {
    this.quoteService
      .addAddOn(
        [],
        this.addOns().flatMap((addon) => addon.customerOrderItemIds),
      )
      .subscribe(() => {
        this.router.navigate([this.quote().purchaseAddonUrl], {
          queryParams: {
            flow: eBusinessFlow.Specification.PURCHASE_ADDON,
            customerOrderId: this.customerOrderId(),
            selectedOfferId: this.activatedRoute.snapshot.queryParams.selectedOfferId,
          },
        });
      });
  }

  getAddonPrice(addOn: QuotePrice.CalculatedAddons) {
    return this.currencyPipe.transform(
      addOn?.totalDiscount ? addOn.totalDiscount : addOn?.totalPrice,
      addOn?.currencyCode,
    );
  }

  handleAddOnCounter(type: 'increment' | 'decrement', addOn: QuotePrice.CalculatedAddons) {
    const updatedQuantity = type === 'increment' ? addOn.quantity + 1 : addOn.quantity - 1;
    const addOnData = { productOfferId: addOn.id, quantity: updatedQuantity };

    this.quoteService.addAddOn([addOnData], addOn.customerOrderItemIds).subscribe(() => {
      addOn.quantity = updatedQuantity;
      this.getQuote();
    });
  }

  private getQuote() {
    this.store.dispatch([
      new QuoteGetQuoteAction({
        customerOrderId: this.customerOrderId(),
        currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ADDON_SELECTION,
      }),
      new GetQuotePriceDetailAction({ customerOrderId: this.customerOrderId() }),
    ]);
  }
}
