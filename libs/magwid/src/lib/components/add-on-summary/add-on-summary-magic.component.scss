eds-card {
  --eds-card-overflow: visible;

  .title {
    display: flex;

    &-text-content {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .dashboard-square-icon {
      display: flex;
      margin-right: 1rem;
      margin-top: 0.25rem;
      width: 20px;
      height: 20px;
    }

    .add-ons-btn {
      margin-top: 0.75rem;
    }
  }
  eds-card {
    &::part(base) {
      background-color: var(--eds-colors-body);
      border-color: transparent;
      padding: 0;
      gap: 0;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      box-shadow: unset;
    }

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.5rem 1rem;
    }

    .add-on-list {
      background-color: var(--eds-white);
      border: 1px solid var(--eds-colors-primary-light);
      border-top-left-radius: 1rem;
      border-top-right-radius: 1rem;

      .add-on {
        padding: 0.75rem 1rem;
        display: grid;
        grid-template-columns: 1.25fr 1fr 1fr;
        align-items: center;
        border-bottom: 1px solid var(--eds-colors-primary-light);

        .price-text {
          justify-self: center;
        }

        .counter-widget {
          justify-self: end;
        }
      }
    }
  }
}
