import { Component, computed, input } from '@angular/core';
import { CmsLayoutContentDefaultComponent, TopOffer, TopOffersComponent } from '@libs/widgets';
import { getLink } from '@libs/bss';

@Component({
  selector: 'magic-widget-cms-top-offers',
  templateUrl: './cms-top-offers-magic.component.html',
  imports: [CmsLayoutContentDefaultComponent, TopOffersComponent],
})
export class CmsTopOffersMagicComponent {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  promotion = input<any[]>();

  topOffers = computed<TopOffer[]>(() => {
    return this.promotion().map((item) => {
      return {
        bgImageUrl: item.backgroundImage?.mediaImage?.url,
        textColor: item.textColor,
        title: item.title,
        subtitle: item.subtitle,
        link: getLink(item.url?.url),
        linkText: item.url?.title,
      };
    });
  });
  backgroundColor = input<string | null>('#F2F8FF');
}
