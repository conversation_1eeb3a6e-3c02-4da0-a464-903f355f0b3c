import { Component, computed, input } from '@angular/core';
import { Cms, CmsCtaSupportComponent, CmsLayoutContentDefaultComponent } from '@libs/widgets';
import { getLink } from '@libs/bss';

@Component({
  selector: 'magic-widget-cms-cta-support',
  templateUrl: './cms-cta-support-magic.component.html',
  imports: [CmsCtaSupportComponent, CmsLayoutContentDefaultComponent],
})
export class CmsCtaSupportMagicComponent {
  heading = input('');
  subtitle = input('');

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  button = input<any[]>([]);

  links = computed<Cms.CtaButton[]>(() =>
    this.button()?.map((button) => ({
      label: button.title,
      link: getLink(button.url),
      appearance: button.appearance || 'primary',
    })),
  );
}
