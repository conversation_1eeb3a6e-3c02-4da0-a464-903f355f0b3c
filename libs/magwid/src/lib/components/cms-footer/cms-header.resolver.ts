import { inject } from '@angular/core';
import { CmsGetMenuAction, CmsState } from '@libs/bss';
import { Store } from '@ngxs/store';
import { MagicResolverModel } from '@libs/plugins';
import { ResolveFn } from '@angular/router';

export const cmsFooterResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: store.selectSnapshot(CmsState.menu)?.menus?.length,
      action: () => {
        return new CmsGetMenuAction();
      },
    },
  ];
};
