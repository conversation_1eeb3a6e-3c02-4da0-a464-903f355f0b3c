import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  output,
  signal,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  CurrentState,
  CustomerAddressListData,
  LovClearCityCriteriaAction,
  LovClearStateCriteriaAction,
  LovGetCityCriteriaAction,
  LovGetStateCriteriaAction,
  LovState,
  validateAllFormFields,
} from '@libs/bss';
import { MagicConfig, TranslatePipe, BSSValidators } from '@libs/plugins';
import { Address, AddressType, City, Country, CreateAddressWrapper, State } from '@libs/types';
import { ManageAddressForm, ManageAddressFormComponent, SelectOption } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { manageAddressModalResolver } from './manage-address-modal.resolver';
import { concatMap, of, tap } from 'rxjs';

@Component({
  selector: 'magic-widget-manage-address-modal',
  imports: [ManageAddressFormComponent, TranslatePipe],
  templateUrl: './manage-address-modal.component.html',
  styleUrls: ['./manage-address-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [manageAddressModalResolver],
})
export class ManageAddressModalComponent implements OnDestroy, OnInit {
  private store = inject(Store);
  private fb = inject(FormBuilder);

  address = input<Address>();
  addressType = input<AddressType>(AddressType.Shipment);
  isPrimaryLocked = input<boolean>(false);

  closeModal = output<void>();
  onSubmit = output<CreateAddressWrapper | Address>();

  private stateCriteria = select(LovState.stateCriteria);
  private cityCriteria = select(LovState.cityCriteria);
  private countryTypes = select(LovState.countryType);
  private customerId = select(CurrentState.customerId);

  // Raw options from the store
  countryOptions = computed(() => this.countryTypes()?.countryOptions() ?? []);
  stateOptions = computed(() => this.stateCriteria()?.stateOptions() ?? []);
  cityOptions = computed(() => this.cityCriteria()?.cityOptions() ?? []);

  // IDs of selected items
  selectedCountryId = signal<number | null>(null);
  selectedStateId = signal<number | null>(null);
  selectedCityId = signal<number | null>(null);

  // Options to pass to the template, with `isSelected` calculated
  countryOpts = computed(() => {
    const id = this.selectedCountryId();
    return this.countryOptions().map((o: SelectOption) => ({
      ...o,
      isSelected: (o.value as Country.Country).id === id,
    }));
  });
  stateOpts = computed(() => {
    const id = this.selectedStateId();
    return this.stateOptions().map((o: SelectOption) => ({ ...o, isSelected: +(o.value as State.State).id === id }));
  });
  cityOpts = computed(() => {
    const id = this.selectedCityId();
    return this.cityOptions().map((o: SelectOption) => ({
      ...o,
      isSelected: +(o.value as City.City).id === id,
    }));
  });

  addressList = input<SelectOption[]>([]);
  deliveryAddressList = input<CustomerAddressListData>();
  addressForm: FormGroup<ManageAddressForm>;

  constructor() {
    this.addressForm = this.fb.group({
      addressLabel: this.fb.control('', [Validators.required, BSSValidators.noEmoji]),
      country: this.fb.control(null, [Validators.required]),
      state: this.fb.control(null, [Validators.required]),
      city: this.fb.control(null, [Validators.required]),
      addressDescription: this.fb.control('', [Validators.required, BSSValidators.noEmoji]),
      postalCode: this.fb.control('', [BSSValidators.noEmoji]),
      isPrimary: this.fb.control(false),
      addressTypeId: this.fb.control(null),
    });

    effect(() => {
      const states = this.stateOptions();
      if (states.length === 1 && !this.addressForm.controls.state.value) {
        const stateToSet = states[0].value as State.State;
        this.addressForm.get('state')?.setValue(stateToSet, { emitEvent: false });
        this.getCityOptions(+stateToSet.id);
      }
    });

    effect(() => {
      const cities = this.cityOptions();
      if (cities.length === 1 && !this.addressForm.controls.city.value) {
        const cityToSet = cities[0].value as City.City;
        this.addressForm.get('city')?.setValue(cityToSet, { emitEvent: false });
        this.selectedCityId.set(+cityToSet.id);
      }
    });
  }

  ngOnInit(): void {
    const addressValue = this.address();
    if (!addressValue) {
      return;
    }

    this.addressForm.patchValue({
      addressLabel: addressValue.addressLabel,
      addressDescription: addressValue.addressDescription,
      postalCode: addressValue.postalCode,
      isPrimary: addressValue.isPrimary,
    });

    // Set initial ID and form value for country
    this.selectedCountryId.set(addressValue.countryId);
    const countryToSet = this.countryOptions().find((o) => (o.value as Country.Country).id === addressValue.countryId)
      ?.value as Country.Country;
    this.addressForm.get('country')?.setValue(countryToSet, { emitEvent: false });

    if (addressValue.countryId) {
      this.store
        .dispatch(new LovGetStateCriteriaAction(addressValue.countryId))
        .pipe(
          tap(() => {
            this.selectedStateId.set(addressValue.stateId);
            const stateToSet = this.stateOptions().find((o) => +(o.value as State.State).id === addressValue.stateId)
              ?.value as State.State;
            this.addressForm.get('state')?.setValue(stateToSet, { emitEvent: false });
          }),
          concatMap(() => {
            if (addressValue.stateId) {
              return this.store.dispatch(new LovGetCityCriteriaAction(addressValue.stateId));
            }
            return of(null);
          }),
          tap(() => {
            if (addressValue.stateId) {
              this.selectedCityId.set(addressValue.cityId);
              const cityToSet = this.cityOptions().find((o) => +(o.value as City.City).id === addressValue.cityId)
                ?.value as City.City;
              this.addressForm.get('city')?.setValue(cityToSet, { emitEvent: false });
            }
          }),
        )
        .subscribe();
    }
  }

  getStateOptions(countryId: number) {
    if (countryId === this.selectedCountryId()) {
      return;
    }
    this.selectedCountryId.set(countryId);
    this.selectedStateId.set(null);
    this.selectedCityId.set(null);

    this.addressForm.get('state')?.reset(null, { emitEvent: false });
    this.addressForm.get('city')?.reset(null, { emitEvent: false });

    this.store.dispatch(new LovClearStateCriteriaAction());
    this.store.dispatch(new LovClearCityCriteriaAction());
    this.store.dispatch(new LovGetStateCriteriaAction(countryId));
  }

  getCityOptions(stateId: number) {
    if (stateId === this.selectedStateId()) {
      return;
    }
    this.selectedStateId.set(stateId);
    this.selectedCityId.set(null);

    this.addressForm.get('city')?.reset(null, { emitEvent: false });

    this.store.dispatch(new LovClearCityCriteriaAction());
    this.store.dispatch(new LovGetCityCriteriaAction(stateId));
  }

  onClose() {
    this.store.dispatch(new LovClearStateCriteriaAction());
    this.store.dispatch(new LovClearCityCriteriaAction());
    this.closeModal.emit();
  }

  getAddressRequest() {
    const formValue = this.addressForm.getRawValue();
    const country = formValue.country as Country.Country;
    const state = formValue.state as State.State;
    const city = formValue.city as City.City;

    const baseAddress = {
      addressLabel: formValue.addressLabel,
      countryId: country?.id,
      countryName: country?.name,
      countryCode: country?.countryCode,
      stateId: Number(state?.id),
      stateName: state?.name,
      stateCode: state?.stateCode,
      cityId: Number(city?.id),
      cityName: city?.name,
      postalCode: formValue.postalCode,
      rowId: this.customerId(),
      addressDescription: formValue.addressDescription,
      isPrimary: formValue.isPrimary,
      addressTypeId: this.address()?.addressTypeId,
    };

    if (this.address()) {
      return {
        ...baseAddress,
        id: this.address()?.id,
        dataTypeId: this.address()?.dataTypeId,
      };
    }

    return {
      addressList: [
        {
          ...baseAddress,
          addressType: AddressType.Customer,
        },
      ],
    };
  }

  submit() {
    if (this.addressForm.invalid) {
      validateAllFormFields(this.addressForm);
      return;
    }
    this.onSubmit.emit(this.getAddressRequest());
  }

  ngOnDestroy() {
    this.store.dispatch(new LovClearStateCriteriaAction());
    this.store.dispatch(new LovClearCityCriteriaAction());
  }
}
