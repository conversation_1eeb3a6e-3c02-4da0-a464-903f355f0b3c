import { ResolveFn } from '@angular/router';
import { LovGetFormatListAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eCommon } from '@libs/types';

export const personalDataRequestModalResolver: ResolveFn<MagicResolverModel[]> = () => {
  const actionPayload = {
    entityCodeName: eCommon.CustomerDataRequest.PERS_DATA_EXP_TYPE,
  };

  return [
    {
      selector: false,
      action: () => {
        return new LovGetFormatListAction(actionPayload);
      },
    },
  ];
};
