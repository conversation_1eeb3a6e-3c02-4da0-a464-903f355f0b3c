import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, output } from '@angular/core';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { personalDataRequestModalResolver } from './personal-data-request-modal.component.resolver';
import { PersonalDataRequestFormComponent } from '@libs/widgets';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CustomerProfileService, LovState, validateAllFormFields } from '@libs/bss';
import {
  PersonalDataRequestFormFields,
  PersonalDataRequestFormGroup,
} from '../../../../../../widgets/src/lib/forms/personal-data-request-form/personal-data-request-form.type';
import { ContactMediumType } from '@libs/types';

@Component({
  selector: 'magic-widget-personal-data-request-modal',
  imports: [TranslatePipe, PersonalDataRequestFormComponent],
  templateUrl: './personal-data-request-modal.component.html',
  styleUrls: ['./personal-data-request-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [personalDataRequestModalResolver],
})
export class PersonalDataRequestModalComponent {
  private store = inject(Store);
  private fb = inject(FormBuilder);
  private customerProfileService = inject(CustomerProfileService);

  onSubmit = output<PersonalDataRequestFormFields>();

  emailList = this.customerProfileService.getContactMediumListByType(
    ContactMediumType.ContactMediumTypeGroupCode.EMAIL,
  );

  emailOptions = computed(() =>
    this.emailList().map((email) => ({
      label: email.contactData,
      value: String(email.id),
      name: email.contactData,
      isSelected: false,
      isDisabled: false,
    })),
  );

  formatOptions = computed(() => this.store.selectSnapshot(LovState.getFormatList)?.formatOptions);

  personalDataRequestForm: FormGroup<PersonalDataRequestFormGroup>;

  constructor() {
    this.personalDataRequestForm = this.fb.group({
      email: ['', [Validators.required]],
      format: ['', [Validators.required]],
      shareDataApproval: [false, [Validators.requiredTrue]],
    });
  }

  submit() {
    this.personalDataRequestForm.markAllAsTouched();

    if (this.personalDataRequestForm.invalid) {
      validateAllFormFields(this.personalDataRequestForm);

      if (!this.personalDataRequestForm.get('shareDataApproval')?.value) {
        this.personalDataRequestForm.setErrors({
          required: true,
        });
      }
      return;
    }

    this.onSubmit.emit(this.personalDataRequestForm.getRawValue());
  }
}
