<div class="base">
  <div class="body">
    <eds-text size="lg" [text]="'removeDataRequestText' | translate"></eds-text>

    <eds-checkbox id="shareDataApproval" name="shareDataApproval" (change)="approval.set(!approval())">
      <label for="shareDataApproval">{{ 'removeDataRequestApprovalLabel' | translate }}</label>
    </eds-checkbox>
  </div>

  <div class="actions">
    <eds-button [appearance]="'destructive'" (button-click)="submit()" [disabled]="!approval()">
      {{ 'yesDelete' | translate }}
    </eds-button>
    <eds-button [appearance]="'default'" (button-click)="onCancel.emit()">
      {{ 'cancel' | translate }}
    </eds-button>
  </div>
</div>
