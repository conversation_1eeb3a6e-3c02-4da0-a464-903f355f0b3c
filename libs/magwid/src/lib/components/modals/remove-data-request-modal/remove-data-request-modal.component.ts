import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, output, signal } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'magic-widget-remove-data-request-modal',
  imports: [TranslatePipe],
  templateUrl: './remove-data-request-modal.component.html',
  styleUrls: ['./remove-data-request-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class RemoveDataRequestModalComponent {
  approval = signal<boolean>(false);

  onSubmit = output<void>();

  onCancel = output<void>();

  submit() {
    if (!this.approval()) {
      return;
    }

    this.onSubmit.emit();
  }
}
