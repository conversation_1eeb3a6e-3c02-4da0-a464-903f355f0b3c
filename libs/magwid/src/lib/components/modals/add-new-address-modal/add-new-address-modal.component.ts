import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  input,
  OnDestroy,
  output,
  viewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  CurrentState,
  CustomerAddressListData,
  LovGetCityCriteriaAction,
  LovGetStateCriteriaAction,
  LovClearStateCriteriaAction,
  LovClearCityCriteriaAction,
  LovState,
  validateAllFormFields,
} from '@libs/bss';
import { MagicConfig, TranslatePipe, BSSValidators } from '@libs/plugins';
import { AddressType, CreateAddressWrapper } from '@libs/types';
import { AddNewAddressForm, AddNewAddressFormComponent, eAddressSelectionMode, SelectOption } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { addNewAddressModalResolver } from './add-new-address-modal.resolver';

@Component({
  selector: 'magic-widget-add-new-address-modal',
  imports: [AddNewAddressFormComponent, TranslatePipe],
  templateUrl: './add-new-address-modal.component.html',
  styleUrls: ['./add-new-address-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [addNewAddressModalResolver],
})
export class AddNewAddressModalComponent implements OnDestroy {
  private store = inject(Store);
  private fb = inject(FormBuilder);

  addressFormComponent = viewChild.required(AddNewAddressFormComponent);

  addressType = input<AddressType>(AddressType.Shipment);

  closeModal = output<void>();
  addAddress = output<CreateAddressWrapper>();

  stateCriteria = select(LovState.stateCriteria);
  cityCriteria = select(LovState.cityCriteria);
  customerId = select(CurrentState.customerId);
  countryTypes = select(LovState.countryType);

  countryOptions = computed(() => this.countryTypes()?.countryOptions());
  countryOptionsForPhoneNumber = computed(() => this.countryTypes()?.countryOptions('phoneNumber', 0));
  cityOptions = computed(() => this.cityCriteria()?.cityOptions());
  stateOptions = computed(() => this.stateCriteria()?.stateOptions());

  addressList = input<SelectOption[]>([]);
  deliveryAddressList = input<CustomerAddressListData>();
  addressForm: FormGroup<AddNewAddressForm>;

  constructor() {
    this.addressForm = this.fb.group({
      addressLabel: this.fb.control('', [BSSValidators.noEmoji]),
      country: this.fb.control(null, [Validators.required]),
      state: this.fb.control(null, [Validators.required]),
      city: this.fb.control(null, [Validators.required]),
      address: this.fb.control('', [BSSValidators.noEmoji]),
      postalCode: this.fb.control('', [BSSValidators.noEmoji]),
      fullName: this.fb.control('', [Validators.required, BSSValidators.noEmoji]),
      phoneNumber: this.fb.control('', [BSSValidators.noEmoji]),
      addressDescription: this.fb.control('', [Validators.required, BSSValidators.noEmoji]),
    });
  }

  getStateOptions(countryId: number) {
    const selectedIndex = this.countryTypes().findIndex(countryId);
    this.countryOptionsForPhoneNumber = computed(() =>
      this.countryTypes()?.countryOptions('phoneNumber', selectedIndex),
    );
    this.store.dispatch(new LovClearStateCriteriaAction());
    this.store.dispatch(new LovClearCityCriteriaAction());
    this.store.dispatch(new LovGetStateCriteriaAction(countryId));
  }

  getCityOptions(stateId: number) {
    this.store.dispatch(new LovClearCityCriteriaAction());
    this.store.dispatch(new LovGetCityCriteriaAction(stateId));
  }

  onClose() {
    this.store.dispatch(new LovClearStateCriteriaAction());
    this.store.dispatch(new LovClearCityCriteriaAction());
    this.closeModal.emit();
  }

  getAddressRequest() {
    return {
      addressList: [
        {
          ...this.addressFormComponent().getAddress(this.customerId()),
        },
      ],
    } as CreateAddressWrapper;
  }

  submit() {
    if (this.addressFormComponent().selectedAddressSelectionType() === eAddressSelectionMode.EXIST_ADDRESS) {
      if (!this.addressFormComponent().selectedExistAddressId()) return;
    } else if (this.addressForm.invalid) {
      validateAllFormFields(this.addressForm);
      return;
    }
    this.addAddress.emit(this.getAddressRequest());
  }

  ngOnDestroy() {
    this.store.dispatch(new LovClearStateCriteriaAction());
    this.store.dispatch(new LovClearCityCriteriaAction());
  }
}
