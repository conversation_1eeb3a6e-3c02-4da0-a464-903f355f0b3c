<div class="base">
  <div class="body">
    @if (type() === ContactMediumTypeGroupCode.PHONE) {
      <widget-manage-phone-number-form
        [phoneNumberForm]="phoneNumberForm"
        [phoneTypeOptions]="phoneTypeOptions()"
        [countryOptionsForPhoneNumber]="countryOptions()"
        [isPrimaryLocked]="isPrimaryLocked()"
        [privacyList]="initialPrivacyList()"
        [isEdit]="isEdit()"
      ></widget-manage-phone-number-form>
    } @else {
      <widget-manage-email-form
        [emailForm]="emailForm"
        [isEdit]="isEdit()"
        [privacyList]="initialPrivacyList()"
        [isPrimaryLocked]="isPrimaryLocked()"
      ></widget-manage-email-form>
    }
  </div>

  <div class="actions">
    <eds-button [appearance]="'default'" (button-click)="onClose()">
      {{ 'cancel' | translate }}
    </eds-button>
    <eds-button [appearance]="'primary'" (button-click)="submit()">
      {{ (isEdit() ? 'save' : 'add') | translate }}
    </eds-button>
  </div>
</div>
