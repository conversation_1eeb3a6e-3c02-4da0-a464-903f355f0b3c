import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  computed,
  output,
  inject,
  signal,
  effect,
} from '@angular/core';
import { CommunicationPreferencesState, CurrentState } from '@libs/bss';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { defaultPreferencesModalResolver } from './default-preferences-modal.resolver';
import { select, Store } from '@ngxs/store';
import { CapturedPartyPrivacy, ContactMediumType } from '@libs/types';
import { Toggle } from '@eds/components';

interface GroupedPreference {
  name: string;
  sortId: number;
  channels: CapturedPartyPrivacy.PartyPrivacyDefault[];
}

@Component({
  selector: 'magic-widget-default-preferences-modal',
  imports: [TranslatePipe],
  templateUrl: './default-preferences-modal.component.html',
  styleUrls: ['./default-preferences-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [defaultPreferencesModalResolver],
})
export class DefaultPreferencesModalComponent {
  private store = inject(Store);

  onClose = output<void>();
  onSubmit = output<CapturedPartyPrivacy.PartyPrivacyDefaultSaveRequest>();

  preferencesList = select(CommunicationPreferencesState.partyRoleDefaultPartyPrivacies);
  editablePreferencesList = signal<CapturedPartyPrivacy.PartyPrivacyDefault[]>([]);

  groupedPreferencesList = computed(() => {
    const list = this.editablePreferencesList();
    if (!list) return [];

    const grouped = new Map<number, GroupedPreference>();

    for (const preference of list) {
      const specId = preference.partyPrivacySpec.id;
      if (!specId) {
        continue;
      }

      if (!grouped.has(specId)) {
        grouped.set(specId, {
          name: preference.partyPrivacySpec.name,
          sortId: preference.partyPrivacySpec.sortId,
          channels: [],
        });
      }
      grouped.get(specId)!.channels.push(preference);
    }

    const groupedArray = Array.from(grouped.values());

    for (const group of groupedArray) {
      group.channels.sort((a, b) => {
        const aIsPhone = a.contactMediumType?.groupTypeCode === ContactMediumType.ContactMediumTypeGroupCode.PHONE;
        const bIsPhone = b.contactMediumType?.groupTypeCode === ContactMediumType.ContactMediumTypeGroupCode.PHONE;
        if (aIsPhone && !bIsPhone) return -1;
        if (!aIsPhone && bIsPhone) return 1;
        return 0;
      });
    }

    return groupedArray.sort((a, b) => a.sortId - b.sortId);
  });

  constructor() {
    effect(() => {
      const originalList = this.preferencesList();
      if (!originalList) {
        this.editablePreferencesList.set([]);
        return;
      }

      const filteredList = originalList.filter((item) => item.partyPrivacySpec.contactMediumRelated);
      this.editablePreferencesList.set(filteredList);
    });
  }

  onPreferenceChange(itemToUpdate: CapturedPartyPrivacy.PartyPrivacyDefault, event: Event) {
    const authorized = (event.target as Toggle).checked;

    this.editablePreferencesList.update((list) =>
      list.map((p) => {
        const isSameItem =
          p.partyPrivacySpec.id === itemToUpdate.partyPrivacySpec.id &&
          p.contactMediumType.id === itemToUpdate.contactMediumType.id;
        return isSameItem ? { ...p, authorizedFlag: authorized } : p;
      }),
    );
  }

  saveChanges() {
    const payload: CapturedPartyPrivacy.PartyPrivacyDefaultSaveRequest = {
      keyCustomer: {
        custId: this.store.selectSnapshot(CurrentState.customerId),
      },
      partyPrivacyDefaultList: this.editablePreferencesList(),
    };
    this.onSubmit.emit(payload);
    this.onClose.emit();
  }
}
