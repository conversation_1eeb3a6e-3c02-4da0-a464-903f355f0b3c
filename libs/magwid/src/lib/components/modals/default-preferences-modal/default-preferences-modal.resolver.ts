import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { CurrentState, InquirePartyRoleDefaultPartyPrivacyAction } from '@libs/bss';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';

export const defaultPreferencesModalResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: false,
      action: () => {
        return new InquirePartyRoleDefaultPartyPrivacyAction(customerId);
      },
    },
  ];
};
