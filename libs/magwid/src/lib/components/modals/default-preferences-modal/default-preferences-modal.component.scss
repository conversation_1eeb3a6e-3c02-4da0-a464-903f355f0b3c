.base {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-600);
}

.body {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
}

.preference-group-container {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
  border: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default);
  border-radius: var(--eds-radius-300);
  padding: var(--eds-spacing-600) 0;
}

.preference-group {
  display: flex;
  justify-content: space-between;
  border-bottom: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-light);
  padding: 0 var(--eds-spacing-400) var(--eds-spacing-400);
  gap: var(--eds-spacing-400);

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.toggles {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-100);
  flex-shrink: 0;
}

.toggle-item {
  display: flex;
  align-items: center;
  gap: var(--eds-spacing-400);
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--eds-spacing-400);
}
