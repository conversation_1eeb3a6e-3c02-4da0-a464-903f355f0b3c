<div class="base">
  <div class="body">
    <eds-text size="lg" weight="medium" [text]="'defaultPreferencesModalDescription' | translate"></eds-text>
    <div class="preference-group-container">
      @for (group of groupedPreferencesList(); track group.sortId) {
        <div class="preference-group">
          <eds-text size="lg" [text]="group.name"></eds-text>
          <div class="toggles">
            @for (channel of group.channels; track channel.contactMediumType.id) {
              <div class="toggle-item">
                <eds-toggle [checked]="channel.authorizedFlag" (change)="onPreferenceChange(channel, $event)">
                  <eds-text
                    [text]="'notificationChannelTypes.' + channel.contactMediumType.groupTypeCode | translate"
                  ></eds-text>
                </eds-toggle>
              </div>
            }
          </div>
        </div>
      }
    </div>
  </div>

  <div class="actions">
    <eds-button appearance="default" shouldFitContainer (button-click)="onClose.emit()">{{
      'cancel' | translate
    }}</eds-button>
    <eds-button appearance="primary" shouldFitContainer (button-click)="saveChanges()">{{
      'save' | translate
    }}</eds-button>
  </div>
</div>
