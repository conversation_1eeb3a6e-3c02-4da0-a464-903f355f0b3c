import { Component, computed, inject } from '@angular/core';
import { select } from '@ngxs/store';
import { PageHeadingComponent } from '@libs/widgets';
import { CurrentState, MyProductsState } from '@libs/bss';
import { TranslateService } from '@libs/plugins';
import { CurrencyPipe } from '@angular/common';
import { RouteDataService } from '@libs/core';
import { LayoutDetailType } from '@libs/types';

@Component({
  selector: 'magic-widget-layout-detail-greeting',
  templateUrl: './layout-detail-greeting-magic.component.html',
  imports: [PageHeadingComponent],
  providers: [CurrencyPipe],
})
export class LayoutDetailGreetingMagicComponent {
  private currencyPipe = inject(CurrencyPipe);
  private translateService = inject(TranslateService);
  private routeDataService = inject(RouteDataService);

  routeData = computed(() => this.routeDataService.data());

  products = select(MyProductsState.products);
  billingAccountId = select(CurrentState.currentBillingAccountId);
  customerOrderId = select(CurrentState.currentCustomerOrderId);

  plan = computed(() => this.products().getProductDetailListByBillingAccountId(this.billingAccountId())?.plan);
  priceText = computed(() => {
    return `${this.currencyPipe.transform(this.plan().discountAppliedCalculatedPriceValue || this.plan().calculatedPriceValue)} / ${this.translateService.translate('month')}`;
  });

  get layoutDetailType(): typeof LayoutDetailType {
    return LayoutDetailType;
  }
}
