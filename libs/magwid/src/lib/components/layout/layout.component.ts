import { Component, computed, ElementRef, inject, Injector, Type, viewChild, ViewContainerRef } from '@angular/core';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { LayoutService } from '@libs/core';
import {
  CmsLayoutDefaultComponent,
  LayoutAccountComponent,
  LayoutCartComponent,
  LayoutCheckoutComponent,
  LayoutDefaultComponent,
  LayoutDetailComponent,
  LayoutMembershipComponent,
  LayoutProfileComponent,
} from '@libs/magwid';
import { LayoutTypeEnum } from '@libs/types';
import { MagicResolverDetect } from '@libs/plugins';
import { filter } from 'rxjs';

@Component({
  selector: 'magic-widget-layout',
  template: `
    <ng-template #layout></ng-template>
    <div #content>
      <router-outlet></router-outlet>
    </div>
  `,
  imports: [RouterOutlet],
})
export class LayoutComponent {
  private layoutService = inject(LayoutService);
  private injector = inject(Injector);
  private router = inject(Router);

  layoutContainer = viewChild('layout', { read: ViewContainerRef });
  contentTemplate = viewChild('content', { read: ElementRef });

  activeLayout = computed(() => {
    return this.layouts[this.layoutService.layout()] ?? this.layouts[LayoutTypeEnum.DEFAULT];
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  layouts: Record<LayoutTypeEnum, Type<any>> = {
    [LayoutTypeEnum.DEFAULT]: LayoutDefaultComponent,
    [LayoutTypeEnum.DETAIL]: LayoutDetailComponent,
    [LayoutTypeEnum.PROFILE]: LayoutProfileComponent,
    [LayoutTypeEnum.CMS]: CmsLayoutDefaultComponent,
    [LayoutTypeEnum.MEMBERSHIP]: LayoutMembershipComponent,
    [LayoutTypeEnum.CHECKOUT]: LayoutCheckoutComponent,
    [LayoutTypeEnum.CART]: LayoutCartComponent,
    [LayoutTypeEnum.ACCOUNT]: LayoutAccountComponent,
  };

  constructor() {
    this.router.events.pipe(filter((a) => a instanceof NavigationEnd)).subscribe(() => {
      this.render();
    });
  }

  render() {
    new MagicResolverDetect(this.activeLayout()).renderComponent({
      container: this.layoutContainer(),
      injector: this.injector,
      projectableNodes: [[this.contentTemplate().nativeElement]],
    });
  }
}
