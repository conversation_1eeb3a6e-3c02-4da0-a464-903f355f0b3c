import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';
import { OfferPlanCardComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { GetQuotePriceDetailAction, QuoteGetQuoteAction, QuoteRemoveQuoteAction, QuoteState } from '@libs/bss';
import { ToasterService, TranslateService } from '@libs/plugins';
import { eBusinessFlow, UpdateQuoteRequest } from '@libs/types';
import { takeUntil } from 'rxjs';
import { injectDestroy } from '@libs/core';
import { switchMap } from 'rxjs/operators';
import { Location } from '@angular/common';

@Component({
  selector: 'magic-widget-offer-plan-card',
  templateUrl: './offer-plan-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [OfferPlanCardComponent],
})
export class OfferPlanCardMagicComponent {
  protected store = inject(Store);
  protected translateService = inject(TranslateService);
  protected toasterService = inject(ToasterService);
  protected destroy$ = injectDestroy();
  private location = inject(Location);

  quote = select(QuoteState.quote);

  planCustomerOrderItemId = input<number>();

  showPrice = input(false);

  showDetails = input(false);

  isOrderSummary = input(false);

  showDelete = input(true);

  dynamicChars = input<string[]>([]);

  tags = input<{ text: string; appearance: string }[]>([]);

  deleteConfirmationText = input<string>(this.translateService.translate('yesDelete'));

  plan = computed(() => {
    return this.quote()?.findPlan(this.planCustomerOrderItemId());
  });

  chars = computed(() => {
    return this.quote()?.findPlan(this.planCustomerOrderItemId())?.getPlanChars();
  });

  phoneNumber = computed((): string => {
    return this.plan().findMsisdnNumber();
  });

  simType = computed((): string => {
    return this.plan().getSimType();
  });

  onDelete() {
    this.deleteOffer();
  }

  private deleteQuoteRequest() {
    const removeOfferInstanceRequest: UpdateQuoteRequest = {
      customerOrderId: this.quote()?.quote.customerOrderId,
      removeQuoteItemIds: [this.planCustomerOrderItemId()],
    };

    this.store
      .dispatch(new QuoteRemoveQuoteAction(removeOfferInstanceRequest))
      .pipe(
        takeUntil(this.destroy$),
        switchMap(() => this.store.selectOnce(QuoteState.removeQuoteResponse)),
      )
      .subscribe({
        next: (result) => {
          if (result) {
            this.toasterService.success({
              title: 'Success',
              description: this.translateService.translate('toast.OPERATION_SUCCESS'),
            });

            this.store.dispatch([
              new QuoteGetQuoteAction({
                customerOrderId: result.customerOrderId,
                currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.CART_SUMMARY,
              }),
              new GetQuotePriceDetailAction({ customerOrderId: result.customerOrderId }),
            ]);
          }
        },
        error: () => {
          this.toasterService.error({
            title: 'Error',
            description: this.translateService.translate('toast.BAD_REQUEST'),
          });
        },
      });
  }
  private deleteOffer() {
    switch (this.quote()?.currentBusinessFlowSpecShortCode) {
      case eBusinessFlow.Specification.PACKAGE_CHANGE:
        this.deleteQuoteRequest();
        this.location.back();
        break;
      default:
        this.deleteQuoteRequest();
        break;
    }
  }
}
