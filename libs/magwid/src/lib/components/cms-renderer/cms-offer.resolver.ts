import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { CmsGetOffersAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';

export const cmsOfferResolver: ResolveFn<MagicResolverModel[]> = (activatedRoute: ActivatedRouteSnapshot) => {
  return [
    {
      selector: activatedRoute.queryParams.selectedOfferId === undefined,
      action: () => {
        return new CmsGetOffersAction({
          variables: {
            offerIds: [activatedRoute.queryParams.selectedOfferId],
          },
        });
      },
    },
  ];
};
