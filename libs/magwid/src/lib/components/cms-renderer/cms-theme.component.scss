::ng-deep :host,
::ng-deep :root {
  --font-size-heading-xxl: clamp(calc(var(--eds-size-multiplier) * 8), 10vw, calc(var(--eds-size-multiplier) * 20));
  --font-size-heading-xl: clamp(calc(var(--eds-size-multiplier) * 6), 10vw, calc(var(--eds-size-multiplier) * 14));
  --font-size-heading-lg: clamp(calc(var(--eds-size-multiplier) * 5), 10vw, calc(var(--eds-size-multiplier) * 12));
  --font-size-heading-md: clamp(calc(var(--eds-size-multiplier) * 5), 10vw, calc(var(--eds-size-multiplier) * 8));
  --font-size-heading-sm: clamp(calc(var(--eds-size-multiplier) * 4.5), 10vw, calc(var(--eds-size-multiplier) * 6));
  --font-size-heading-xs: clamp(calc(var(--eds-size-multiplier) * 3), 10vw, calc(var(--eds-size-multiplier) * 4.5));

  --line-height-heading-xxl: clamp(calc(var(--eds-size-multiplier) * 10), 10vw, calc(var(--eds-size-multiplier) * 24));
  --line-height-heading-xl: clamp(calc(var(--eds-size-multiplier) * 7), 10vw, calc(var(--eds-size-multiplier) * 16));
  --line-height-heading-lg: clamp(calc(var(--eds-size-multiplier) * 6), 10vw, calc(var(--eds-size-multiplier) * 14));
  --line-height-heading-md: clamp(calc(var(--eds-size-multiplier) * 6), 10vw, calc(var(--eds-size-multiplier) * 10));
  --line-height-heading-sm: clamp(calc(var(--eds-size-multiplier) * 5), 10vw, calc(var(--eds-size-multiplier) * 8));
  --line-height-heading-xs: clamp(calc(var(--eds-size-multiplier) * 4), 10vw, calc(var(--eds-size-multiplier) * 6));

  --link-text-color: var(--eds-colors-info-default);

  --swiper-pagination-bullet-height: calc(var(--eds-size-multiplier) * 1.5);
  --swiper-pagination-bullet-width: calc(var(--eds-size-multiplier) * 1.5);
  --swiper-pagination-active-bullet-width: calc(var(--eds-size-multiplier) * 6);
}
