import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn, RouterStateSnapshot } from '@angular/router';
import { CmsGetPageAction, UserState } from '@libs/bss';
import { LayoutService } from '@libs/core';
import { LayoutTypeEnum } from '@libs/types';
import { Store } from '@ngxs/store';
import { IframeHandlerService } from '@libs/plugins';

export const cmsRendererResolver: ResolveFn<unknown> = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
  inject(LayoutService).setLayout(LayoutTypeEnum.CMS);
  const currentRoute = state.url;
  const iframeHandlerService = inject(IframeHandlerService);
  const store = inject(Store);
  return store
    .dispatch(
      new CmsGetPageAction({
        variables: {
          path: currentRoute ? currentRoute : '/',
          viewMode: 'full',
          isAuthenticated: store.selectSnapshot(UserState.isAuthenticated),
        },
        queryParams: route.queryParams,
      }),
    )
    .subscribe({
      error: () => {
        iframeHandlerService.sendMessageToParent({
          type: 'PAGE_NOT_FOUND',
          url: currentRoute,
        });
      },
    });
};
