import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  Injector,
  viewChild,
  ViewContainerRef,
} from '@angular/core';
import { injectDebugMode, MagicConfig } from '@libs/plugins';
import { cmsRendererResolver } from './cms-renderer.resolver';
import { CmsState } from '@libs/bss';
import { select } from '@ngxs/store';
import { forkJoin } from 'rxjs';
import { cmsOfferResolver } from './cms-offer.resolver';
import { ScrollToFragmentDirective } from '@libs/core';

@Component({
  selector: 'magic-widget-cms-renderer',
  template: ` <div #content appScrollToFragment></div> `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./cms-theme.component.scss'],
  imports: [ScrollToFragmentDirective],
})
@MagicConfig({
  resolve: [cmsRendererResolver, cmsOfferResolver],
})
export class CmsRendererMagicComponent {
  private injector = inject(Injector);
  private debugMode = injectDebugMode();

  page = select(CmsState.page);

  contentContainer = viewChild('content', { read: ViewContainerRef });

  constructor() {
    effect(() => {
      this.contentContainer().clear();

      const renderers = this.page().blocks.map((block) => block.getRenderer(this.injector));
      forkJoin(renderers).subscribe((renderers) => {
        renderers.forEach((renderer) => {
          renderer?.(this.contentContainer());
        });
      });
    });
    if (this.debugMode()) {
      effect(() => {
        console.debug('Cms Page', this.page());
      });
    }
  }
}
