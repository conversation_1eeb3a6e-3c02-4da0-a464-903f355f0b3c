import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import {
  CurrentState,
  QuoteGetDeliverInstallationRetrieveConfigAction,
  QuoteGetQuoteAction,
  QuoteState,
} from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';

export const deliveryStatusMagicResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);
  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);

  return [
    {
      selector: store.selectSnapshot(QuoteState.quote)?.quote,
      action: () => {
        return new QuoteGetQuoteAction({
          customerOrderId,
        });
      },
      next: [
        {
          selector: store.selectSnapshot(QuoteState.getOfferDeliverInstallationRetrieveConfig),
          action: () => {
            const deliveryAddress = store.selectSnapshot(QuoteState.quote)?.findPlan(customerOrderId)
              ?.deliveryInfo?.deliveryAddress;
            return new QuoteGetDeliverInstallationRetrieveConfigAction({
              customerId,
              customerOrderId,
              deliveryAddress,
            });
          },
        },
      ],
    },
  ];
};
