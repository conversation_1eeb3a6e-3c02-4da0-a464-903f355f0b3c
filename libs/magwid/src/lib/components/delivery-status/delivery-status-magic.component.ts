import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { MagicConfig } from '@libs/plugins';
import { deliveryStatusMagicResolver } from './delivery-status-magic.resolver';
import { DeliveryStatusComponent } from '@libs/widgets';
import { select } from '@ngxs/store';
import { CustomerState, QuoteState } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';

@Component({
  selector: 'magic-widget-delivery-status',
  templateUrl: './delivery-status-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [DeliveryStatusComponent],
})
@MagicConfig({
  resolve: [deliveryStatusMagicResolver],
})
export class DeliveryStatusMagicComponent {
  customerOrderItemId = input<number>();

  quote = select(QuoteState.quote);
  deliveryOptions = select(QuoteState.getOfferDeliverInstallationRetrieveConfig);

  subOrders = select(CustomerState.customerSubOrders);

  deliveryOrder = computed(() => {
    return this.subOrders().orderByShortCode(eBusinessFlow.Specification.DELIVERY_ORDER);
  });
}
