import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, signal } from '@angular/core';
import { CustomDatePipe, MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import { getColorByProductStatus, getProductDetailOneTimePrice, PhoneNumberPipe } from '@libs/core';
import { CurrencyPipe } from '@angular/common';
import { select } from '@ngxs/store';
import { AccountState, checkDeviceIsByod, CurrentState, MyProductsState, ProductCharListData } from '@libs/bss';
import { eBusinessFlow, eOffer, eProduct } from '@libs/types';
import { DataList, DeviceCardComponent as WidgetDeviceCardComponent, Media } from '@libs/widgets';
import { deviceCardResolver } from './device-card.resolver';
import { InteractionsMagicComponent } from '@libs/magwid';

@Component({
  selector: 'magic-widget-device-card',
  templateUrl: './device-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [PhoneNumberPipe, CurrencyPipe, CustomDatePipe],
  imports: [TranslatePipe, WidgetDeviceCardComponent, InteractionsMagicComponent],
})
@MagicConfig({
  resolve: [deviceCardResolver],
})
export class DeviceCardMagicComponent {
  private translate: TranslateService = inject(TranslateService);
  private datePipe: CustomDatePipe = inject(CustomDatePipe);
  private currencyPipe: CurrencyPipe = inject(CurrencyPipe);

  products = select(MyProductsState.products);
  billingAccountId = select(CurrentState.currentBillingAccountId);
  inquireInstallment = select(AccountState.inquireInstallment);
  productDetailList = computed(() => this.products().getProductDetailListByBillingAccountId(this.billingAccountId()));
  device = computed(() => this.productDetailList().device);
  isByod = computed(() => checkDeviceIsByod(this.device()));
  deviceMedia = computed<Media>(() => {
    const device = this.device();
    const image = device.medias
      .find(Boolean)
      .multiLanguageData.find((multi) => multi.language === this.translate.getActiveLang())?.mediaUrl;

    return {
      imageSrc: this.isByod() ? '' : image,
      imageAlt: '',
      upperText: '',
      text: device.name,
      description: '',
    };
  });

  dataList = computed<DataList>(() => {
    const productDetailList = this.productDetailList();
    const device = productDetailList.device;
    const inquireInstallment =
      device.offerChargeType === eOffer.OfferChargeTypeShortCode.ONETIME ? [] : this.inquireInstallment();
    const productChars = new ProductCharListData(productDetailList.nonPrimarySecondaryProductChars);
    const primaryDisplayProductChars = productChars.primaryDisplayChars.filter(
      (productChars) => productChars.productCharShortCode !== eProduct.ProductConsts.ICCID,
    );
    const secondaryDisplayProductChars = productChars.secondaryDisplayChars;

    return {
      itemsSize: 5,
      trim: true,
      expandedText: this.translate.translate('showLess'),
      unexpandedText: this.translate.translate('showMore'),
      items: [
        {
          className: '',
          key: this.translate.translate('status'),
          value: `<eds-tag part="status" content="${device.status?.name}" appearance="${getColorByProductStatus(device.status?.shortCode)}"></eds-tag>`,
        },
        ...primaryDisplayProductChars.map((char) => ({
          className: '',
          key: char.productCharName,
          value: char.productCharValueName || char.productCharValue,
        })),
        {
          className: '',
          key: this.translate.translate('firstActivationDate'),
          value: this.datePipe.transform(device.firstActivationDate, 'medium'),
        },
        ...(device.validUntilDate
          ? [
              {
                className: '',
                key: this.translate.translate('validUntilDate'),
                value: this.datePipe.transform(device.validUntilDate),
              },
            ]
          : []),
        {
          className: '',
          key: this.translate.translate('price'),
          value: getProductDetailOneTimePrice(device, this.currencyPipe),
        },
        ...inquireInstallment.flatMap((installment) => [
          {
            className: '',
            key: this.translate.translate('pricePerMonth'),
            value: `${this.currencyPipe.transform(installment.amount)} / ${this.translate.translate('monthUpper')}`,
          },
          {
            className: '',
            key: this.translate.translate('remainingEipAmount'),
            value: `${this.currencyPipe.transform(installment.amount * installment.installmentNumber)}`,
          },
          {
            className: '',
            key: this.translate.translate('remainingEipMonths'),
            value: `${installment.installmentNumber}`,
          },
        ]),
        ...secondaryDisplayProductChars.map((char) => ({
          className: '',
          key: char.productCharName,
          value: char.productCharValueName || char.productCharValue,
        })),
      ],
    };
  });

  interactionsLevel = signal<eBusinessFlow.Levels>(eBusinessFlow.Levels.PRODUCT_DETAIL_PRIMARY_PRODUCT_CARD);
}
