<widget-cancel-quote
  [reasonTypes]="reasonTypeOptions()"
  [refundInformation]="refund()"
  (reasonCodeEvent)="setReasonCode($event)"
></widget-cancel-quote>

<div class="footer">
  <eds-button appearance="secondary" class="confirm" (button-click)="onConfirm()">
    {{ 'yesCancelQuote' | translate }}
  </eds-button>
  <eds-button appearance="default" class="cancel" (button-click)="onCancel()">
    {{ 'cancel' | translate }}
  </eds-button>
</div>
