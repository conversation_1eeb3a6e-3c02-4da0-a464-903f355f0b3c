import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import {
  BusinessInteractionReasonTypeState,
  CurrentState,
  GetBusinessInteractionReasonTypeAction,
  QuoteRefundConfirmationAction,
} from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { eBusinessFlow } from '@libs/types';

export const cancelQuoteResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);

  return [
    {
      selector: false,
      action: () => {
        return new QuoteRefundConfirmationAction(customerOrderId);
      },
    },
    {
      selector: store.selectSnapshot(BusinessInteractionReasonTypeState.businessInteractionReasonTypes)?.reasons
        ?.length,
      action: () => {
        return new GetBusinessInteractionReasonTypeAction(eBusinessFlow.Specification.ORDER_CANCEL);
      },
    },
  ];
};
