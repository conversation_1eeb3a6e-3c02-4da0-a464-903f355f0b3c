import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, output, signal } from '@angular/core';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { CancelQuoteComponent } from '@libs/widgets';
import { cancelQuoteResolver } from './cancel-quote-magic.resolver';
import { select } from '@ngxs/store';
import { BusinessInteractionReasonTypeState, QuoteState } from '@libs/bss';

@Component({
  selector: 'magic-widget-cancel-quote',
  templateUrl: './cancel-quote-magic.component.html',
  styleUrl: 'cancel-quote-magic.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CancelQuoteComponent, TranslatePipe],
})
@MagicConfig({
  resolve: [cancelQuoteResolver],
})
export class CancelQuoteMagicComponent {
  refund = select(QuoteState.refundConfirmationResponse);

  reasonTypes = select(BusinessInteractionReasonTypeState.businessInteractionReasonTypes);
  reasonTypeOptions = computed(() => this.reasonTypes().options());

  closeModal = output<void>();
  confirmModal = output<string>();

  selectedReasonCode = signal<string>(null);

  setReasonCode(reasonCode: string) {
    this.selectedReasonCode.set(reasonCode);
  }

  onConfirm() {
    this.confirmModal.emit(this.selectedReasonCode());
  }

  onCancel() {
    this.closeModal.emit();
  }
}
