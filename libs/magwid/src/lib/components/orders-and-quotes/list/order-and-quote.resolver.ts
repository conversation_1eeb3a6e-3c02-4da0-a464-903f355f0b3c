import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import {
  CurrentState,
  InquireCustomerOrdersAction,
  LovGetOrderStatusesAction,
  LovGetOrderTypesAction,
  LovState,
} from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { CustomerOrder } from '@libs/types';

export const orderAndQuoteResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: false,
      action: () => {
        return new InquireCustomerOrdersAction({
          customerId,
          searchType: 'order',
          pageName: CustomerOrder.SegmentType.ORDER,
          page: 0,
          size: 5,
          sortColumn: CustomerOrder.SortColumn.SUBMIT_DATE,
          sortType: CustomerOrder.SortType.DESC,
        });
      },
    },
    {
      selector: store.selectSnapshot(LovState.orderTypes)?.items?.length,
      action: () => new LovGetOrderTypesAction(),
    },
    {
      selector: store.selectSnapshot(LovState.orderStatuses)?.items?.length,
      action: () => new LovGetOrderStatusesAction(),
    },
  ];
};
