import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, signal } from '@angular/core';
import { select } from '@ngxs/store';
import { CurrentState, MyProductsState, ProductCharListData } from '@libs/bss';
import { MagicConfig, TranslateService } from '@libs/plugins';
import { DataListItem } from '@eds/components';
import { ProductCardProps, SecondaryProductCardComponent as WidgetSecondaryProductCardsComponent } from '@libs/widgets';
import { secondaryProductCardsResolver } from './secondary-product-cards.resolver';
import { InteractionsMagicComponent } from '../interactions/interactions-magic.component';
import { eBusinessFlow } from '@libs/types';
import { getColorByProductStatus } from '@libs/core';

@Component({
  selector: 'magic-widget-secondary-product-cards',
  templateUrl: './secondary-product-cards-magic.component.html',
  styleUrls: ['./secondary-product-card.components.scss'],
  imports: [WidgetSecondaryProductCardsComponent, InteractionsMagicComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [secondaryProductCardsResolver],
})
export class SecondaryProductCardsMagicComponent {
  private translateService = inject(TranslateService);

  productData = select(MyProductsState.products);

  billingAccountId = select(CurrentState.currentBillingAccountId);
  secondaryProducts = computed(
    () => this.productData().getProductDetailListByBillingAccountId(this.billingAccountId()).secondaryProducts,
  );

  secondaryProductCards = computed<ProductCardProps[]>(() =>
    this.secondaryProducts().flatMap((product) => ({
      productDetail: product,
      title: product.name,
      productId: product.productId,
      dataListItems: [
        {
          key: this.translateService.translate('status'),
          value: `<eds-tag part="status" content="${product.status?.name}" appearance="${getColorByProductStatus(product.status?.shortCode)}"></eds-tag>`,
        },
        ...new ProductCharListData(product.productCharList).primaryDisplayChars.map((char) => ({
          key: char.productCharName,
          value: char.productCharValue,
        })),
      ] as DataListItem[],
    })),
  );

  interactionsLevel = signal<eBusinessFlow.Levels>(eBusinessFlow.Levels.PRODUCT_DETAIL_SECONDARY_PRODUCT_CARD);
}
