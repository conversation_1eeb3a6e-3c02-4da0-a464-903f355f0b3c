import { Component, computed, input } from '@angular/core';
import {
  type CmsBlogCard,
  CmsLatestArticlesComponent as WidgetCmsLatestArticlesComponent,
  CmsLayoutContentDefaultComponent,
} from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-latest-articles',
  templateUrl: './cms-latest-articles-magic.component.html',
  imports: [WidgetCmsLatestArticlesComponent, CmsLayoutContentDefaultComponent],
})
export class CmsLatestArticlesMagicComponent {
  heading = input<string>();
  subtitle = input<string>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  article = input<any[]>();
  items = computed<CmsBlogCard[]>(() => {
    return this.article().map((item) => {
      return {
        title: item.title,
        helperText: item.body?.value,
        image: item.backgroundImage?.mediaImage.url,
        badgeType: item.tags?.find(Boolean)?.color || 'green',
        badgeLabel: item.tags?.find(Boolean)?.name || 'Hot deals',
        // link: item.path,
        link: [
          'https://www.etiya.com/tr/blog/yapay-zeka-destekli-urun-paketleriyle-daha-akilli-bir-musteri-deneyimi',
          'https://www.etiya.com/tr/blog/gelecegin-musteri-yonetim-sistemleri-musteri-odakli-yeni-stratejiler-olusturmak',
          'https://www.etiya.com/tr/blog/veriden-kararlara-dijital-ikiz-teknolojisi-ile-musteri-deneyimini-sekillendirmek',
        ][item.path?.replace('/node/', '') % 3],
      };
    });
  });
}
