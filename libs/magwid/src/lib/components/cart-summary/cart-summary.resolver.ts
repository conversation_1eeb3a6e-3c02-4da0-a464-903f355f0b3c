import { ActivatedRouteSnapshot, ResolveFn, RouterStateSnapshot } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { getQuoteResolver } from '../../resolvers/get-quote.resolver';
import { BusinessWorkFlowSetAction, CurrentState, GetQuotePriceDetailAction, QuoteService } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';
import { businessWorkflowConfigResolver } from '@libs/magwid';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';

export const cartSummaryResolver: ResolveFn<MagicResolverModel[]> = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot,
) => {
  const store = inject(Store);
  const quoteService = inject(QuoteService);
  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);

  const resolvers = getQuoteResolver(route, state) as MagicResolverModel[];

  // resolvers[0].next[1].next.push((businessWorkflowConfigResolver(route, state) as MagicResolverModel[])[0]);

  resolvers.unshift({
    selector: false,
    action: () => {
      return new BusinessWorkFlowSetAction(eBusinessFlow.WorkflowStateType.PRE_VALIDATION);
    },
  });

  resolvers.push({
    selector: () => {
      return false;
      // const customerId = store.selectSnapshot(CurrentState.customerId);
      // return store.selectSnapshot(QuoteState.quote)?.quote.customer?.id === customerId;
    },
    action: () => {
      return quoteService.buildNextStateRequest(
        eBusinessFlow.WorkflowStateType.PRE_VALIDATION,
        eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
      );
    },
    next: [
      {
        selector: false,
        action: () => {
          return new GetQuotePriceDetailAction({ customerOrderId });
        },
      },
      ...(businessWorkflowConfigResolver(route, state) as MagicResolverModel[]),
    ],
  });

  return resolvers;
};
