import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  input,
  output,
} from '@angular/core';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { TitleCasePipe } from '@angular/common';
import { select, Store } from '@ngxs/store';
import { CmsState, QuoteState } from '@libs/bss';
import { OfferPlanCardMagicComponent } from '../offer-plan-card/offer-plan-card-magic.component';
import { OfferDeviceCardMagicComponent } from '../offer-device-card/offer-device-card-magic.component';
import { cartSummaryResolver } from './cart-summary.resolver';
import { OfferPricingAndAgreementTermsMagicComponent } from '../offer-pricing-and-agreement-terms/offer-pricing-and-agreement-terms-magic.component';
import {
  CheckoutCardComponent,
  EmptyCardComponent,
  ItemDeleteActionComponent,
  SeperatedComponent,
} from '@libs/widgets';
import { AddonSummaryMagicComponent } from '@libs/magwid';

@Component({
  selector: 'magic-widget-cart-summary',
  templateUrl: './cart-summary-magic.component.html',
  styleUrls: ['./cart-summary-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    OfferPlanCardMagicComponent,
    OfferDeviceCardMagicComponent,
    OfferPricingAndAgreementTermsMagicComponent,
    TranslatePipe,
    TitleCasePipe,
    ItemDeleteActionComponent,
    CheckoutCardComponent,
    SeperatedComponent,
    EmptyCardComponent,
    AddonSummaryMagicComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [cartSummaryResolver],
})
export class CartSummaryMagicComponent {
  store = inject(Store);

  quote = select(QuoteState.quote);
  quotePrice = select(QuoteState.priceDetail);

  planCustomerOrderItemId = input<number>();
  clearButton = output<void>();

  plans = computed(() => {
    return this.quote()?.bundleOffers;
  });

  familyCategoryName = computed(() => {
    return this.quote()?.familyCategoryName;
  });

  addOns = computed(() => {
    return this.quotePrice()?.addOnPriceListWithCalculatedPrice;
  });

  getPlanTags(planCustomerOrderItemId: number) {
    const plan = this.quote()?.findPlan(planCustomerOrderItemId);
    const planOfferId = plan?.plan?.offerId?.toString();
    const cmsOffer = this.store.selectSnapshot(CmsState.offersById(planOfferId));
    if (!plan || !planOfferId) {
      return [];
    }

    return cmsOffer?.marketing_tags || [];
  }

  clearShoppingCart(): void {
    this.clearButton.emit();
  }
}
