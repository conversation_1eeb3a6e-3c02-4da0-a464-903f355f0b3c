import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import {
  CurrentState,
  GetQuotePriceDetailAction,
  PaymentGetCustomerPaymentMethodsAction,
  QuoteGetQuoteAction,
} from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eBusinessFlow } from '@libs/types';
import { Store } from '@ngxs/store';

export const orderSuccessResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: false,
      action: () =>
        new QuoteGetQuoteAction({
          customerOrderId,
          currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_SUMMARY,
        }),
      next: [
        {
          selector: false,
          action: () => new GetQuotePriceDetailAction({ customerOrderId }),
        },
      ],
    },
    {
      selector: false,
      action: () => new PaymentGetCustomerPaymentMethodsAction(customerId),
    },
  ];
};
