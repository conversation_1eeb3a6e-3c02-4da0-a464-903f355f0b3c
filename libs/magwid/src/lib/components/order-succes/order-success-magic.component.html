<div class="base">
  <eds-card>
    <widget-success-icon></widget-success-icon>
    <div class="header">
      <eds-heading size="lg" [text]="'orderSuccessTitle' | translate"></eds-heading>
      <eds-text align="center" size="md" [text]="'orderSuccessSubtitle' | translate"></eds-text>
    </div>

    <div class="order-steps">
      @for (step of orderSteps(); track step.step) {
        <div class="step-container">
          <div class="step" [class.active]="step.active">
            <eds-icon [name]="step.icon" class="step-icon"></eds-icon>
            <span>{{ step.step | translate }}</span>
          </div>
        </div>
      }
    </div>

    <eds-card class="order-details-card">
      @for (info of orderInfo(); track info.key) {
        <div class="detail-row">
          <eds-text size="md" [text]="info.key | translate"></eds-text>
          <div class="detail-value">
            @if (info?.icon) {
              <eds-icon [name]="info.icon" class="detail-icon"></eds-icon>
            }
            <eds-text size="md" weight="medium" [text]="info.value"></eds-text>
          </div>
        </div>
      }
    </eds-card>

    <eds-button [href]="actionUrl()" class="actions-button" appearance="secondary" shouldFitContainer>
      {{ actionKey() | translate }}
    </eds-button>
  </eds-card>
</div>
