import { CurrencyPipe, DatePipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  OnInit,
  Signal,
} from '@angular/core';
import { PaymentState, QuoteState } from '@libs/bss';
import { CustomDatePipe, MagicConfig, TranslatePipe } from '@libs/plugins';
import { select } from '@ngxs/store';
import { orderSuccessResolver } from './order-success.resolver';
import { SuccessIconComponent } from '@libs/widgets';
import { eBusinessFlow } from '@libs/types';

@Component({
  selector: 'magic-widget-order-success',
  templateUrl: './order-success-magic.component.html',
  styleUrls: ['./order-success-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, SuccessIconComponent],
  providers: [CurrencyPipe, CustomDatePipe],
})
@MagicConfig({
  resolve: [orderSuccessResolver],
})
export class OrderSuccessMagicComponent implements OnInit {
  private datePipe = inject(CustomDatePipe);
  private currency = inject(CurrencyPipe);
  private quote = select(QuoteState.quote);
  private paymentMethods = select(PaymentState.customerPaymentMethods);

  orderInfo: Signal<{ key: string; value: string; icon?: string }[]> = computed(() => {
    const paymentMethodTypeName = this.paymentMethods().findPaymentMethodById(
      this.quote().paymentReference?.rowId,
    )?.paymentMethodTypeName;
    return [
      {
        key: 'orderId',
        value: this.quote().quote.customerOrderId.toString(),
      },
      {
        key: 'orderSubmitDate',
        value: this.datePipe.transform(this.quote().quote?.submitDate || new Date(), 'dd/MM/yyyy'),
      },
      ...(this.quote()?.price?.dueNowTotal > 0
        ? [
            {
              key: 'totalAmount',
              value: this.currency.transform(this.quote().price.total || 0),
            },
          ]
        : []),
      ...(paymentMethodTypeName
        ? [
            {
              key: 'paymentMethod',
              value: paymentMethodTypeName,
              icon: 'creditCard',
            },
          ]
        : []),
    ];
  });

  orderSteps = computed<{ step: string; icon: string; active: boolean }[]>(() => {
    const states = [
      { step: 'orderReceived', icon: 'checkmarkCircle', active: true },
      { step: 'preparing', icon: 'packageReceived', active: false },
    ];
    if (!this.quote().hasShipment()) {
      return [...states, { step: 'done', icon: 'packageDelivered', active: false }];
    }

    return [
      ...states,
      { step: 'shipped', icon: 'deliveryDelayed', active: false },
      { step: 'inTransit', icon: 'shippingTruck', active: false },
      { step: 'delivered', icon: 'packageDelivered', active: false },
    ];
  });

  actionKey = computed(() => {
    if (
      [eBusinessFlow.Specification.PACKAGE_CHANGE, eBusinessFlow.Specification.PURCHASE_ADDON].includes(
        this.quote().currentBusinessFlowSpecShortCode,
      )
    ) {
      return 'goToMyHomepage';
    }

    return 'goToHomepage';
  });

  actionUrl = computed(() => {
    if (
      [eBusinessFlow.Specification.PACKAGE_CHANGE, eBusinessFlow.Specification.PURCHASE_ADDON].includes(
        this.quote().currentBusinessFlowSpecShortCode,
      )
    ) {
      return 'my/overview';
    }

    return '.';
  });

  ngOnInit() {
    history.pushState(null, '', location.href);
    window.onpopstate = function () {
      history.go(1);
    };
  }
}
