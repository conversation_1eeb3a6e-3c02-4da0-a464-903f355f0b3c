import { Component, computed, input } from '@angular/core';
import { getLink } from '@libs/bss';
import {
  CmsLayoutContentDefaultComponent,
  CmsSubOfferComponent as WidgetCmsSubOfferComponent,
  CmsSubOffersComponent as WidgetCmsSubOffersComponent,
} from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-sub-offers',
  templateUrl: './cms-sub-offers-magic.component.html',
  imports: [WidgetCmsSubOffersComponent, WidgetCmsSubOfferComponent, CmsLayoutContentDefaultComponent],
})
export class CmsSubOffersMagicComponent {
  title = input('');
  promoItem = input<unknown[]>();

  subOffers = computed(() =>
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    this.promoItem().map((item: any) => ({
      title: item.title,
      description: item.body?.value,
      image: item.image?.mediaImage?.url,
      link: { title: item.link?.title, href: getLink(item.link?.url) },
    })),
  );
}
