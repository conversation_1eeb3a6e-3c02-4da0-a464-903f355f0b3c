import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, output } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { select, Store } from '@ngxs/store';
import { CmsState, QuoteService, QuoteState } from '@libs/bss';
import { OfferPlanCardMagicComponent } from '../../offer-plan-card/offer-plan-card-magic.component';
import { OfferPricingAndAgreementTermsMagicComponent } from '../../offer-pricing-and-agreement-terms/offer-pricing-and-agreement-terms-magic.component';
import { ItemDeleteActionComponent, PlanChangeCartSummaryComponent } from '@libs/widgets';
import { CustomerOrderItemActionTypeEnum } from '@libs/types';

@Component({
  selector: 'magic-widget-plan-change-cart-summary',
  templateUrl: './cart-summary-magic.component.html',
  styleUrls: ['./cart-summary-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    OfferPlanCardMagicComponent,
    OfferPricingAndAgreementTermsMagicComponent,
    TranslatePipe,
    PlanChangeCartSummaryComponent,
    ItemDeleteActionComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PlanChangeCartSummaryMagicComponent {
  store = inject(Store);
  quoteService = inject(QuoteService);

  cancelQuote = output<void>();

  quote = select(QuoteState.quote);

  date = 'Immediate';

  effectiveDateOptions = [
    { label: 'Immediate', value: 'immediate', isSelected: true, isDisabled: false },
    { label: 'Next Billing Date', value: 'nextBillingDate', isSelected: false, isDisabled: false },
  ];

  onDateChanged(newDate: string) {
    this.date = newDate;
    this.effectiveDateOptions = this.effectiveDateOptions.map((option) => ({
      ...option,
      isSelected: option.label === newDate,
    }));

    const isForwardDated = newDate === 'Next Billing Date';
    this.quoteService.updateQuoteForwardDated(isForwardDated);
  }

  currentPlan = computed(() => {
    const quote = this.quote();
    const deactivatedOffer = quote?.bundleOffers?.find(
      (offer) => offer.actionCode === CustomerOrderItemActionTypeEnum.DEACTIVATION,
    );
    return deactivatedOffer?.customerOrderItemId || null;
  });

  currentPlanChars = computed(() => {
    const quote = this.quote();
    const dataBucket = quote?.currentPlanData;
    const smsBucket = quote?.currentPlanSms;
    const voiceBucket = quote?.currentPlanVoice;
    return [dataBucket?.offerName, smsBucket?.offerName, voiceBucket?.offerName];
  });

  newPlan = computed(() => {
    const quote = this.quote();
    const activatedOffer = quote?.bundleOffers?.find(
      (offer) => offer.actionCode === CustomerOrderItemActionTypeEnum.ACTIVATION,
    );
    return activatedOffer?.customerOrderItemId || null;
  });

  getPlanTags(planCustomerOrderItemId: number) {
    const plan = this.quote()?.findPlan(planCustomerOrderItemId);
    const planOfferId = plan?.plan?.offerId?.toString();
    const cmsOffer = this.store.selectSnapshot(CmsState.offersById(planOfferId));
    if (!plan || !planOfferId) {
      return [];
    }

    return cmsOffer?.marketing_tags || [];
  }
}
