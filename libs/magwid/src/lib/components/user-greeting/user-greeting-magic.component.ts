import { Component, computed } from '@angular/core';
import { select } from '@ngxs/store';
import { PageHeadingComponent } from '@libs/widgets';
import { UserState } from '@libs/bss';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { loggedInUserResolver } from '@libs/magwid';

@Component({
  selector: 'magic-widget-user-greeting',
  templateUrl: './user-greeting-magic.component.html',
  imports: [PageHeadingComponent, TranslatePipe],
})
@MagicConfig({
  resolve: [loggedInUserResolver],
})
export class UserGreetingMagicComponent {
  firstName = select(UserState.getFirstName);

  /**
   * Computed property that returns the appropriate greeting based on current time
   */
  greetingMessage = computed(() => {
    const currentHour = new Date().getHours();
    return this.getTimeBasedGreeting(currentHour);
  });

  private getTimeBasedGreeting(hour: number): string {
    if (hour >= 5 && hour < 12) {
      return 'goodMorning'; // 5:00-11:59
    } else if (hour >= 12 && hour < 18) {
      return 'goodAfternoon'; // 12:00-17:59
    } else if (hour >= 18 && hour < 22) {
      return 'goodEvening'; // 18:00-21:59
    } else {
      return 'goodNight'; // 22:00-4:59
    }
  }
}
