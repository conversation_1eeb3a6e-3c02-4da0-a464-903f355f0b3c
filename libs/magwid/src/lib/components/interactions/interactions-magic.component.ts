import { Component, computed, effect, inject, input, output, Signal, signal, WritableSignal } from '@angular/core';
import { ContextDataType, eBusinessFlow, eBusinessInteraction } from '@libs/types';
import { select, Store } from '@ngxs/store';
import {
  BusinessFlowState,
  CurrentState,
  GetApplicableInteractionsFilterAction,
  InitializeAction,
  InteractionFlowData,
  MyProductsState,
  ProductData,
} from '@libs/bss';
import { Interaction, InteractionListComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-interactions',
  templateUrl: './interactions-magic.component.html',
  imports: [InteractionListComponent],
})
export class InteractionsMagicComponent {
  store = inject(Store);

  level = input<eBusinessFlow.Levels>();
  context = input<ContextDataType<eBusinessFlow.Specification>>();

  actions: WritableSignal<InteractionFlowData> = signal(null);
  actionList: Signal<Interaction[]> = computed(() => {
    return this.actions()?.asInteractionList((action) => {
      this.store.dispatch(new InitializeAction(action.shortCode, this.context()));
    });
  });

  products = select(MyProductsState.products);

  itemClick = output<Interaction>();

  constructor() {
    effect(() => {
      const customerId = this.store.selectSnapshot(CurrentState.customerId);

      const filter = {
        type: this.buildLevelType(),
        ids: [this.getProductIdByLevel()],
      };

      this.store
        .dispatch(
          new GetApplicableInteractionsFilterAction(
            {
              level: this.level(),
              customerId,
              filter,
            },
            filter.ids[0],
          ),
        )
        .subscribe(() => {
          this.actions.set(
            this.store.selectSnapshot(BusinessFlowState.applicableInteractionMap(this.level())).flowFlatten,
          );
        });
    });
  }

  onItemClick(item: Interaction) {
    return this.itemClick.emit(item);
  }

  buildLevelType() {
    switch (this.level()) {
      case eBusinessFlow.Levels.BILL_ACCT:
        return eBusinessInteraction.FilterType.BILLING_ACCOUNT;
      default:
        return eBusinessInteraction.FilterType.PRODUCT;
    }
  }

  getProductIdByLevel(): number {
    const product = this.context();

    if (!(product instanceof ProductData)) {
      return product?.productId ?? product; // in case of secondary products
    }

    switch (this.level()) {
      case eBusinessFlow.Levels.BILL_ACCT:
        return product.billingAccountId;
      case eBusinessFlow.Levels.PRODUCT_DETAIL_PRIMARY_PRODUCT_CARD:
        return product.productDetailList().device?.productId;
      case eBusinessFlow.Levels.PRODUCT_DETAIL_USAGE_SUMMARY:
      default:
        return product.productDetailList().plan?.productId;
    }
  }
}
