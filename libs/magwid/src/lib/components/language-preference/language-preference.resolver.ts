import { ResolveFn } from '@angular/router';
import { CurrentState, LovGetGeographyPlacesAction, SetCurrentCommercialRegionAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { PlaceType } from '@libs/types';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';

export const languagePreferenceResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: false,
      action: () => {
        return new LovGetGeographyPlacesAction(PlaceType.COMMERCIAL_REGION);
      },
      next: [
        {
          selector: store.selectSnapshot(CurrentState.currentCommercialRegion),
          action: () => {
            return new SetCurrentCommercialRegionAction();
          },
        },
      ],
    },
  ];
};
