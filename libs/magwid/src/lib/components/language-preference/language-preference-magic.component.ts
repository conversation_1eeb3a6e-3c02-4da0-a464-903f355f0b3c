import { ChangeDetectionStrategy, Component, computed, effect, inject, model, output } from '@angular/core';
import { LovState } from '@libs/bss';
import { KnownLocalStorageKeys, LocalStorageService } from '@libs/core';
import { MagicConfig, TranslateService } from '@libs/plugins';
import { LanguagePreferenceComponent, SelectOption } from '@libs/widgets';
import { select } from '@ngxs/store';
import { languagePreferenceResolver } from './language-preference.resolver';

@Component({
  selector: 'magic-widget-language-preference',
  templateUrl: './language-preference-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [LanguagePreferenceComponent],
})
@MagicConfig({
  resolve: [languagePreferenceResolver],
})
export class LanguagePreferenceMagicComponent {
  translate = inject(TranslateService);
  localStorageService = inject(LocalStorageService);

  open = model<boolean>();

  geographyPlaces = select(LovState.geographyPlaces);
  geographyPlacesOptions = computed(() => this.geographyPlaces().placesOptions);
  languageSelectOptions = computed(() => {
    const storedLang = this.localStorageService.get<string>(KnownLocalStorageKeys.LANGUAGE);
    return this.translate.getAvailableLangs().map((lang) => ({
      label: lang as string,
      value: lang,
      name: lang as string,
      isSelected: lang === (storedLang ?? 'en'),
      isDisabled: false,
      isMultiple: false,
    }));
  });

  isLanguagePreferenceClose = output<boolean>();

  constructor() {
    effect(() => {
      if (this.open()) {
        document.documentElement.style.setProperty('--header-language-preference-row', '1fr');
        document.documentElement.style.setProperty('--header-language-preference-overflow', 'visible');
      } else {
        document.documentElement.style.setProperty('--header-language-preference-row', '0');
        document.documentElement.style.setProperty('--header-language-preference-overflow', 'hidden');
      }
    });
  }

  onClose(): void {
    this.open.set(false);
  }

  handleSave({ language, geographyPlace }: { language: SelectOption; geographyPlace: SelectOption }): void {
    this.localStorageService.set(KnownLocalStorageKeys.LANGUAGE, language.value);
    this.localStorageService.set(KnownLocalStorageKeys.REGION, geographyPlace.value);
    window.location.reload();
  }
}
