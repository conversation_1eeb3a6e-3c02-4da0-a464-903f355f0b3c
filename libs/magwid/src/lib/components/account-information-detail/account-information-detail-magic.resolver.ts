import { inject } from '@angular/core';
import { ResolveFn, Router, ActivatedRouteSnapshot } from '@angular/router';
import { AccountGetInquireBillingAccountInfos, CurrentState, InquireCustomerFinancialInfoAction } from '@libs/bss';
import { Store } from '@ngxs/store';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export const accountInformationDetailResolver: ResolveFn<Observable<boolean>> = (route: ActivatedRouteSnapshot) => {
  const store = inject(Store);
  const router = inject(Router);
  const billAcctId = Number(route.paramMap.get('id'));
  const customerId = store.selectSnapshot(CurrentState.customerId);
  if (!billAcctId) {
    router.navigate(['/my/account/information']);
    return of(false);
  }

  return store
    .dispatch([
      new InquireCustomerFinancialInfoAction(billAcctId),
      new AccountGetInquireBillingAccountInfos(customerId, null, true, true),
    ])
    .pipe(
      map(() => {
        return true;
      }),
      catchError(() => {
        router.navigate(['/my/account/information']);
        return of(false);
      }),
    );
};
