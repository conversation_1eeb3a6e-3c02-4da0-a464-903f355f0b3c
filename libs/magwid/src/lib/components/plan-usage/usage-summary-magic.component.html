@if (myPlans()?.length) {
  <eds-card [title]="'usageSummary' | translate" linkIcon="arrowRight">
    <widget-selective-usage-summary
      [showPlanSelection]="!lockedBillingAccount()"
      [planUsage]="currentUsage()"
      [(selectedPlan)]="selectedPlan"
      (loadMorePlans)="onLoadMorePlans()"
      [loadMoreEnabled]="showMoreEnabled()"
      [myProductList]="myPlans()"
      (onRefresh)="getUsageSummary(selectedPlan().product.billingAccountId)"
      [loading]="loading()"
    ></widget-selective-usage-summary>
    <magic-widget-interactions
      [level]="interactionsLevel()"
      [context]="selectedPlan().product"
    ></magic-widget-interactions>
  </eds-card>
} @else {
  <widget-empty-state [text]="'noActiveProducts' | translate"></widget-empty-state>
}
