import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import {
  CurrentState,
  MyProductsState,
  MyServicesGetProductDetailAction,
  MyServicesGetProductListAction,
  UsageSummaryGetUsageSummaryAction,
} from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { eBusinessFlow, eProduct } from '@libs/types';
import { myProductDetailResolverBase } from '@libs/magwid';

export const usageSummaryResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const billingAccountId = store.selectSnapshot(CurrentState.currentBillingAccountId);

  return [
    ...myProductDetailResolverBase(),
    {
      selector: store.selectSnapshot(MyProductsState.productList).products,
      action: () => {
        return new MyServicesGetProductListAction({
          customerId: store.selectSnapshot(CurrentState.customerId),
          statusCodes: [
            eProduct.ProductStatusShortCodes.ACTV,
            // eProduct.ProductStatusShortCodes.PNDG,
            eProduct.ProductStatusShortCodes.SPND,
            // eProduct.ProductStatusShortCodes.CNCL,
          ],
          planBundleSummary: 1,
          page: 0,
          size: 5,
        });
      },
      next: [
        {
          selector: false,
          action: () => {
            if (billingAccountId) {
              return [];
            }

            const firstBillingAccountId = store
              .selectSnapshot(MyProductsState.productList)
              .productBillingAccountIds?.find(Boolean);

            return new MyServicesGetProductDetailAction({
              customerId: store.selectSnapshot(CurrentState.customerId),
              billingAccountId: firstBillingAccountId,
              statusCodes: [
                eProduct.ProductStatusShortCodes.ACTV,
                eProduct.ProductStatusShortCodes.PNDG,
                eProduct.ProductStatusShortCodes.SPND,
                // eProduct.ProductStatusShortCodes.CNCL,
              ],
              productDetailList: 1,
              planBundleSummary: 1,
            });
          },
          next: [
            {
              selector: !billingAccountId,
              action: () => {
                return new UsageSummaryGetUsageSummaryAction({
                  billingAccountId,
                  customerId: store.selectSnapshot(CurrentState.customerId),
                  actionReasonCode: eBusinessFlow.Specification.DELIVERY_ORDER,
                });
              },
            },
          ],
        },
      ],
    },
  ];
};
