.catalog {
  padding: var(--eds-spacing-400) !important;
}

#order-button {
  display: grid;
  grid-template-columns: minmax(0, calc(var(--eds-size-multiplier) * 288));
  padding: var(--eds-spacing-400);
  transition: background-color 0.4s ease-in-out;

  &.sticky {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--eds-colors-surface-default);
  }

  eds-button {
    place-self: end;
  }

  .footer-spacer {
    display: none;
  }

  &.sticky + .footer-spacer {
    display: block;
    height: 72px;
  }
}
