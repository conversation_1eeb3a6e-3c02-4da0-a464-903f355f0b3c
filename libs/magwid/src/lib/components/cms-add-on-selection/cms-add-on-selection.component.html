<layout-cms-content-default>
  <section class="catalog">
    <widget-cms-plan-catalog
      [initialPlansCount]="3"
      [loadMoreCount]="15"
      [tabs]="tabs()"
      (onTabChange)="onTabChange($event)"
    ></widget-cms-plan-catalog>
  </section>
  @if (showOrderButton()) {
    <section id="order-button" [class.sticky]="!isFooterInView()">
      <eds-button class="order-now" [disabled]="!selectAddon()" (button-click)="orderNow()">
        {{ orderNowButtonText() | translate }}
      </eds-button>
    </section>
    <div class="footer-spacer"></div>
  }
</layout-cms-content-default>
