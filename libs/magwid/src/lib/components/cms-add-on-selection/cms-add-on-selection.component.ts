import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, effect, inject, input, output, signal } from '@angular/core';
import { FooterVisibilityService } from '../../services/footer-visibility.service';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import { Store } from '@ngxs/store';
import {
  CMSAddOnData,
  cmsGraphqlGetAddonsPayload,
  CmsPaginationService,
  CurrentState,
  injectCmsPagination,
  injectCmsSelectedOffer,
  KnownCmsPaginationSources,
  QuoteService,
} from '@libs/bss';
import { Cms, CmsLayoutContentDefaultComponent, CmsPlanCatalogComponent } from '@libs/widgets';
import { customerOrderIdResolver } from '../../../../../../apps/wsc/src/app/resolvers';
import { ActivatedRoute, Router } from '@angular/router';
import { CMS } from '@libs/types';
import { eBusinessFlow } from '@libs/types';

@Component({
  selector: 'magic-widget-cms-add-on-selection',
  templateUrl: './cms-add-on-selection.component.html',
  styleUrls: ['./cms-add-on-selection.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CmsLayoutContentDefaultComponent, CmsPlanCatalogComponent, TranslatePipe],
})
@MagicConfig({
  resolve: [customerOrderIdResolver],
})
export class CmsAddOnSelectionMagicComponent {
  private store = inject(Store);
  private router = inject(Router);
  private translateService = inject(TranslateService);
  private quoteService = inject(QuoteService);
  private cmsPaginationService = inject(CmsPaginationService);
  private activatedRoute = inject(ActivatedRoute);
  private footerVisibilityService = inject(FooterVisibilityService);
  addonResponse = injectCmsPagination(KnownCmsPaginationSources.AddOns);
  selectedOffer = injectCmsSelectedOffer();

  selectedAddonGroup = input<{ name: string }>();
  showOrderButton = input<boolean>(true);
  addonOffers = input<CMS.PaginationResult>();
  orderNowButtonText = input('orderNow');
  handleOrderNow = output();

  changedAddons = signal<{ id: number; quantity: number }[]>([]);
  selectAddon = signal(false);
  visibleTabs = computed<string[]>(() => {
    const addonGroup: string[] = Object.values(
      this.addonResponse()
        ?.filters()
        ?.find((tab: { id: string }) => tab.id === 'addonGroup')?.options ?? {},
    );
    return addonGroup.filter((tab: string) =>
      tab?.toLowerCase().includes(this.selectedOffer()?.saleType.toLowerCase()),
    );
  });

  isFooterInView = this.footerVisibilityService.isFooterInView;

  tabs = computed<Cms.PlansTab[]>(() => {
    return this.visibleTabs()?.map((tab: string) => {
      return {
        title: tab,
        plans: this.addonResponse()
          .results()
          .map((plan: unknown) => new CMSAddOnData(plan, { translateService: this.translateService }))
          .map((plan: CMSAddOnData) => ({
            ...plan.addon,
            actions: [
              {
                text: this.translateService.translate('selectAddOn'),
                appearance: 'primary',
                type: 'buttonWithCounter',
                counter: 0,
                action: (props: { id: number; counter: number }) => {
                  this.changedAddons.update((addons) => {
                    const existingAddon = addons.find((a) => a.id === plan.id);
                    if (existingAddon) {
                      existingAddon.quantity = props.counter;
                    } else {
                      return [...addons, { id: plan.id, quantity: props.counter }];
                    }
                    return addons;
                  });

                  this.selectAddon.set(this.changedAddons().reduce((total, addon) => total + addon.quantity, 0) > 0);
                },
              },
            ],
          })),
      } as Cms.PlansTab;
    });
  });

  constructor() {
    effect(() => {
      this.cmsPaginationService.addSource(KnownCmsPaginationSources.AddOns, {
        responseKey: 'data.viewAddonOffers',
        query: cmsGraphqlGetAddonsPayload(),
        initialState: this.addonOffers(),
        queryParams: {
          selectedOfferId: this.activatedRoute.snapshot.queryParams.selectedOfferId,
        },
        activeFilter: [{ key: 'addonGroup', value: this.selectedAddonGroup()?.name }],
      });
    });
    effect(() => {
      const activeFilterName = this.addonResponse().getActiveFilterByKey('addonGroup')?.value;
      if (activeFilterName && !this.visibleTabs().includes(activeFilterName as string)) {
        this.addonResponse()
          .setFilter({
            variables: {
              addonGroups: [
                this.addonResponse().getFilterId('addonGroup', this.visibleTabs().find(Boolean)).toString(),
              ],
            },
          })
          .subscribe(() => {
            this.addonResponse().setActiveFilterByKey('addonGroup', this.visibleTabs().find(Boolean));
          });
      }
    });
  }

  onTabChange(tabName: string) {
    this.addonResponse()
      .setFilter({ variables: { addonGroups: [this.addonResponse().getFilterId('addonGroup', tabName).toString()] } })
      .subscribe();
  }

  orderNow() {
    this.quoteService
      .addAddOn(
        this.changedAddons().map((addon) => ({
          productOfferId: addon.id,
          quantity: addon.quantity,
        })),
      )
      .subscribe(() => {
        this.router.navigate(['cart'], {
          queryParams: {
            flow: eBusinessFlow.Specification.PURCHASE_ADDON,
            customerOrderId: this.store.selectSnapshot(CurrentState.currentCustomerOrderId),
            selectedOfferId: this.activatedRoute.snapshot.queryParams.selectedOfferId,
          },
        });
        this.handleOrderNow.emit();
      });
  }
}
