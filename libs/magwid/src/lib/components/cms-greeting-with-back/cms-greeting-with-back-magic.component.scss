:host {
  --eds-page-layout-detail-display: var(--page-layout-detail-display, flex);
  --eds-page-layout-detail-align-items: var(--page-layout-detail-align-items, center);
  --eds-page-layout-detail-flex-direction: var(--page-layout-detail-flex-direction, column);
  --eds-page-layout-detail-padding-bottom: var(
    --page-layout-detail-padding-bottom,
    calc(var(--eds-size-multiplier) * 10)
  );
  --eds-page-layout-detail-padding-top: var(--page-layout-detail-padding-top, calc(var(--eds-size-multiplier) * 10));
  --eds-page-layout-detail-gap: var(--page-layout-detail-gap, var(--eds-spacing-800));

  --eds-page-layout-detail-heading-display: var(--page-layout-detail-heading-display, grid);
  --eds-page-layout-detail-heading-align-items: var(--page-layout-detail-heading-align-items, start);
  --eds-page-layout-detail-heading-grid-template: var(
    --page-layout-detail-heading-grid-template,
    none / minmax(0, calc(var(--eds-size-multiplier) * 190))
  );
  --eds-page-layout-detail-heading-gap: var(--page-layout-detail-heading-gap, var(--eds-spacing-800));

  --eds-page-layout-detail-page-heading-content-gap: var(
    --page-layout-detail-page-heading-content-gap,
    var(--eds-spacing-100)
  );
}

@media (max-width: 834px) {
  :host {
    --eds-page-layout-detail-gap: var(--page-layout-detail-gap, var(--eds-spacing-600));
    --eds-page-layout-detail-heading-gap: var(--page-layout-detail-heading-gap, var(--eds-spacing-600));
  }
}

@media (min-width: 1440px) {
  :host {
    --eds-page-layout-detail-heading-grid-template: var(
      --page-layout-default-heading-grid-template,
      none / 1fr calc(var(--eds-size-multiplier) * 190) 1fr
    );
  }
}

.base {
  position: relative;
  display: var(--eds-page-layout-detail-display);
  align-items: var(--eds-page-layout-detail-align-items);
  flex-direction: var(--eds-page-layout-detail-flex-direction);
  padding-bottom: var(--eds-page-layout-detail-padding-bottom);
  padding-top: var(--eds-page-layout-detail-padding-top);
  gap: var(--eds-page-layout-detail-gap);
  background-color: var(--eds-colors-body);
}

.heading {
  display: var(--eds-page-layout-detail-heading-display);
  align-items: var(--eds-page-layout-detail-heading-align-items);
  grid-template: var(--eds-page-layout-detail-heading-grid-template);
  gap: var(--eds-page-layout-detail-heading-gap);
}

widget-page-heading {
  --eds-page-heading-content-gap: var(--eds-page-layout-detail-page-heading-content-gap);
}
