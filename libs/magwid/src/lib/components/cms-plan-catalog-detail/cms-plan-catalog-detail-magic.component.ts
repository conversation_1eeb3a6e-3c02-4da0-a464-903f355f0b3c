import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { BusinessFlowInteractionService, CMSOfferData, CmsState } from '@libs/bss';
import {
  Cms,
  CmsPlanCatalogDetailComponent as WidgetCmsPlanCatalogDetailComponent,
  LayoutContentTopNavComponent,
  PageHeadingComponent,
} from '@libs/widgets';
import { select } from '@ngxs/store';
import { TranslateService } from '@libs/plugins';
import { Location } from '@angular/common';

@Component({
  selector: 'magic-widget-cms-plan-catalog-detail',
  templateUrl: './cms-plan-catalog-detail-magic.component.html',
  styleUrls: ['./cms-plan-catalog-detail-magic.component.scss'],
  imports: [WidgetCmsPlanCatalogDetailComponent, LayoutContentTopNavComponent, PageHeadingComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CmsPlanCatalogDetailMagicComponent {
  private location = inject(Location);
  private translateService = inject(TranslateService);
  private businessFlowInteractionService = inject(BusinessFlowInteractionService);

  pageOffer = select(CmsState.page);

  offer = computed(() => new CMSOfferData(this.pageOffer()?.data));

  advantages = computed<Cms.PlanAdvantageTile[]>(() => this.offer()?.getAdvantages(this.translateService));

  price = computed(() => this.offer()?.price ?? this.offer()?.discountPrice);

  commitment = computed<Cms.PlanCommitment[]>(() => {
    // TODO get variation prices
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.offer()?.variations?.map((variation: any) => {
      return {
        title: variation?.commitment?.name || 'No commitment',
        price: this.offer()?.price,
        discountPrice: this.offer()?.discountPrice,
      };
    });
  });

  advantagesTitle = 'What’s included';
  benefitsTitle = 'Benefits';
  informationTitle = 'Information';
  commitmentTitle = 'Commitment';
  priceTitle = 'Amount to be paid';
  orderButton = 'Order Now';
  appsTitle = 'Entertainment Apps';

  back() {
    this.location.back();
  }

  initialize() {
    this.businessFlowInteractionService.byodInitialize$(Number(this.offer().offerId)).subscribe();
  }
}
