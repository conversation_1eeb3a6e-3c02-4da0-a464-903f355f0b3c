import { TitleCasePipe } from '@angular/common';
import { Component, computed, inject } from '@angular/core';
import { Router } from '@angular/router';
import { UserState, AccountState } from '@libs/bss';
import { RouteDataService } from '@libs/core';
import { BrowserTitleService } from '@libs/plugins';
import { PageHeadingComponent as WidgetPageHeadingComponent } from '@libs/widgets';
import { eProduct } from '@libs/types';
import { select } from '@ngxs/store';

@Component({
  selector: 'magic-widget-page-heading',
  templateUrl: './page-heading-magic.component.html',
  imports: [WidgetPageHeadingComponent],
  providers: [TitleCasePipe],
})
export class PageHeadingMagicComponent {
  private browserTitleService = inject(BrowserTitleService);
  private routeDataService = inject(RouteDataService);
  private titleCasePipe: TitleCasePipe = inject(TitleCasePipe);
  private router = inject(Router);

  routeData = computed(() => this.routeDataService.data());
  billingAccounts = select(AccountState.billingAccountInfos);

  private loggedInUser = select(UserState.getLoggedInUser);

  private getPageContext() {
    const url = this.router.url;

    const match = url.match(/\/information\/(\d+)/);
    const billingAccountId = match ? match[1] : null;

    return {
      page: this.routeData()?.['page'],
      billingAccountId: billingAccountId,
    };
  }

  private getSelectedBillingAccount() {
    const { billingAccountId } = this.getPageContext();
    const accounts = this.billingAccounts();

    if (!billingAccountId || !accounts) {
      return null;
    }

    const selectedAccount = accounts.find((account) => account.billingAccountId?.toString() === billingAccountId);

    if (selectedAccount) {
      const result = {
        title: selectedAccount.accountName || '',
        subtitle: `#${billingAccountId}`,
        statusText: selectedAccount.accountStatus?.name || '',
        statusAppearance: selectedAccount.accountStatus?.shortCode || '',
      };
      return result;
    }

    return null;
  }

  private getPageSpecificData<T>(accountDetailHandler: () => T, defaultValue: T): T {
    const { page } = this.getPageContext();

    switch (page) {
      case 'accountInformationDetail':
        return accountDetailHandler();
      default:
        return defaultValue;
    }
  }

  title = computed(() => {
    const { page } = this.getPageContext();

    switch (page) {
      case 'accountManagement': {
        const userName = this.loggedInUser()?.name;
        return this.titleCasePipe.transform(userName) ?? '';
      }
      case 'accountInformationDetail': {
        const selectedAccount = this.getSelectedBillingAccount();
        return selectedAccount?.title || '';
      }
      default:
        return this.browserTitleService.title() ?? '';
    }
  });

  subtitle = computed(() => {
    return this.getPageSpecificData(() => {
      return `# ${this.getPageContext()?.billingAccountId}`;
    }, '');
  });

  statusText = computed(() => {
    return this.getPageSpecificData(() => {
      const selectedAccount = this.getSelectedBillingAccount();
      return selectedAccount?.statusText || '';
    }, '');
  });

  statusAppearance = computed(() => {
    return this.getPageSpecificData(() => {
      const selectedAccount = this.getSelectedBillingAccount();
      return selectedAccount?.statusAppearance as eProduct.ProductStatusShortCodes;
    }, null);
  });
}
