import { Component, computed, inject, model } from '@angular/core';
import { CmsState, MenuType, QuoteState, UserState } from '@libs/bss';
import { select } from '@ngxs/store';
import { ConfigState, MagicConfig } from '@libs/plugins';
import { CmsHeaderComponent as WidgetCmsHeaderComponent } from '@libs/widgets';
import { cmsHeaderResolver } from './cms-header.resolver';
import { ProfileMenuMagicComponent } from '../profile-status/profile-menu-magic.component';
import { SearchMagicComponent } from '../search/search-magic.component';
import { LanguagePreferenceMagicComponent } from '../language-preference/language-preference-magic.component';
import { FeatureFlagEnum } from '@libs/types';
import { Router } from '@angular/router';
import { MobileMenuMagicComponent } from '../mobile-menu/mobile-menu-magic.component';
import { FeatureFlagDirective } from '../../directives';

@Component({
  selector: 'magic-widget-cms-header',
  templateUrl: './cms-header-magic.component.html',
  imports: [
    WidgetCmsHeaderComponent,
    ProfileMenuMagicComponent,
    SearchMagicComponent,
    LanguagePreferenceMagicComponent,
    MobileMenuMagicComponent,
    FeatureFlagDirective,
  ],
})
@MagicConfig({
  resolve: [cmsHeaderResolver],
})
export class CmsHeaderMagicComponent {
  protected router = inject(Router);

  firstName = select(UserState.getFirstName);
  menu = select(CmsState.menu);
  shoppingCardQuote = select(QuoteState.quoteQueryResponse);
  configLogo = select(ConfigState.getDeep('theme.logoNew'));

  languagePreferences = model<boolean>(false);

  logoSrc = computed(() => (this.configLogo() as string) || 'assets/images/logo/logo.svg');
  headerMenu = computed(() => this.menu().get(MenuType.MAIN));

  protected quickLinkEnabled = select(ConfigState.isFeatureEnabled(FeatureFlagEnum.headerQuickLinks));

  protected userTypeMenuEnabled = select(ConfigState.isFeatureEnabled(FeatureFlagEnum.headerUserTypeMenu));
  protected readonly FeatureFlagEnum = FeatureFlagEnum;

  quickLinks = computed(() => {
    if (!this.quickLinkEnabled()) {
      return [];
    }

    return [
      {
        label: 'Fast transactions',
        href: '#',
      },
    ];
  });

  userTypeMenu = computed(() => {
    return this.userTypeMenuEnabled();
  });

  cartItemsCount = computed(() => {
    const query = (
      this.shoppingCardQuote() as {
        data: { count: number };
      }
    )?.data;

    return query?.count || 0;
  });

  navigateToCart() {
    if (this.cartItemsCount() > 0) {
      this.router.navigate([`cart`]);
    }
  }
}
