import { inject } from '@angular/core';
import { CmsGetMenuAction, CmsState, UserGetLoggedInUserAction, UserState } from '@libs/bss';
import { Store } from '@ngxs/store';
import { MagicResolverModel } from '@libs/plugins';
import { ResolveFn } from '@angular/router';

export const cmsHeaderResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: store.selectSnapshot(UserState.getLoggedInUser)?.name,
      action: () => {
        return new UserGetLoggedInUserAction();
      },
    },
    {
      selector: store.selectSnapshot(CmsState.menu)?.menus?.length,
      action: () => {
        return new CmsGetMenuAction();
      },
    },
  ];
};
