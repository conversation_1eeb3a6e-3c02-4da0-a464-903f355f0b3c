import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { ActivationESimCardComponent, ActivationESimQrCodeComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { BusinessFlowState, CurrentState, MyProductsState, ProductCharListData } from '@libs/bss';
import { eProduct } from '@libs/types';
import { activationESimMagicResolver } from './activation-esim-magic.resolver';
import { ConfigState, MagicConfig, ModalService, ModalSize, TranslateService } from '@libs/plugins';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'magic-widget-activation-esim',
  imports: [ActivationESimCardComponent],
  templateUrl: './activation-esim-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
@MagicConfig({
  resolve: [activationESimMagicResolver],
})
export class ActivationESimMagicComponent {
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private modalService = inject(ModalService);
  private translateService = inject(TranslateService);
  private store = inject(Store);

  products = select(MyProductsState.products);
  billingAccountId = select(CurrentState.currentBillingAccountId);
  productChar = computed(() => {
    const productChars = this.products().getProductDetailListByBillingAccountId(
      this.billingAccountId(),
    ).secondaryProductChars;
    return new ProductCharListData(productChars)?.find(eProduct.ProductConsts.ICCID);
  });

  businessFlow = select(BusinessFlowState.getBusinessFlow);
  qrCode = computed(() => {
    const qrValue = this.businessFlow()?.getAdditionalInfoValueByKey('qrCode');
    if (!qrValue) {
      return this.store.selectSnapshot(ConfigState.getDeep('url.app')) + '/public/e-sim-success'; // TODO temporary
    }
    return qrValue;
  });

  displayQrCode() {
    const modalRef = this.modalService.open(ActivationESimQrCodeComponent, {
      title: this.translateService.translate('activationESimQrCode'),
      size: ModalSize.SMALL,
      data: {
        qrCode: this.qrCode(),
        closeEvent: () => {
          modalRef.close();
          this.router.navigate(['success'], { relativeTo: this.route });
        },
      },
    });
  }
}
