import { Component, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { ConfigState, TranslateService } from '@libs/plugins';
import { ActionListComponent, ActionListItem } from '@libs/widgets';
import { Router } from '@angular/router';
import { select } from '@ngxs/store';
import { FeatureFlagEnum } from '@libs/types';

@Component({
  selector: 'magic-widget-discover',
  templateUrl: './discover-magic.component.html',
  imports: [ActionListComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class DiscoverMagicComponent {
  private translateService = inject(TranslateService);
  private router = inject(Router);
  private standaloneEnabled = select(ConfigState.isFeatureEnabled(FeatureFlagEnum.discoverBuyStandalone));

  titleText = input<string>(this.translateService.translate('discover'));

  actionListItems = input<ActionListItem[]>([
    {
      text: this.translateService.translate('subscribeNewPlan'),
      iconTrailing: 'arrowRight',
      onClick: () => {
        this.router.navigate(['/']);
      },
    },
    ...(this.standaloneEnabled()
      ? [
          {
            text: this.translateService.translate('buyDevice'),
            iconTrailing: 'arrowRight',
            onClick: () => {
              this.router.navigate(['/standalone-device-selection']);
            },
          },
        ]
      : []),
  ]);
}
