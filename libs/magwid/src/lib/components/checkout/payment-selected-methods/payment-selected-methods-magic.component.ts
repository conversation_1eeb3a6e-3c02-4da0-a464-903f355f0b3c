import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';
import { MagicConfig } from '@libs/plugins';
import {
  CheckoutCardComponent,
  PaymentMethodSelectionData,
  PaymentMethodSelectionTypes,
  PaymentSelectedMethodsComponent,
} from '@libs/widgets';
import { paymentSelectedMethodResolver } from './payment-selected-methods.resolver';
import { select } from '@ngxs/store';
import { PaymentState, QuoteState } from '@libs/bss';

@Component({
  selector: 'magic-widget-payment-selected-methods',
  templateUrl: './payment-selected-methods-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [PaymentSelectedMethodsComponent, CheckoutCardComponent],
})
@MagicConfig({
  resolve: [paymentSelectedMethodResolver],
})
export class PaymentSelectedMethodsMagicComponent {
  quote = select(QuoteState.quote);
  paymentMethods = select(PaymentState.customerPaymentMethods);
  isSelected = input<boolean>(false);
  isCheckoutView = input<boolean>(true);

  paymentMethodTitle = input<string>('');
  showEditButton = input<boolean>(false);
  editPaymentMethod = output<void>();

  selectedPaymentMethods = computed<Record<PaymentMethodSelectionTypes, PaymentMethodSelectionData>>(() => {
    const paymentMethod = this.paymentMethods()?.paymentMethods.find(
      (paymentMethod) => paymentMethod.paymentMethodId === Number(this.quote()?.paymentReference?.rowId),
    );

    return {
      [PaymentMethodSelectionTypes.AUTHORIZED]: paymentMethod?.paymentMethodId
        ? {
            type: paymentMethod?.paymentMethodType,
            id: paymentMethod?.paymentMethodId,
            data: paymentMethod,
          }
        : undefined,
      [PaymentMethodSelectionTypes.PAY_NOW]: undefined,
    };
  });

  hasPaymentMethods = computed(() => {
    return Object.values(this.selectedPaymentMethods()).filter(Boolean).length > 0;
  });
}
