@if (hasPaymentMethods()) {
  @if (isCheckoutView()) {
    <widget-checkout-card title="Payment" icon="creditCard" [isActive]="true">
      <widget-payment-selected-methods
        [savedPaymentMethods]="selectedPaymentMethods()"
        [isSelected]="isSelected()"
      ></widget-payment-selected-methods>
    </widget-checkout-card>
  } @else {
    <widget-payment-selected-methods
      [showEditButton]="showEditButton()"
      [savedPaymentMethods]="selectedPaymentMethods()"
      [paymentMethodTitle]="paymentMethodTitle()"
      [isSelected]="isSelected()"
      (editPaymentMethod)="editPaymentMethod.emit()"
    ></widget-payment-selected-methods>
  }
}
