import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import {
  CurrentState,
  PaymentGetCustomerPaymentMethodsAction,
  PaymentGetPaymentMethodTypesAction,
  PaymentState,
  QuoteGetQuoteAction,
  QuoteService,
  QuoteState,
} from '@libs/bss';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import { eBusinessFlow } from '@libs/types';
import { switchMap } from 'rxjs';

export const paymentMethodResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);
  const quoteService = inject(QuoteService);

  return [
    {
      selector: store.selectSnapshot(PaymentState.customerPaymentMethods)?.paymentMethods?.length > 0,
      action: () => {
        return new PaymentGetCustomerPaymentMethodsAction(customerId);
      },
      next: [
        {
          selector: false,
          action: () => {
            const quote = store.selectSnapshot(QuoteState.quote);

            if (
              quote?.currentBusinessFlowSpecShortCode === eBusinessFlow.Specification.MAIN_ORDER ||
              quote.paymentReference?.rowId
            ) {
              return [];
            }

            return quoteService
              .savePaymentMethods$(
                store.selectSnapshot(PaymentState.customerPaymentMethods)?.firstPapOrDefaultPaymentMethod
                  ?.paymentMethodId,
              )
              .pipe(
                switchMap(() =>
                  store.dispatch(
                    new QuoteGetQuoteAction({
                      customerOrderId: store.selectSnapshot(CurrentState.currentCustomerOrderId),
                      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PAYMENT_BILLING,
                    }),
                  ),
                ),
              );
          },
        },
      ],
    },
    {
      selector: store.selectSnapshot(PaymentState.paymentMethodTypes)?.length > 0,
      action: () => {
        return new PaymentGetPaymentMethodTypesAction(eBusinessFlow.Specification.REAL_SALE);
      },
    },
  ];
};
