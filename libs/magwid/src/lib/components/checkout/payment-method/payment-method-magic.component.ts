import { CurrencyPipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  input,
  model,
  output,
  signal,
} from '@angular/core';
import { Checkbox } from '@eds/components';
import {
  BusinessWorkflowStepService,
  CurrentState,
  PaymentService,
  PaymentState,
  QuoteGetQuoteAction,
  QuoteService,
  QuoteState,
} from '@libs/bss';
import { MagicConfig, ModalService, ModalSize, ToasterService, TranslatePipe, TranslateService } from '@libs/plugins';
import { eBusinessFlow, ePayment } from '@libs/types';
import {
  BankAccountFormComponent,
  BankAccountFormFields,
  CreditCardFormComponent,
  CreditCardFormFields,
  PaymentMethodComponent,
  PaymentMethodSelectionData,
  PaymentMethodSelectionTypes,
  SavedPaymentMethod,
} from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { switchMap } from 'rxjs';
import { PaymentSelectedMethodsMagicComponent } from '../payment-selected-methods/payment-selected-methods-magic.component';
import { paymentMethodResolver } from './payment-method.resolver';
import { CheckboxDirective } from '@libs/core';

@Component({
  selector: 'magic-widget-payment-method',
  templateUrl: './payment-method-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    PaymentMethodComponent,
    TranslatePipe,
    CurrencyPipe,
    PaymentSelectedMethodsMagicComponent,
    CheckboxDirective,
  ],
})
@MagicConfig({
  resolve: [paymentMethodResolver],
})
export class PaymentMethodMagicComponent {
  private modalService = inject(ModalService);
  private paymentService = inject(PaymentService);
  private quoteService = inject(QuoteService);
  private translateService = inject(TranslateService);
  private store = inject(Store);
  private businessWorkflowStepService = inject(BusinessWorkflowStepService);
  private toasterService = inject(ToasterService);

  showSelected = input<boolean>();
  showActionButtons = input<boolean>(true);
  showEditButton = input<boolean>(false);
  paymentMethodTitle = input<string>('');

  completed = output<boolean>();
  onSelectPaymentMethod = output<{ id: number }>();

  quotePriceDetail = select(QuoteState.priceDetail);
  paymentMethods = select(PaymentState.customerPaymentMethods);
  paymentMethodTypes = select(PaymentState.paymentMethodTypes);
  paymentMethodDetail = select(PaymentState.paymentMethodDetail);
  quote = select(QuoteState.quote);

  showDefaultPaymentMethod = signal(false);
  saved = signal(false);

  currentSelectedCreditCardId = model<number>();
  currentSelectedBankAccountId = model<number>();

  currentSelectedCreditCardIdForPayNow = model<number>();
  currentSelectedBankAccountIdForPayNow = model<number>();

  selectedPaymentMethods = signal<Record<PaymentMethodSelectionTypes, PaymentMethodSelectionData>>({
    [PaymentMethodSelectionTypes.AUTHORIZED]: undefined,
    [PaymentMethodSelectionTypes.PAY_NOW]: undefined,
  });

  disabled = computed(() => {
    const authorizedId = +this.selectedPaymentMethods()?.[PaymentMethodSelectionTypes.AUTHORIZED]?.id;
    const authorizedType =
      this.selectedPaymentMethods()?.[PaymentMethodSelectionTypes.AUTHORIZED]?.data?.paymentMethodType;
    const existAuthorizedMethod = this.findPaymentMethod(authorizedId, authorizedType);

    if (this.showDefaultPaymentMethod()) {
      const payNowId = +this.selectedPaymentMethods()?.[PaymentMethodSelectionTypes.PAY_NOW]?.id;
      const payNowType = this.selectedPaymentMethods()?.[PaymentMethodSelectionTypes.PAY_NOW]?.data?.paymentMethodType;
      const existPayNowMethod = this.findPaymentMethod(payNowId, payNowType);
      return !existAuthorizedMethod || !existPayNowMethod;
    }

    return !existAuthorizedMethod;
  });

  bankPaymentMethodType = computed(() => {
    return this.paymentMethodTypes()?.find((type) => type.shortCode === ePayment.PaymentMethodType.BANK_ACCT);
  });

  creditCardPaymentMethodType = computed(() => {
    return this.paymentMethodTypes()?.find((type) => type.shortCode === ePayment.PaymentMethodType.CREDIT_CARD);
  });

  savedCreditCards = computed<SavedPaymentMethod[]>(() => {
    return this.paymentMethods().mappedCreditCards(
      this.currentSelectedCreditCardId(),
      this.quote()?.paymentReference?.rowId,
    );
  });

  savedCreditCardsForPayNow = computed<SavedPaymentMethod[]>(() => {
    return this.paymentMethods().mappedCreditCards(
      this.currentSelectedCreditCardIdForPayNow(),
      this.quote()?.paymentReference?.rowId,
    );
  });

  savedBankAccounts = computed<SavedPaymentMethod[]>(() => {
    return this.paymentMethods().mappedBankAccounts(
      this.currentSelectedBankAccountId(),
      this.quote()?.paymentReference?.rowId,
    );
  });

  savedBankAccountsForPayNow = computed<SavedPaymentMethod[]>(() => {
    return this.paymentMethods().mappedBankAccounts(
      this.currentSelectedBankAccountIdForPayNow(),
      this.quote()?.paymentReference?.rowId,
    );
  });

  currentPaymentMethodSelectionType = model<PaymentMethodSelectionTypes>(PaymentMethodSelectionTypes.AUTHORIZED);

  selectedPaymentMethodType = computed(() => {
    const paymentMethodId = this.quote()?.paymentReference?.rowId;
    const paymentMethod = this.paymentMethods()?.findPaymentMethodById(paymentMethodId);
    const selectedPaymentMethodType = this.selectedPaymentMethods()?.[PaymentMethodSelectionTypes.AUTHORIZED]?.type;
    return selectedPaymentMethodType || paymentMethod?.paymentMethodType || ePayment.PaymentMethodType.CREDIT_CARD;
  });

  selectedPaymentMethodTypeForPayNow = computed(() => {
    const paymentMethodId = this.quote()?.paymentReference?.rowId;
    const paymentMethod = this.paymentMethods()?.findPaymentMethodById(paymentMethodId);
    const selectedPaymentMethodType = this.selectedPaymentMethods()?.[PaymentMethodSelectionTypes.PAY_NOW]?.type;
    return selectedPaymentMethodType || paymentMethod?.paymentMethodType || ePayment.PaymentMethodType.CREDIT_CARD;
  });

  PaymentMethodSelectionTypes = PaymentMethodSelectionTypes;

  defaultPaymentMethodSelection = computed(() => {
    return this.getDefaultPaymentMethod(PaymentMethodSelectionTypes.AUTHORIZED);
  });

  defaultPaymentMethodSelectionForPayNow = computed(() => {
    return this.getDefaultPaymentMethod(PaymentMethodSelectionTypes.PAY_NOW);
  });

  constructor() {
    effect(() => {
      if (this.quote()?.paymentReference?.rowId) {
        this.completed.emit(true);
      }
    });

    effect(() => {
      const paymentMethodId = Number(
        this.selectedPaymentMethods()?.pay_now?.id ?? this.selectedPaymentMethods()?.pre_authorized?.id,
      );
      if (!isNaN(paymentMethodId)) {
        this.onSelectPaymentMethod.emit({
          id: paymentMethodId,
        });
      }
    });

    effect(() => {
      if (this.quote()?.paymentReference?.rowId) {
        this.saved.set(this.showSelected());
      }
    });
  }

  findPaymentMethod(paymentMethodId: number, paymentMethodType: ePayment.PaymentMethodType) {
    if (paymentMethodType === ePayment.PaymentMethodType.BANK_ACCT) {
      return this.paymentMethods().bankAccounts.find((account) => account.paymentMethodId === paymentMethodId);
    } else {
      return this.paymentMethods().creditCards.find((card) => card.paymentMethodId === paymentMethodId);
    }
  }

  findPaymentMethodById(paymentMethodId: number) {
    return this.paymentMethods().paymentMethods.find(
      (paymentMethod) => paymentMethod.paymentMethodId === Number(paymentMethodId),
    );
  }

  createCreditCard(CreditCardFormFields?: CreditCardFormFields | void) {
    if (CreditCardFormFields) {
      this.paymentService
        .saveNewPaymentMethod$(this.creditCardPaymentMethodType().paymentMethodTypeId, CreditCardFormFields)
        .subscribe({
          next: () => {
            this.updateSelectedCreditCardAfterSave();
            this.showOperationSuccessToast();
          },
        });
      return;
    }

    return this.createCreditCardModal();
  }

  createBankAccount(bankAccountFormFields: BankAccountFormFields | void) {
    if (bankAccountFormFields) {
      return this.paymentService
        .saveNewPaymentMethod$(this.bankPaymentMethodType().paymentMethodTypeId, bankAccountFormFields)
        .subscribe({
          next: () => {
            this.updateSelectedBankAccountAfterSave();
            this.showOperationSuccessToast();
          },
        });
    }

    return this.createBankAccountModal();
  }

  removePaymentMethod$(paymentMethodId: string | number) {
    this.paymentService.removePaymentMethod$(Number(paymentMethodId)).subscribe({
      next: () => {
        this.showOperationSuccessToast();
      },
      error: () => {
        this.showOperationErrorToast();
      },
    });
  }

  onSelectionChange(type: PaymentMethodSelectionTypes, paymentMethodSelectionData: PaymentMethodSelectionData) {
    this.currentPaymentMethodSelectionType.set(type);
    if (type === PaymentMethodSelectionTypes.AUTHORIZED) {
      if (paymentMethodSelectionData.type === ePayment.PaymentMethodType.CREDIT_CARD) {
        this.currentSelectedCreditCardId.set(+paymentMethodSelectionData.id);
      } else {
        this.currentSelectedBankAccountId.set(+paymentMethodSelectionData.id);
      }
    } else {
      if (paymentMethodSelectionData.type === ePayment.PaymentMethodType.CREDIT_CARD) {
        this.currentSelectedCreditCardIdForPayNow.set(+paymentMethodSelectionData.id);
      } else {
        this.currentSelectedBankAccountIdForPayNow.set(+paymentMethodSelectionData.id);
      }
    }

    this.selectedPaymentMethods.update((state) => ({
      ...state,
      [type]: {
        ...paymentMethodSelectionData,
        data: this.findPaymentMethodById(+paymentMethodSelectionData.id),
      },
    }));
  }

  changeDefaultPaymentMethod(event: Event) {
    this.showDefaultPaymentMethod.set((event.target as Checkbox).checked);
  }

  onContinue() {
    this.businessWorkflowStepService.nextStep();
  }

  onSave() {
    this.quoteService
      .savePaymentMethods$(
        Number(this.selectedPaymentMethods()?.pay_now?.id ?? this.selectedPaymentMethods()?.pre_authorized?.id),
      )
      .pipe(
        switchMap(() =>
          this.store.dispatch(
            new QuoteGetQuoteAction({
              customerOrderId: this.store.selectSnapshot(CurrentState.currentCustomerOrderId),
              currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PAYMENT_BILLING,
            }),
          ),
        ),
      )
      .subscribe(() => {
        this.saved.set(true);
        this.onContinue();
      });
  }

  private createCreditCardModal() {
    const modalRef = this.modalService.open(CreditCardFormComponent, {
      title: this.translateService.translate('addCreditCard'),
      size: ModalSize.SMALL,
      data: {
        addCreditCard: (creditCardFormFields: CreditCardFormFields) => {
          this.paymentService
            .saveNewPaymentMethod$(this.creditCardPaymentMethodType().paymentMethodTypeId, creditCardFormFields)
            .subscribe({
              next: () => {
                this.updateSelectedCreditCardAfterSave();
                this.showOperationSuccessToast();
                modalRef.close();
              },
              error: () => {
                modalRef.close();
              },
            });
        },
      },
    });
  }

  private createBankAccountModal() {
    const modalRef = this.modalService.open(BankAccountFormComponent, {
      title: this.translateService.translate('addBankAccount'),
      size: ModalSize.SMALL,
      data: {
        addBankAccount: (bankAccountFormFields: BankAccountFormFields) => {
          this.paymentService
            .saveNewPaymentMethod$(this.bankPaymentMethodType().paymentMethodTypeId, bankAccountFormFields)
            .subscribe({
              next: () => {
                this.updateSelectedBankAccountAfterSave();
                this.showOperationSuccessToast();
                modalRef.close();
              },
              error: () => {
                modalRef.close();
              },
            });
        },
      },
    });
  }

  // Helper methods for standardized toast messages and actions
  private updateSelectedCreditCardAfterSave(): void {
    if (this.currentPaymentMethodSelectionType() === PaymentMethodSelectionTypes.AUTHORIZED) {
      this.currentSelectedCreditCardId.set(this.paymentMethodDetail().paymentMethodId);
    } else {
      this.currentSelectedCreditCardIdForPayNow.set(this.paymentMethodDetail().paymentMethodId);
    }
  }

  private updateSelectedBankAccountAfterSave(): void {
    if (this.currentPaymentMethodSelectionType() === PaymentMethodSelectionTypes.AUTHORIZED) {
      this.currentSelectedBankAccountId.set(this.paymentMethodDetail().paymentMethodId);
    } else {
      this.currentSelectedBankAccountIdForPayNow.set(this.paymentMethodDetail().paymentMethodId);
    }
  }

  private showOperationSuccessToast(): void {
    this.toasterService.success({
      title: this.translateService.translate('toast.SUCCESS_TITLE'),
      description: this.translateService.translate('toast.OPERATION_SUCCESS'),
    });
  }

  private showOperationErrorToast(): void {
    this.toasterService.error({
      title: this.translateService.translate('toast.ERROR_TITLE'),
      description: this.translateService.translate('toast.BAD_REQUEST'),
    });
  }

  private getDefaultPaymentMethod(paymentMethodSelectionType: PaymentMethodSelectionTypes) {
    const selected = this.selectedPaymentMethods()?.[paymentMethodSelectionType];
    const selectedType = this.selectedPaymentMethods()?.[paymentMethodSelectionType]?.type;

    if (!selected?.id && !selectedType) {
      const cards = this.savedCreditCards();
      const accounts = this.savedBankAccounts();

      const hasNoSavedMethods = !cards?.length && !accounts?.length;
      if (hasNoSavedMethods) {
        return {
          id: undefined,
          type: ePayment.PaymentMethodType.CREDIT_CARD,
        } as PaymentMethodSelectionData;
      }

      const defaultCreditCard = cards.find((card) => card.default);
      if (defaultCreditCard) {
        return {
          id: defaultCreditCard.id,
          type: ePayment.PaymentMethodType.CREDIT_CARD,
          data: this.findPaymentMethodById(+defaultCreditCard.id),
        } as PaymentMethodSelectionData;
      }

      const defaultBankAccount = accounts.find((account) => account.default);
      if (defaultBankAccount) {
        return {
          id: defaultBankAccount.id,
          type: ePayment.PaymentMethodType.BANK_ACCT,
          data: this.findPaymentMethodById(+defaultBankAccount.id),
        } as PaymentMethodSelectionData;
      }
    }

    return undefined;
  }
}
