import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  input,
  model,
  signal,
  untracked,
} from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  CurrentState,
  LovState,
  LovSearchMsisdnAction,
  OfferState,
  PhoneNumberConfigurationService,
  PlanData,
  QuoteState,
  SearchResourceQualificationInfoData,
  ValidateSimCardAction,
} from '@libs/bss';
import { RadioGroupDirective } from '@libs/core';
import { MagicConfig, ToasterService, TranslatePipe } from '@libs/plugins';
import { eCommon, eProduct, HaveSimCardEnum, OfferInstanceKeyEnum, ValidateSimCard } from '@libs/types';
import { EnterSimDetailsComponent, Radio, TitledSectionComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { of, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { NewNumberMagicComponent } from '../new-number-magic/new-number-magic.component';
import { EnterSimDetailsResolver } from './enter-sim-details.resolver';

@Component({
  selector: 'magic-widget-enter-sim-details',
  imports: [
    EnterSimDetailsComponent,
    TitledSectionComponent,
    NewNumberMagicComponent,
    RadioGroupDirective,
    TranslatePipe,
  ],
  templateUrl: './enter-sim-details-magic.component.html',
  styleUrl: './enter-sim-details-magic.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [EnterSimDetailsResolver],
})
export class EnterSimDetailsMagicComponent {
  protected store = inject(Store);
  protected phoneNumberConfigurationService = inject(PhoneNumberConfigurationService);
  protected toasterService = inject(ToasterService);
  simCardOfferCatalog = select(OfferState.simCardOfferCatalog);

  activePlan = input<PlanData>(null);
  validatedPhoneNumber = model<string>(null);
  validatedSimType = model<number>(null);
  selectedSimMethod = input<HaveSimCardEnum>(null);

  existingIccid = computed(() =>
    this.activePlan()?.findOfferInstancesChar(OfferInstanceKeyEnum.BYOD_SIM, eProduct.ProductConsts.ICCID),
  );
  existingMsisdnNumber = computed(() => this.activePlan()?.findMsisdnNumber());
  phoneValidationStatus = signal('');

  simCardOfferCatalogs = computed<Radio[]>(() => {
    const catalog = this.simCardOfferCatalog();

    if (!catalog) {
      return [];
    }

    return catalog.getNonByodOffers().map((offer) => ({
      id: `sim_${offer.offerId}`,
      name: `sim_${offer.offerId}`,
      value: offer.offerId.toString(),
      label: offer.offerName,
      isChecked: offer.offerId === this.validatedSimType(),
      isDisabled: false,
    }));
  });

  readonly SIM_METHOD = HaveSimCardEnum;

  simForm = computed(() => {
    if (this.selectedSimMethod()) {
    }

    return new FormGroup({
      iccid: new FormControl('89148222', [Validators.required, Validators.minLength(20)]),
    });
  });

  constructor() {
    effect(() => {
      if (this.existingMsisdnNumber()) {
        this.validatedPhoneNumber.set(this.existingMsisdnNumber());
      }
    });

    effect(() => {
      if (this.existingIccid()) {
        untracked(this.simForm).controls.iccid.setValue(this.existingIccid());
        this.phoneValidationStatus.set('success');
        this.validatedPhoneNumber.set(this.existingMsisdnNumber());
      }
    });
  }

  onIccidChange(number: string = null) {
    this.phoneValidationStatus.set(null);

    if (!this.simForm().valid) {
      return;
    }
    this.validateMsisdn(number || this.simForm().value?.iccid);
  }

  simTypeChange(event: Event) {
    const eventTargetValue = Number((event.target as HTMLInputElement).value);
    if (!eventTargetValue) {
      return;
    }
    if (this.validatedSimType() !== eventTargetValue) {
      this.assignANumber();
    }
    this.validatedSimType.set(eventTargetValue);
  }

  private validateMsisdn(number: string) {
    const byodSim = this.store.selectSnapshot(OfferState.simCardOfferCatalog).getByodOffer();

    const request: ValidateSimCard.Request = {
      simCardOfferId: byodSim?.offerId,
      charVal: number,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      customerOrderId: this.store.selectSnapshot(CurrentState.currentCustomerOrderId),
      charShortCode: eCommon.ProductCharType.ICCID,
    };

    this.store
      .dispatch(new ValidateSimCardAction(request))
      .pipe(
        map(() => this.store.selectSnapshot(QuoteState.validateSimCardResponse)),
        catchError(({ error }) => {
          this.phoneValidationStatus.set('fail');
          return throwError(error);
        }),
      )
      .subscribe((response) => {
        if (!response) {
          return;
        }

        this.customSearchMsisdn().subscribe((msisdnList: SearchResourceQualificationInfoData) => {
          this.phoneValidationStatus.set('success');
          this.validatedSimType.set(byodSim.offerId);
          this.assignFirstNumber(msisdnList);
        });
      });
  }

  private customSearchMsisdn() {
    const request = {
      capacityDemandAmount: 1,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      pattern: '...',
      resourceValueOffset: 0,
    };

    return this.store
      .dispatch(new LovSearchMsisdnAction(request))
      .pipe(map(() => this.store.selectSnapshot(LovState.misdnList)))
      .pipe(
        catchError(({ error }) => {
          this.toasterService.error({
            title: 'Error',
            description: error.message ?? error.detail,
          });

          return of(null);
        }),
      );
  }

  protected assignANumber() {
    this.phoneNumberConfigurationService.searchRandomNumbers$().subscribe((msisdnList) => {
      this.assignFirstNumber(msisdnList);
    });
  }

  protected assignFirstNumber(msisdnList: SearchResourceQualificationInfoData) {
    const selectedNumber = msisdnList?.firsItem.msisdn;
    this.validatedPhoneNumber.set(selectedNumber);
  }
}
