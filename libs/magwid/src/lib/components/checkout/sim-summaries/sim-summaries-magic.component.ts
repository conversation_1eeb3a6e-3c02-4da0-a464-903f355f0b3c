import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';
import { select, Store } from '@ngxs/store';
import { QuoteState } from '@libs/bss';
import { DeliveryInvoiceSummaryCardComponent, SimSummaryCardMagicComponent } from '@libs/magwid';
import { CheckoutCardComponent, SectionComponent, SeperatedComponent, TitledSectionComponent } from '@libs/widgets';
import { TranslatePipe } from '@libs/plugins';
import { AbstractOfferInstance } from '@libs/types';

@Component({
  selector: 'magic-widget-sim-summaries',
  imports: [
    SimSummaryCardMagicComponent,
    CheckoutCardComponent,
    SeperatedComponent,
    SectionComponent,
    TitledSectionComponent,
    TranslatePipe,
    DeliveryInvoiceSummaryCardComponent,
  ],
  templateUrl: './sim-summaries-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SimSummariesMagicComponent {
  store = inject(Store);
  quote = select(QuoteState.quote);
  isOrderSummary = input(false);
  plans = computed(() => {
    return this.quote()?.rawPlans;
  });

  hasPlans = computed(() => {
    return this.plans()?.some((plan) => plan.plan) ?? false;
  });

  checkEsimOfferInstance(offerInstance: Record<string, AbstractOfferInstance[]>) {
    return offerInstance?.esim?.length > 0;
  }
}
