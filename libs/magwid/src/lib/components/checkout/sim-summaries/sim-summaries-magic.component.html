@if (hasPlans()) {
  <widget-checkout-card title="Mobile" icon="simCard" [itemAmount]="plans()?.length" [isActive]="true">
    <widget-seperated>
      @for (plan of plans(); track $index) {
        <widget-section padding="none">
          <magic-widget-sim-summary-card
            [orderItemId]="plan?.plan.customerOrderItemId"
            [isOrderSummary]="isOrderSummary()"
            [completed]="true"
          ></magic-widget-sim-summary-card>
          <widget-titled-section [sectionTitle]="'deliveryInvoice' | translate">
            <magic-widget-delivery-invoice-summary-card
              [showDeliveryInfo]="!checkEsimOfferInstance(plan.plan.offerInstances)"
              [customerOrderItemId]="plan?.plan.customerOrderItemId"
            ></magic-widget-delivery-invoice-summary-card>
          </widget-titled-section>
        </widget-section>
      }
    </widget-seperated>
  </widget-checkout-card>
}
