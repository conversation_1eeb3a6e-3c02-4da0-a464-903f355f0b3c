<div class="sim-card-selection">
  <widget-titled-section [sectionTitle]="'selectSimCard' | translate">
    <eds-radio-group name="sim" (radio-change)="valueChange($event)">
      @for (char of selectionOptions(); track $index) {
        <eds-radio
          [id]="char.id"
          [value]="char.value"
          [name]="char.name"
          [isChecked]="char.isChecked"
          [isDisabled]="char.isDisabled"
        >
          <label [for]="char.id">{{ char.label | translate }}</label>
        </eds-radio>
      }
    </eds-radio-group>
  </widget-titled-section>
  <magic-widget-enter-sim-details
    [activePlan]="activePlan()"
    [selectedSimMethod]="selectedSimSelection()"
    [(validatedPhoneNumber)]="validatedNumber"
    [(validatedSimType)]="validatedSimType"
  ></magic-widget-enter-sim-details>
</div>
