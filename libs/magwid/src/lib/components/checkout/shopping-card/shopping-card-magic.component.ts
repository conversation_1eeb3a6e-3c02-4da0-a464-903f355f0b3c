import { ChangeDetectionStrategy, Component, computed, inject, input, output } from '@angular/core';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import { ShoppingCardComponent, ShoppingCardItem, ShoppingCardTotal, SummaryCardComponent } from '@libs/widgets';
import { select } from '@ngxs/store';
import { BusinessWorkflowStepService, QuoteState } from '@libs/bss';
import { CurrencyPipe } from '@angular/common';
import { eBusinessFlow } from '@libs/types';
import { shoppingCardResolver } from './shopping-card.resolver';

@Component({
  selector: 'magic-widget-shopping-card',
  imports: [ShoppingCardComponent, SummaryCardComponent, TranslatePipe],
  templateUrl: './shopping-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [CurrencyPipe],
})
@MagicConfig({
  resolve: [shoppingCardResolver],
})
export class ShoppingCardMagicComponent {
  private translateService = inject(TranslateService);
  private currencyPipe = inject(CurrencyPipe);
  protected businessWorkflowStepService = inject(BusinessWorkflowStepService);

  quotePriceDetail = select(QuoteState.priceDetail);

  actionButtonText = input<string>(this.translateService.translate('completeOrder'));
  actionButtonDisabled = computed(() => {
    const activeStep = this.businessWorkflowStepService.activeStep()?.shortCode;
    if (
      activeStep &&
      !this.businessWorkflowStepService.isInActiveStep([
        eBusinessFlow.WorkflowStateType.ORDER_REVIEW,
        eBusinessFlow.WorkflowStateType.PRE_VALIDATION,
        eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
      ])
    ) {
      return true;
    }

    return !this.quotePriceDetail().price.subscriptionDetailList?.length;
  });

  onAction = output();

  onButtonClick() {
    this.onAction.emit();
  }

  items = computed(() => {
    return this.buildItems([
      ...(this.quotePriceDetail().price.lateOutstandingBalance
        ? [
            {
              name: 'outstandingBalance',
              amount: this.quotePriceDetail().price.lateOutstandingBalance.totalAmount,
              class: '',
            },
          ]
        : []),
      {
        name: 'paidInFirstInvoice',
        amount: this.quotePriceDetail().price.firstInvoiceTotal.toString(),
        class: 'highlight',
      },
      {
        name: 'dueNow',
        amount: this.quotePriceDetail().price.dueNowTotal,
        class: 'due',
      },
    ]);
  });

  total = computed(
    () =>
      ({
        key: this.translateService.translate('totalAmount'),
        amount: this.currencyPipe.transform(this.quotePriceDetail().price.total),
      }) as ShoppingCardTotal,
  );

  buildItems(items: ShoppingCardItem[]): ShoppingCardItem[] {
    return items.map((data) => ({
      name: this.translateService.translate(data.name),
      amount: this.currencyPipe.transform(data.amount),
      class: data.class,
    }));
  }
}
