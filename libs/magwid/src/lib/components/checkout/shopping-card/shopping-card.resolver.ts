import { inject } from '@angular/core';
import { CurrentState, GetQuotePriceDetailAction, QuoteGetQuoteAction, QuoteState } from '@libs/bss';
import { Store } from '@ngxs/store';
import { MagicResolverModel } from '@libs/plugins';
import { ResolveFn } from '@angular/router';
import { eBusinessFlow } from '@libs/types';

export const shoppingCardResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);

  return [
    {
      selector: false,
      action: () => {
        return [
          new QuoteGetQuoteAction({
            customerOrderId,
            currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.CART_SUMMARY,
          }),
        ];
      },
      next: [
        {
          selector: () => {
            const customerId = store.selectSnapshot(CurrentState.customerId);
            // anonymous cart check
            return (
              store.selectSnapshot(QuoteState.quote)?.quote.customer?.id !== customerId ||
              store.selectSnapshot(QuoteState.priceDetail)
            );
          },
          action: () => {
            return new GetQuotePriceDetailAction({
              customerOrderId: store.selectSnapshot(CurrentState.currentCustomerOrderId),
            });
          },
        },
      ],
    },
  ];
};
