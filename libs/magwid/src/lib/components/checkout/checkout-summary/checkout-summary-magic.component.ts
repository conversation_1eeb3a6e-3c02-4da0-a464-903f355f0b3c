import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { select, Store } from '@ngxs/store';
import { PaymentState, QuoteState } from '@libs/bss';
import { SimSummariesMagicComponent } from '../sim-summaries/sim-summaries-magic.component';
import { PaymentSelectedMethodsMagicComponent } from '../payment-selected-methods/payment-selected-methods-magic.component';
import { SectionComponent } from '@libs/widgets';
import { ReadonlyAddonSummaryMagicComponent } from '@libs/magwid';

@Component({
  selector: 'magic-widget-checkout-summary',
  templateUrl: './checkout-summary-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    SimSummariesMagicComponent,
    PaymentSelectedMethodsMagicComponent,
    SectionComponent,
    ReadonlyAddonSummaryMagicComponent,
  ],
})
export class CheckoutSummaryMagicComponent {
  store = inject(Store);
  quote = select(QuoteState.quote);
  quotePrice = select(QuoteState.priceDetail);

  addOns = computed(() => {
    return this.quotePrice()?.addOnPriceListWithCalculatedPrice;
  });

  paymentMethods = select(PaymentState.customerPaymentMethods);

  showPaymentMethods = computed(() => {
    return this.paymentMethods()?.paymentMethods.find(
      (paymentMethod) => paymentMethod.paymentMethodId === Number(this.quote()?.paymentReference?.rowId),
    );
  });
}
