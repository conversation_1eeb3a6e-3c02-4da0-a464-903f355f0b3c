import { ChangeDetectionStrategy, Component, output, signal } from '@angular/core';
import { Radio, SpecifyPreferenceComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-delivery-address-preference',
  templateUrl: './delivery-address-preference-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SpecifyPreferenceComponent],
})
export class DeliveryAddressPreferenceMagicComponent {
  addressPreferences = signal<Radio[]>([
    {
      id: '1',
      name: 'Use the same delivery address for all items',
      value: '1',
      label: 'Use the same delivery address for all items',
      isChecked: true,
      isDisabled: false,
    },
    {
      id: '2',
      name: 'Use a different delivery address for each item',
      value: '2',
      label: 'Use a different delivery address for each item',
      isChecked: false,
      isDisabled: true,
    },
  ]);

  selectAddressPreference = output<string>();
}
