import { ChangeDetectionStrategy, Component, computed, inject, input, output } from '@angular/core';
import { CurrentState, CustomerCreateAddressAction, CustomerGetAddressListAction, CustomerState } from '@libs/bss';
import { injectDestroy } from '@libs/core';
import { MagicConfig, ModalService, ModalSize, TranslateService } from '@libs/plugins';
import { Address, AddressType, CreateAddressWrapper } from '@libs/types';
import { AddressModel, SelectAddressComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { concatMap, takeUntil } from 'rxjs/operators';
import { AddNewAddressModalComponent } from '../../../modals/add-new-address-modal/add-new-address-modal.component';
import { deliveryAddressSelectionResolver } from './delivery-address-selection.resolver';

@Component({
  selector: 'magic-widget-delivery-address-selection',
  templateUrl: './delivery-address-selection-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SelectAddressComponent],
})
@MagicConfig({
  resolve: [deliveryAddressSelectionResolver],
})
export class DeliveryAddressSelectionMagicComponent {
  private modalService = inject(ModalService);
  private translateService = inject(TranslateService);
  private store = inject(Store);
  private destroy$ = injectDestroy();

  addressProp = input<AddressModel>({
    icon: 'plusCircle',
    buttonText: 'Add delivery address',
    buttonIcon: 'plusCircle',
    title: this.translateService.translate('selectDeliveryAddress'),
    selectLabel: 'Your delivery address',
  });

  emptyAddress = input<AddressModel>({
    icon: 'plusCircle',
    buttonText: 'Add Address',
    buttonIcon: 'plusCircle',
    title: 'No saved address found',
    description: 'Add your delivery address to complete your order easily.',
  });

  currentAddress = input<Address>(null);

  selectAddress = output<Address>();

  deliveryAddressList = select(CustomerState.getAddressList);

  mappedDeliveryAddresses = computed(
    () => {
      return this.deliveryAddressList().customerAddressLovOptions(this.currentAddress());
    },
    {
      equal: (a, b) => a.length === b.length,
    },
  );

  handleSelectAddress(addressId: string) {
    const address = this.deliveryAddressList().findCustomerAddressById(Number(addressId));
    this.selectAddress.emit(address);
  }

  openAddAddressModal() {
    const modalService = this.modalService.open(AddNewAddressModalComponent, {
      title: this.translateService.translate('addDeliveryAddress'),
      size: ModalSize.MEDIUM,
      data: {
        addressType: AddressType.Shipment,
        addAddress: (address: CreateAddressWrapper) => {
          this.store
            .dispatch(new CustomerCreateAddressAction(address))
            .pipe(
              takeUntil(this.destroy$),
              concatMap(() =>
                this.store.dispatch(
                  new CustomerGetAddressListAction(this.store.selectSnapshot(CurrentState.customerId)),
                ),
              ),
              concatMap(() => this.store.selectOnce(CustomerState.newAddress)),
            )
            .subscribe((newAddress) => {
              const address = newAddress.addressList[0];
              this.selectAddress.emit(address);
              modalService.close();
            });
        },
      },
    });
  }
}
