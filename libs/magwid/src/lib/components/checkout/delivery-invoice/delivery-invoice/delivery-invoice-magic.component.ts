import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import {
  BusinessWorkflowStepService,
  CurrentState,
  GetQuotePriceDetailAction,
  QuoteGetDeliverInstallationRetrieveConfigAction,
  QuoteService,
  QuoteState,
  SetBillingAddressAction,
  SetNewInvoiceAddressAction,
} from '@libs/bss';
import { injectDestroy } from '@libs/core';
import { MagicConfig, ToasterService, TranslatePipe, TranslateService } from '@libs/plugins';
import { Address, eBusinessFlow, InquireDeliverMethodRequest, OfferInstanceKeyEnum } from '@libs/types';
import { SectionComponent, SeperatedComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { concatMap, takeUntil } from 'rxjs';
import { getQuoteResolver } from '../../../../resolvers/get-quote.resolver';
import { DeliveryInvoiceSummaryCardComponent } from '../../../delivery-invoice-summary-card/delivery-invoice-summary-card.component';
import { BillingAddressSelectionMagicComponent } from '../billing-address-selection/billing-address-selection-magic.component';
import { DeliveryAddressPreferenceMagicComponent } from '../delivery-address-preference/delivery-address-preference-magic.component';
import { DeliveryAddressSelectionMagicComponent } from '../delivery-address-selection/delivery-address-selection-magic.component';
import { DeliveryBasketMagicComponent } from '../delivery-basket/delivery-basket-magic.component';
import { DeliveryMethodSelectionMagicComponent } from '../delivery-method-selection/delivery-method-selection-magic.component';
import { DeliveryOptionSelectionMagicComponent } from '../delivery-option-selection/delivery-option-selection-magic.component';
import { InvoiceAddressSelectionMagicComponent } from '../invoice-address-selection/invoice-address-selection-magic.component';

@Component({
  selector: 'magic-widget-delivery-invoice',
  templateUrl: './delivery-invoice-magic.component.html',
  styleUrls: ['./delivery-invoice-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    DeliveryMethodSelectionMagicComponent,
    DeliveryAddressSelectionMagicComponent,
    InvoiceAddressSelectionMagicComponent,
    DeliveryOptionSelectionMagicComponent,
    DeliveryInvoiceSummaryCardComponent,
    DeliveryAddressPreferenceMagicComponent,
    BillingAddressSelectionMagicComponent,
    TranslatePipe,
    SeperatedComponent,
    SectionComponent,
    DeliveryBasketMagicComponent,
  ],
})
@MagicConfig({
  resolve: [getQuoteResolver],
})
export class DeliveryInvoiceMagicComponent implements OnInit {
  private store = inject(Store);
  private quoteService = inject(QuoteService);
  private destroy$ = injectDestroy();
  private toastr = inject(ToasterService);
  private translateService = inject(TranslateService);
  businessWorkflowStepService = inject(BusinessWorkflowStepService);

  quote = select(QuoteState.quote);

  saveAndContinueClick = signal<boolean>(false); // todo: temp solution for displaying summary-card

  plans = computed(() => {
    return this.quote()?.plans;
  });

  plan = computed(() => {
    return this.quote()?.plans[0];
  });

  basketItems = computed(() => {
    return this.quote()?.rawPlans.map(
      (plan) => plan.plan.offerName + (plan?.device ? ` + ${plan?.device.offerName}` : ''),
    );
  });

  deliveryInfo = computed(() => {
    const planDeliveryInfo = this.plan()?.deliveryInfo;
    const physicalDeliveryInfo = this.quote()?.getOfferInstanceOfBundleOffer(
      OfferInstanceKeyEnum.PHYSICAL_SIM,
    )?.deliveryInfo;

    return planDeliveryInfo || physicalDeliveryInfo;
  });

  deliveryMethodId = signal<number>(this.deliveryInfo()?.deliveryMethod?.deliveryMethodId);
  deliveryOptionId = signal<number>(this.deliveryInfo()?.deliveryMethod?.deliveryOfferId);
  deliveryAddress = signal<Address>(this.deliveryInfo()?.deliveryAddress);
  invoiceAddress = signal<Address>(this.quote().paymentInfoBillingAddress);
  billingAddress = signal<Address>(this.quote().paymentInfoBillingAddress);
  addressPreference = signal<string>('');
  isExistingInvoiceAddress = signal<boolean>(true);
  isSameInvoiceAccount = signal<boolean>(true);

  postpaidBundleOffers = computed(() => {
    return this.quote()?.postpaidBundleOffers;
  });

  defaultDeliveryAddressVisible = computed(() =>
    this.businessWorkflowStepService.hasStep(eBusinessFlow.WorkflowStateType.DEFAULT_DELIVERY_ADDRESS_SELECTION),
  );

  deliveryVisible = computed(() => {
    return this.businessWorkflowStepService.hasStep(eBusinessFlow.WorkflowStateType.DELIVERY);
  });

  invoiceVisible = this.businessWorkflowStepService.isShown(eBusinessFlow.WorkflowStateType.INVOICE_SELECT);
  billingAddressIsShown = this.businessWorkflowStepService.isShown(eBusinessFlow.WorkflowStateType.BILLING_ADDRESS);

  continueButtonDisabled = computed(() => {
    const isDeliveryInvalid =
      this.deliveryVisible() && (!this.deliveryMethodId() || !this.deliveryAddress() || !this.deliveryOptionId());

    const isInvoiceInvalid = this.postpaidBundleOffers().length > 0 && this.invoiceVisible() && !this.invoiceAddress();

    const isBillingInvalid =
      this.postpaidBundleOffers().length === 0 && this.billingAddressIsShown() && !this.billingAddress();

    return isDeliveryInvalid || isInvoiceInvalid || isBillingInvalid;
  });

  constructor() {
    effect(() => {
      this.checkHasInvoiceSelectStep();
    });
  }

  ngOnInit(): void {
    const billingAddress = this.quote().paymentInfoBillingAddress;
    if (billingAddress) {
      this.store.dispatch(new SetNewInvoiceAddressAction(billingAddress));
      this.store.dispatch(new SetBillingAddressAction(billingAddress));
    }
  }

  private checkHasInvoiceSelectStep() {
    if (!this.postpaidBundleOffers().length && this.invoiceVisible()) {
      this.businessWorkflowStepService.nextStep(eBusinessFlow.WorkflowStateType.INVOICE_SELECT);
    }
  }

  handleDeliveryMethod(methodId: number) {
    this.deliveryMethodId.set(methodId);
  }

  handleDeliveryAddress(address: Address) {
    this.deliveryAddress.set(address);
    this.callDeliveryOptionSelection();
    this.quoteService
      .updateDefaultDeliveryAddress$(this.deliveryAddress())
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.businessWorkflowStepService.nextStep(eBusinessFlow.WorkflowStateType.DEFAULT_DELIVERY_ADDRESS_SELECTION);
      });
  }

  handleInvoiceAddress(invoiceBillingAccountAddress: Address) {
    this.invoiceAddress.set(invoiceBillingAccountAddress);
    this.businessWorkflowStepService.nextStep(eBusinessFlow.WorkflowStateType.INVOICE_SELECT);
  }

  handleBillingAddress(billingAddress: Address) {
    this.billingAddress.set(billingAddress);
  }

  handleIsExistingInvoiceAddress(isExistingInvoiceAddress: boolean) {
    this.isExistingInvoiceAddress.set(isExistingInvoiceAddress);
  }

  handleDeliveryOption(optionId: number) {
    this.quoteService
      .updateDeliveryInformation$(optionId, this.deliveryMethodId(), this.deliveryAddress())
      .pipe(
        takeUntil(this.destroy$),
        concatMap(() => this.quoteService.callNextState(eBusinessFlow.WorkflowStateType.DELIVERY)),
        concatMap(() => this.quoteService.inquireQuote(eBusinessFlow.WorkflowStateType.DELIVERY)),
      )
      .subscribe({
        next: () => {
          this.deliveryOptionId.set(optionId);
          this.businessWorkflowStepService.nextStep(eBusinessFlow.WorkflowStateType.DELIVERY);
          if (this.quote().postpaidBundleOffers.length === 0) {
            this.businessWorkflowStepService.nextStep(eBusinessFlow.WorkflowStateType.INVOICE_SELECT);
          }
          this.store.dispatch(
            new GetQuotePriceDetailAction({
              customerOrderId: this.store.selectSnapshot(CurrentState.currentCustomerOrderId),
            }),
          );
        },
      });
  }

  handleAddressPreference(preference: string) {
    this.addressPreference.set(preference);
  }

  handleInvoicePreference(invoicePreference: string) {
    this.isSameInvoiceAccount.set(invoicePreference === '1');
  }

  saveAndContinue() {
    const onSuccess = () => {
      this.saveAndContinueClick.set(true);

      const customerOrderId = this.store.selectSnapshot(CurrentState.currentCustomerOrderId);
      this.store.dispatch(new GetQuotePriceDetailAction({ customerOrderId }));
    };

    if (this.quote().postpaidBundleOffers.length > 0) {
      this.saveCombined(onSuccess);
    } else {
      this.savePrepaid(onSuccess);
    }
  }

  edit() {
    this.saveAndContinueClick.set(false);
  }

  continueNextStep() {
    this.businessWorkflowStepService.nextStep();
  }

  private callDeliveryOptionSelection() {
    const customerOrderId = this.store.selectSnapshot(CurrentState.currentCustomerOrderId);
    const customerId = this.store.selectSnapshot(CurrentState.customerId);

    let request = {
      customerId,
      customerOrderId,
      deliveryAddress: this.deliveryAddress(),
    } as InquireDeliverMethodRequest;

    this.store.dispatch(new QuoteGetDeliverInstallationRetrieveConfigAction(request));
  }

  private saveCombined(onSuccess: () => void) {
    this.quoteService
      .updateInvoiceInformation$(this.invoiceAddress(), this.isExistingInvoiceAddress())
      .pipe(
        takeUntil(this.destroy$),
        concatMap(() =>
          this.quoteService.updateBillingAddress$(
            this.invoiceAddress(),
            this.isExistingInvoiceAddress(),
            this.isSameInvoiceAccount(),
          ),
        ),
        concatMap(() => this.quoteService.inquireQuote(eBusinessFlow.WorkflowStateType.BILLING_ADDRESS)),
      )
      .subscribe({
        next: onSuccess,
        error: (err) => {
          this.toastr.error({
            title: this.translateService.translate(`error.updateInvoiceAddress`),
            description: err.message ?? this.translateService.translate(`error.${err?.code}`),
          });
        },
      });
  }

  private savePrepaid(onSuccess: () => void) {
    this.quoteService
      .updateBillingAddress$(this.billingAddress(), false, false)
      .pipe(
        takeUntil(this.destroy$),
        concatMap(() => this.quoteService.inquireQuote(eBusinessFlow.WorkflowStateType.BILLING_ADDRESS)),
      )
      .subscribe({
        next: onSuccess,
        error: (err) => {
          this.toastr.error({
            title: this.translateService.translate(`error.updateDeliveryAddress`),
            description: err.message ?? this.translateService.translate(`error.${err?.code}`),
          });
        },
      });
  }
}
