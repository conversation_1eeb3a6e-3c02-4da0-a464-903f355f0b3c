import { ChangeDetectionStrategy, Component, computed, inject, input, output } from '@angular/core';
import { CustomerState, SetBillingAddressAction } from '@libs/bss';
import { MagicConfig, ModalService, ModalSize, TranslateService } from '@libs/plugins';
import { Address, AddressType, CreateAddressWrapper } from '@libs/types';
import { AddressModel, SectionComponent, SelectAddressComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { AddNewAddressModalComponent } from '../../../modals/add-new-address-modal/add-new-address-modal.component';
import { billingAddressSelectionResolver } from './billing-address-selection.resolver';

@Component({
  selector: 'magic-widget-billing-address-selection',
  templateUrl: './billing-address-selection-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SelectAddressComponent, SectionComponent],
})
@MagicConfig({
  resolve: [billingAddressSelectionResolver],
})
export class BillingAddressSelectionMagicComponent {
  private modalService = inject(ModalService);
  private translateService = inject(TranslateService);
  private store = inject(Store);

  addressProp = input<AddressModel>({
    icon: 'plusCircle',
    buttonText: 'Add billing address',
    buttonIcon: 'plusCircle',
    title: 'Select billing address',
    selectLabel: 'Your billing address',
  });

  emptyAddress = input<AddressModel>({
    icon: 'plusCircle',
    buttonText: 'Add billing address',
    buttonIcon: 'plusCircle',
    title: 'No saved billing address found',
    description: 'Add your billing address to complete your order easily.',
  });

  currentAddress = input<Address>(null);

  selectAddress = output<Address>();

  billingAddressList = select(CustomerState.getBillingAddressList);

  mappedBillingAddresses = computed(() => {
    return this.billingAddressList().billingAddressLovOptions(this.currentAddress()) ?? [];
  });

  deliveryAddressList = select(CustomerState.getAddressList);

  mappedDeliveryAddresses = computed(() => {
    return this.deliveryAddressList().customerAddressLovOptions(this.currentAddress());
  });

  handleSelectAddress(addressId: string) {
    const addressIdNumber = addressId === 'undefined' ? undefined : +addressId;
    const address = this.billingAddressList().findBillingAddressById(addressIdNumber);
    this.selectAddress.emit(address);
  }

  openAddAddressModal() {
    const modalRef = this.modalService.open(AddNewAddressModalComponent, {
      title: this.translateService.translate('addBillingAddress'),
      size: ModalSize.MEDIUM,
      data: {
        addressType: AddressType.Billing,
        addressList: this.mappedDeliveryAddresses(),
        deliveryAddressList: this.deliveryAddressList(),
        addAddress: (addressWrapper: CreateAddressWrapper) => {
          const newAddress = addressWrapper.addressList[0];
          if (newAddress) {
            this.store.dispatch(new SetBillingAddressAction({ ...newAddress, id: newAddress.rowId }));
            this.selectAddress.emit(newAddress);
          }
          modalRef.close();
        },
      },
    });
  }
}
