import { ResolveFn } from '@angular/router';
import { CurrentState, CustomerGetBillingAddressListAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';

export const billingAddressSelectionResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: false,
      action: () => {
        return new CustomerGetBillingAddressListAction(store.selectSnapshot(CurrentState.customerId));
      },
    },
  ];
};
