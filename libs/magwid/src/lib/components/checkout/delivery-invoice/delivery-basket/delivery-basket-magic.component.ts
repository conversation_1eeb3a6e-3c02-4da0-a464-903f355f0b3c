import { ChangeDetectionStrategy, Component, computed } from '@angular/core';
import { QuoteState } from '@libs/bss';
import { DeliveryBasketComponent } from '@libs/widgets';
import { select } from '@ngxs/store';

@Component({
  selector: 'magic-widget-delivery-basket',
  templateUrl: './delivery-basket-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [DeliveryBasketComponent],
})
export class DeliveryBasketMagicComponent {
  quote = select(QuoteState.quote);

  basketItems = computed(() => {
    return this.quote()?.rawPlans.map(
      (plan) => plan.plan.offerName + (plan?.device ? ` + ${plan?.device.offerName}` : ''),
    );
  });
}
