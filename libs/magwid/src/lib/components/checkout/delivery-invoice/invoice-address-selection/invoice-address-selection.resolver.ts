import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import {
  CurrentState,
  CustomerGetAddressListAction,
  CustomerState,
  GetIntegrationBillingAccountsAction,
} from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eBusinessFlow } from '@libs/types';
import { Store } from '@ngxs/store';

export const invoiceAddressSelectionResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: false,
      action: () => {
        return new GetIntegrationBillingAccountsAction(customerId, eBusinessFlow.Specification.REAL_SALE);
      },
    },
    {
      selector: store.selectSnapshot(CustomerState.getAddressList).items,
      action: () => {
        return new CustomerGetAddressListAction(store.selectSnapshot(CurrentState.customerId));
      },
    },
  ];
};
