import { ChangeDetectionStrategy, Component, computed, inject, input, output, signal } from '@angular/core';
import { AccountState, CustomerState, QuoteState, SetNewInvoiceAddressAction } from '@libs/bss';
import { MagicConfig, ModalService, ModalSize, TranslateService } from '@libs/plugins';
import { Address, AddressType, CreateAddressWrapper } from '@libs/types';
import {
  AddressModel,
  Radio,
  SectionComponent,
  SelectAddressComponent,
  SpecifyPreferenceComponent,
} from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { AddNewAddressModalComponent } from '../../../modals/add-new-address-modal/add-new-address-modal.component';
import { invoiceAddressSelectionResolver } from './invoice-address-selection.resolver';

@Component({
  selector: 'magic-widget-invoice-address-selection',
  templateUrl: './invoice-address-selection-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SelectAddressComponent, SpecifyPreferenceComponent, SectionComponent],
})
@MagicConfig({
  resolve: [invoiceAddressSelectionResolver],
})
export class InvoiceAddressSelectionMagicComponent {
  private modalService = inject(ModalService);
  private translateService = inject(TranslateService);
  private store = inject(Store);

  addressProp = input<AddressModel>({
    icon: 'plusCircle',
    buttonText: 'Add invoice account',
    buttonIcon: 'plusCircle',
    title: 'Select invoicing account',
    selectLabel: 'Your invoice account',
  });

  emptyAddress = input<AddressModel>({
    icon: 'plusCircle',
    buttonText: 'Add invoice account',
    buttonIcon: 'plusCircle',
    title: 'No saved invoice account found',
    description: 'Add your account to complete your order easily.',
  });

  currentAddress = input<Address>(null);

  invoicePreferences = signal<Radio[]>([
    {
      id: '1',
      name: 'Use the same invoice account for all items',
      value: '1',
      label: 'Use the same invoice account for all items',
      isChecked: true,
      isDisabled: false,
    },
    {
      id: '0',
      name: 'Use a different invoice account for each item',
      value: '0',
      label: 'Use a different invoice account for each item',
      isChecked: false,
      isDisabled: true,
    },
  ]);

  selectAddress = output<Address>();
  selectInvoicePreference = output<string>();
  isExistingInvoiceAddress = output<boolean>();

  billingAccountList = select(AccountState.integrationBillingAccounts);
  quote = select(QuoteState.quote);
  countryChangeKey = signal<number>(0);

  mappedInvoiceAddresses = computed(() => {
    return this.billingAccountList()?.getBillingAccountAddressLovOptions(this.currentAddress()) ?? [];
  });

  deliveryAddressList = select(CustomerState.getAddressList);

  mappedDeliveryAddresses = computed(() => {
    return this.deliveryAddressList().customerAddressLovOptions(this.currentAddress());
  });

  plans = computed(() => {
    return this.quote()?.plans;
  });

  handleSelectAddress(addressId: string) {
    const addressIdNumber = addressId === 'undefined' ? undefined : +addressId;
    const address = this.billingAccountList().findBillingAccountAddressByAddressId(addressIdNumber);
    this.selectAddress.emit(address);
  }

  handleInvoicePreference(invoicePreference: string) {
    this.selectInvoicePreference.emit(invoicePreference);
  }

  openAddAddressModal() {
    const modalRef = this.modalService.open(AddNewAddressModalComponent, {
      title: this.translateService.translate('addInvoiceAccount'),
      size: ModalSize.MEDIUM,
      data: {
        addressType: AddressType.Billing,
        addressList: this.mappedDeliveryAddresses(),
        deliveryAddressList: this.deliveryAddressList(),
        addAddress: (addressWrapper: CreateAddressWrapper) => {
          const newAddress = addressWrapper.addressList[0];
          if (newAddress) {
            this.store.dispatch(new SetNewInvoiceAddressAction(newAddress));
            this.selectAddress.emit(newAddress);
            this.isExistingInvoiceAddress.emit(false);
            this.countryChangeKey.update((key) => key + 1);
          }
          modalRef.close();
        },
      },
    });
  }
}
