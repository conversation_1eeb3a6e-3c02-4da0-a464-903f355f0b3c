<widget-section padding="none">
  @if (plans().length > 1 && false) {
    <!-- Disabled for now  -->
    <widget-specify-preference
      titleText="specifyInvoicePreference"
      [specifyPreferences]="invoicePreferences()"
      (radioChange)="handleInvoicePreference($event)"
    ></widget-specify-preference>
  }
  @for (key of [countryChangeKey()]; track key) {
    <widget-select-address
      [addressProp]="addressProp()"
      [emptyAddress]="emptyAddress()"
      [addressList]="mappedInvoiceAddresses()"
      (addAddress)="openAddAddressModal()"
      (selectAddress)="handleSelectAddress($event)"
    ></widget-select-address>
  }
</widget-section>
