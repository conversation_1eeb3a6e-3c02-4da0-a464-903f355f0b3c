import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { ActivationESimSuccessCardComponent } from '@libs/widgets';
import { select } from '@ngxs/store';
import { CurrentState, MyProductsState, ProductCharListData } from '@libs/bss';
import { eProduct } from '@libs/types';
import { activationEsimSuccessMagicResolver } from './activation-esim-success-magic.resolver';
import { MagicConfig } from '@libs/plugins';
import { Router } from '@angular/router';

@Component({
  selector: 'magic-widget-activation-esim-success',
  imports: [ActivationESimSuccessCardComponent],
  styleUrls: ['./activation-esim-success-magic.component.scss'],
  templateUrl: './activation-esim-success-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
@MagicConfig({
  resolve: [activationEsimSuccessMagicResolver],
})
export class ActivationESimSuccessMagicComponent {
  private router = inject(Router);

  products = select(MyProductsState.products);
  billingAccountId = select(CurrentState.currentBillingAccountId);

  productChar = computed(() => {
    const productChars = this.products().getProductDetailListByBillingAccountId(
      this.billingAccountId(),
    ).secondaryProductChars;
    return new ProductCharListData(productChars).find(eProduct.ProductConsts.ICCID);
  });

  goToHomepage() {
    this.router.navigate(['/my/overview']);
  }
}
