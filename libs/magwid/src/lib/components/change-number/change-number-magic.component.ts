import { ChangeDetectionStrategy, Component, computed, inject, OnInit, output, signal } from '@angular/core';
import { ChangeNumberFormComponent, FreeTextNumberForm } from '@libs/widgets';
import { MagicConfig } from '@libs/plugins';
import { changeNumberResolver } from './change-number.resolver';
import { select, Store } from '@ngxs/store';
import { LovGetNXXByNpaAction, LovState, PhoneNumberConfigurationService } from '@libs/bss';
import { SearchMsisdnRequest } from '@libs/types';
import { PhoneNumberPipe } from '@libs/core';

@Component({
  selector: 'magic-widget-change-number',
  templateUrl: './change-number-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ChangeNumberFormComponent],
  providers: [PhoneNumberPipe],
})
@MagicConfig({
  resolve: [changeNumberResolver],
})
export class ChangeMagicNumberComponent implements OnInit {
  private store = inject(Store);
  private phoneNumberConfigurationService = inject(PhoneNumberConfigurationService);
  private phoneNumberPipe = inject(PhoneNumberPipe);

  save = output<string>();

  npaItems = select(LovState.npaItems);
  npaSelectOptions = computed(() =>
    this.npaItems()?.options?.map((option) => {
      return {
        ...option,
        isSelected: option.value === this.npaItems().firsItem.val,
      };
    }),
  );

  nxxItems = select(LovState.nxxItems);
  nxxSelectOptions = computed(() =>
    this.nxxItems()?.options?.map((option) => {
      return {
        ...option,
        isSelected: option.value === this.nxxItems().firsItem.val,
      };
    }),
  );

  msisdnList = select(LovState.misdnList);
  msisdnOptions = computed(() => {
    return this.msisdnList()?.options?.map((option) => ({
      ...option,
      label: this.phoneNumberPipe.transform(option.value, {
        dash: false,
        bracketFormat: false,
      }),
    }));
  });

  selectedNumber = signal<string>('');

  ngOnInit() {
    if (!this.msisdnList().items?.length) {
      this.phoneNumberConfigurationService.searchRandomNumbers$().subscribe();
    }
  }

  getNxxItems(npa: string) {
    this.store.dispatch(new LovGetNXXByNpaAction(npa));
  }

  searchMsisdn(event: SearchMsisdnRequest) {
    return this.phoneNumberConfigurationService.searchMsisdn(event);
  }

  searchFreeTextNumber(event: FreeTextNumberForm) {
    return this.phoneNumberConfigurationService.searchFreeTextNumber(event);
  }

  onSave(event: string) {
    this.save.emit(event);
  }

  assignMsisdn(msisdn: string) {
    this.selectedNumber.set(msisdn);
  }
}
