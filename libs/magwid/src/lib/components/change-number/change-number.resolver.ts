import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import { LovGetNpaAction, LovGetNXXByNpaAction, LovState } from '@libs/bss';

export const changeNumberResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: store.selectSnapshot(LovState.npaItems)?.items?.length,
      action: () => {
        return new LovGetNpaAction();
      },
      next: [
        {
          selector: store.selectSnapshot(LovState.npaItems).items?.length,
          action: () => {
            return new LovGetNXXByNpaAction(store.selectSnapshot(LovState.npaItems)?.firsItem?.val);
          },
        },
      ],
    },
  ];
};
