import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { ActionListItem, ProfileMenuComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { UserState } from '@libs/bss';
import { LogoutAction, StartLoginAction } from '@libs/core';
import { Router } from '@angular/router';
import { ConfigState, TranslateService } from '@libs/plugins';
import { FeatureFlagEnum } from '@libs/types';

@Component({
  selector: 'magic-widget-profile-menu',
  imports: [ProfileMenuComponent],
  templateUrl: './profile-menu-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileMenuMagicComponent {
  private store = inject(Store);
  private router = inject(Router);
  private translateService = inject(TranslateService);

  firstName = select(UserState.getFirstName);
  protected loyaltyEnabled = select(ConfigState.isFeatureEnabled(FeatureFlagEnum.loyalty));
  protected activityFeedEnabled = select(ConfigState.isFeatureEnabled(FeatureFlagEnum.activityFeed));

  profileItems = computed<ActionListItem[]>(() => [
    {
      text: this.translateService.translate('overview'),
      iconLeading: 'dashboardSquare',
      onClick: () => this.handleNavigate('/my/overview'),
    },
    {
      text: this.translateService.translate('products'),
      iconLeading: 'propertyNew',
      onClick: () => this.handleNavigate('/my/products'),
    },
    {
      text: this.translateService.translate('bills'),
      iconLeading: 'invoice',
      onClick: () => this.handleNavigate('/my/bills'),
    },
    ...(this.loyaltyEnabled()
      ? [
          {
            text: this.translateService.translate('myEtiyaGiftClubs'),
            iconLeading: 'propertyNew',
            onClick: () => this.handleNavigate('/ext/loyalty'),
          },
        ]
      : []),
    {
      text: this.translateService.translate('account'),
      iconLeading: 'userSquare',
      onClick: () => this.handleNavigate('/my/account'),
    },
    {
      text: this.translateService.translate('myOrderAndQuotes'),
      iconLeading: 'shoppingBag',
      onClick: () => this.handleNavigate('/my/orders-and-quotes'),
    },
    ...(this.activityFeedEnabled()
      ? [
          {
            text: this.translateService.translate('activityFeed'),
            iconLeading: 'invoice',
            onClick: () => this.handleNavigate('/ext/activity-feed'),
          },
        ]
      : []),
    {
      text: this.translateService.translate('exit'),
      iconLeading: 'logout',
      onClick: () => this.handleLogout(),
    },
  ]);

  onLoginClick() {
    this.store.dispatch(new StartLoginAction());
  }

  protected handleNavigate(path: string): void {
    this.router.navigate([path], { onSameUrlNavigation: 'reload' });
  }

  protected handleLogout(): void {
    this.store.dispatch(new LogoutAction('/'));
  }
}
