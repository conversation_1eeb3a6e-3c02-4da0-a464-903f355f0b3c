import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { CurrentState, QuoteState, SetCurrentCustomerOrderItemIdAction } from '@libs/bss';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import { getQuoteResolver } from './get-quote.resolver';

export const CurrentOrderResolver: ResolveFn<MagicResolverModel[]> = (route: ActivatedRouteSnapshot) => {
  const store = inject(Store);
  const resolver = getQuoteResolver(route, null);
  if (!(resolver instanceof Array)) {
    return resolver;
  }
  resolver[0].next = [
    {
      selector: null,
      action: () => {
        const flowSpecCode = store.selectSnapshot(CurrentState.currentFlow);
        const customerOrderItemId = store.selectSnapshot(QuoteState.quote).lastCurrentOrderItemIdBy(flowSpecCode);
        return new SetCurrentCustomerOrderItemIdAction(customerOrderItemId);
      },
    },
  ];
  return resolver;
};
