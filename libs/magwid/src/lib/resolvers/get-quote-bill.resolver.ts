import { ResolveFn } from '@angular/router';
import { CurrentState, QuoteGetQuoteAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eBusinessFlow } from '@libs/types';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';

export const getQuoteBillResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);
  return [
    {
      selector: false,
      action: () => {
        return [
          new QuoteGetQuoteAction({
            customerOrderId,
            currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.BILL_PAYMENT,
          }),
        ];
      },
    },
  ];
};
