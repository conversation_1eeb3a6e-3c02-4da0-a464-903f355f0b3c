import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { CurrentState, GetInvoicingBillingAccount, QuoteState, SetCurrentCustomerOrderItemIdAction } from '@libs/bss';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import { getQuoteBillResolver } from './get-quote-bill.resolver';

export const CurrentBillOrderResolver: ResolveFn<MagicResolverModel[]> = (route: ActivatedRouteSnapshot) => {
  const store = inject(Store);
  const resolver = getQuoteBillResolver(route, null);
  if (!(resolver instanceof Array)) {
    return resolver;
  }
  resolver[0].next = [
    {
      selector: false,
      action: () => {
        const flowSpecCode = store.selectSnapshot(CurrentState.currentFlow);
        const customerOrderItemId = store.selectSnapshot(QuoteState.quote).lastCurrentOrderItemIdBy(flowSpecCode);
        return new SetCurrentCustomerOrderItemIdAction(customerOrderItemId);
      },
    },
    {
      selector: false,
      action: () => {
        return new GetInvoicingBillingAccount({
          billingAccountId: store.selectSnapshot(CurrentState.currentBillingAccountId),
          ...(store.selectSnapshot(CurrentState.currentInvoiceId) && {
            invoiceNumber: store.selectSnapshot(CurrentState.currentInvoiceId),
          }),
        });
      },
    },
  ];
  return resolver;
};
