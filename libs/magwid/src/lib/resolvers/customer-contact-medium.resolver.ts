import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import {
  CurrentState,
  CustomerState,
  GetApplicableInteractionsAction,
  InquireCustomerContactMediumAction,
  InquireCustomerSubscriptionsAction,
} from '@libs/bss';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import { eBusinessFlow, eCommon } from '@libs/types';

export const customerContactMediumResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: store.selectSnapshot(CustomerState.getContactMediumList)?.length,
      action: () => {
        return new InquireCustomerContactMediumAction({ customerId });
      },
    },
    {
      selector: store.selectSnapshot(CustomerState.getSubscriptionList)?.length,
      action: () => {
        return new InquireCustomerSubscriptionsAction({ customerId, dataTpId: eCommon.EntityDataType.CUST_ACCT });
      },
    },
    {
      selector: false,
      action: () => {
        return new GetApplicableInteractionsAction({
          level: eBusinessFlow.Levels.CUST_CONTACT_MEDIUM,
          customerId,
        });
      },
    },
  ];
};
