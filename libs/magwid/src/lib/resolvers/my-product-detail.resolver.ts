import { CurrentState, MyServicesGetProductDetailAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eProduct } from '@libs/types';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';

export const myProductDetailResolverBase = (billingAccountId: number = null): MagicResolverModel[] => {
  const store = inject(Store);

  if (!billingAccountId) {
    billingAccountId = store.selectSnapshot(CurrentState.currentBillingAccountId);
  }

  if (!billingAccountId) {
    return [];
  }

  // const products = store.selectSnapshot(MyServicesState.products).products;
  // const product = products?.find(item => item.billingAccountId === billingAccountId);

  return [
    {
      selector: false,
      // selector: product,
      action: () => {
        return new MyServicesGetProductDetailAction({
          customerId: store.selectSnapshot(CurrentState.customerId),
          billingAccountId,
          statusCodes: [
            eProduct.ProductStatusShortCodes.ACTV,
            eProduct.ProductStatusShortCodes.PNDG,
            eProduct.ProductStatusShortCodes.SPND,
            // eProduct.ProductStatusShortCodes.CNCL,
          ],
          productDetailList: 1,
          planBundleSummary: 1,
        });
      },
    },
  ];
};
