import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { CurrentState, GetBusinessWorkflowConfigAction } from '@libs/bss';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';

export const businessWorkflowConfigResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);

  return [
    {
      selector: false,
      action: () => {
        return new GetBusinessWorkflowConfigAction(customerOrderId);
      },
    },
  ];
};
