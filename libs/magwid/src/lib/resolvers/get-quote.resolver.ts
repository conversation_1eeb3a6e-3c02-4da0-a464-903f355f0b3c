import { ResolveFn } from '@angular/router';
import { CmsGetOffersAction, CurrentState, QuoteGetQuoteAction, QuoteState } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eBusinessFlow } from '@libs/types';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';

export const getQuoteResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);
  return [
    {
      selector: false,
      action: () => {
        return [
          new QuoteGetQuoteAction({
            customerOrderId,
            currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.CART_SUMMARY,
          }),
        ];
      },
      next: [
        {
          selector: false,
          action: () => {
            return new CmsGetOffersAction({
              variables: {
                offerIds: store.selectSnapshot(QuoteState.quote)?.bundleOfferIds,
              },
            });
          },
        },
      ],
    },
  ];
};
