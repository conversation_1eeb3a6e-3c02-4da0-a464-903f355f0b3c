import { effect, signal } from '@angular/core';
import { Alert } from '@libs/widgets';

export interface AmountValidationConfig {
  amount: () => number | null;
  openAmount: () => number | null;
  totalOpenAmount: () => number | null;
  hasInvoiceId: () => boolean;
}

export function useAmountAlert(config: AmountValidationConfig) {
  const alertDetail = signal<Alert>({});

  effect(
    () => {
      const amount = config.amount();
      const openAmount = config.openAmount();
      const totalOpenAmount = config.totalOpenAmount();
      const hasInvoiceId = config.hasInvoiceId();

      if (!amount || amount <= 0) {
        alertDetail.set({});
        return;
      }

      const isExactAmount = hasInvoiceId ? amount === (openAmount ?? 0) : amount === (totalOpenAmount ?? 0);

      if (isExactAmount) {
        alertDetail.set({});
        return;
      }

      const isUnderAmount = hasInvoiceId ? amount < openAmount : amount < totalOpenAmount;

      if (isUnderAmount) {
        alertDetail.set({
          appearance: 'info',
          iconName: 'informationCircle',
          title: 'underAmountInfoTitle',
          description: 'underAmountInfoDescription',
        });
      } else {
        alertDetail.set({
          appearance: 'error',
          iconName: 'alertCircle',
          title: 'overAmountWarningTitle',
          description: 'overAmountWarningDescription',
        });
      }
    },
    { allowSignalWrites: true },
  );

  return {
    alertDetail: alertDetail.asReadonly(),
  };
}
