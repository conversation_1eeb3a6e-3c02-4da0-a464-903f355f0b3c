import { CheckoutStep } from '@libs/widgets';
import { eBusinessFlow } from '@libs/types';
import { BillPaymentConfigurationMagicComponent, BillSummaryMagicComponent } from '../components';

export const billCheckoutSteps: CheckoutStep[] = [
  {
    path: '/my/bills/checkout/payment',
    title: 'Bill Payment',
    shortCode: eBusinessFlow.WorkflowStateType.BILL_PAYMENT,
    component: BillPaymentConfigurationMagicComponent,
  },
  {
    path: '/my/bills/checkout/summary',
    title: 'Summary',
    shortCode: eBusinessFlow.WorkflowStateType.ORDER_SUMMARY,
    component: BillSummaryMagicComponent,
  },
  {
    path: '/my/bills/checkout/order-success',
    title: 'Order Success',
    shortCode: eBusinessFlow.WorkflowStateType.ORDER_SUBMIT,
    component: null,
  },
];
