import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { HeaderMagicComponent, LayoutDetailGreetingMagicComponent, NavigationMagicComponent } from '@libs/magwid';
import { LayoutPageDetailComponent } from '@libs/widgets';
import { RouteDataService } from '@libs/core';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'magic-widget-layout-detail',
  templateUrl: './layout-detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    LayoutPageDetailComponent,
    HeaderMagicComponent,
    NavigationMagicComponent,
    LayoutDetailGreetingMagicComponent,
    TranslatePipe,
  ],
})
export class LayoutDetailComponent {
  private routeDataService = inject(RouteDataService);

  routeData = computed(() => this.routeDataService.data());

  back() {
    this.routeDataService.routerBack();
  }
}
