<layout-page-detail>
  <magic-widget-header header></magic-widget-header>
  <magic-widget-layout-detail-greeting page-heading></magic-widget-layout-detail-greeting>
  <div back-button>
    <eds-button appearance="subtle" iconLeading="arrowLeft" (button-click)="back()">
      {{ routeData()?.backTitle ?? 'back' | translate }}
    </eds-button>
  </div>
  <magic-widget-navigation navigation></magic-widget-navigation>
  <ng-content></ng-content>
</layout-page-detail>
