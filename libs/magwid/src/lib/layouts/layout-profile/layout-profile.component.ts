import { ChangeDetectionStrategy, Component } from '@angular/core';
import { UserGreetingMagicComponent, HeaderMagicComponent, NavigationMagicComponent } from '@libs/magwid';
import { LayoutPageDefaultComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-layout-profile',
  templateUrl: './layout-profile.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NavigationMagicComponent, UserGreetingMagicComponent, HeaderMagicComponent, LayoutPageDefaultComponent],
})
export class LayoutProfileComponent {}
