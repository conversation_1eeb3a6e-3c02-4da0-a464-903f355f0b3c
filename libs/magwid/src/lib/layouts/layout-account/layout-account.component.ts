import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { HeaderMagicComponent, NavigationMagicComponent, PageHeadingMagicComponent } from '@libs/magwid';
import { LayoutPageAccountComponent } from '@libs/widgets';
import { RouteDataService } from '@libs/core';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'magic-widget-layout-account',
  templateUrl: './layout-account.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    LayoutPageAccountComponent,
    HeaderMagicComponent,
    NavigationMagicComponent,
    PageHeadingMagicComponent,
    TranslatePipe,
  ],
})
export class LayoutAccountComponent {
  private routeDataService = inject(RouteDataService);
  routeData = computed(() => this.routeDataService.data());

  back() {
    this.routeDataService.routerBack();
  }
}
