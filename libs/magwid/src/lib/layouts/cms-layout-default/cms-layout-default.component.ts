import { ChangeDetectionStrategy, Component } from '@angular/core';
import { CmsFooterMagicComponent, CmsHeaderMagicComponent } from '@libs/magwid';
import { CmsLayoutPageDefaultComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-layout-default',
  templateUrl: './cms-layout-default.component.html',
  styleUrls: ['./cms-layout-default.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CmsLayoutPageDefaultComponent, CmsHeaderMagicComponent, CmsFooterMagicComponent],
})
export class CmsLayoutDefaultComponent {}
