import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { LayoutPageCartComponent } from '@libs/widgets';
import { HeaderMagicComponent } from '@libs/magwid';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@libs/plugins';
import { QuoteState } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';
import { select } from '@ngxs/store';

@Component({
  selector: 'magic-widget-cart-page-layout',
  templateUrl: './layout-cart.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [LayoutPageCartComponent, HeaderMagicComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class LayoutCartComponent {
  private router = inject(Router);
  private translateService = inject(TranslateService);
  private activatedRoute = inject(ActivatedRoute);

  private quote = select(QuoteState.quote);

  backToTitle = computed(() => {
    switch (this.quote().currentBusinessFlowSpecShortCode) {
      case eBusinessFlow.Specification.PURCHASE_ADDON:
        return this.translateService.translate('backToAddonPurchase');

      default:
        return this.translateService.translate('backToStore');
    }
  });

  back() {
    switch (this.quote().currentBusinessFlowSpecShortCode) {
      case eBusinessFlow.Specification.PACKAGE_CHANGE:
        return this.router.navigate([this.quote().planChangeUrl], {
          queryParams: {
            flow: this.quote().currentBusinessFlowSpecShortCode,
            customerOrderId: this.quote().customerOrderId,
          },
        });
      case eBusinessFlow.Specification.PURCHASE_ADDON:
        return this.router.navigate([this.quote().purchaseAddonUrl], {
          queryParams: {
            flow: this.quote().currentBusinessFlowSpecShortCode,
            customerOrderId: this.quote().customerOrderId,
            selectedOfferId: this.activatedRoute.snapshot.queryParams.selectedOfferId,
          },
        });
      default:
        return this.router.navigate(['/']);
    }
  }
}
