import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { Location } from '@angular/common';
import { RouteDataService } from '@libs/core';
import { HeaderMagicComponent } from '@libs/magwid';
import { LayoutPageMembershipComponent } from '@libs/widgets';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'magic-widget-layout-membership',
  templateUrl: './layout-membership.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [HeaderMagicComponent, LayoutPageMembershipComponent, TranslatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class LayoutMembershipComponent {
  private location = inject(Location);
  private routeDataService = inject(RouteDataService);

  routeData = computed(() => this.routeDataService.data());

  back() {
    this.location.back();
  }
}
