import { ChangeDetectionStrategy, Component } from '@angular/core';
import { LayoutPageCheckoutComponent } from '@libs/widgets';
import { HeaderMagicComponent } from '@libs/magwid';
@Component({
  selector: 'magic-widget-checkout-page-layout',
  templateUrl: './layout-checkout.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [LayoutPageCheckoutComponent, HeaderMagicComponent],
})
export class LayoutCheckoutComponent {}
