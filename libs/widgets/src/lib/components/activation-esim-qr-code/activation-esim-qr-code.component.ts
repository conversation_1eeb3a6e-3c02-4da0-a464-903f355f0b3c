import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { QRCodeComponent } from 'angularx-qrcode';

@Component({
  selector: 'widget-activation-esim-qr-code',
  templateUrl: './activation-esim-qr-code.component.html',
  styleUrls: ['./activation-esim-qr-code.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, QRCodeComponent],
})
export class ActivationESimQrCodeComponent {
  qrCode = input<string>('');

  closeEvent = output<void>();
}
