@if (usageOptions()?.length) {
  <widget-segment-area part="base" [segmentedOptions]="usageOptions()">
    @for (item of usageSummary(); track $index) {
      <div segment [id]="item.id" part="usages" [class.deactivated]="item.deactivated">
        @for (data of item.data; track $index) {
          <eds-progress-bar
            [label]="data.label"
            [helperText]="data.helperText"
            [value]="data.value"
            [maxValue]="data.maxValue"
            [valueType]="data.valueType"
            [isIndeterminate]="data.isIndeterminate"
            [isAnimated]="data.isAnimated"
            [isUnlimited]="data.isUnlimited"
            [type]="data.type"
            [circleProperties]="data.circleProperties"
            [hideBar]="!!data.warning"
            [hiddenMessage]="data.warning"
          >
          </eds-progress-bar>
        }
      </div>
    }
  </widget-segment-area>
}
