import { Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { SegmentAreaComponent } from '../../segment-area';
import { SegmentDirective } from '../../../directives';
import { UsageOption, UsageSummaryData } from '../../../model';

@Component({
  selector: 'widget-usage-summary',
  templateUrl: './usage-summary.component.html',
  styleUrls: ['./usage-summary.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [SegmentAreaComponent, SegmentDirective],
})
export class UsageSummaryComponent {
  usageOptions = input<UsageOption[]>([]);

  usageSummary = input<UsageSummaryData[]>([]);
}
