import { Component, CUSTOM_ELEMENTS_SCHEMA, input, output, model } from '@angular/core';
import { UsageSummaryComponent } from '../usage-summary/usage-summary.component';
import { PlanUsage, UsageSummaryPlan } from '../../../model';
import { EmptyFallbackDataComponent } from '@libs/widgets';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-selective-usage-summary',
  templateUrl: './selective-usage-summary.component.html',
  styleUrls: ['./selective-usage-summary.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [UsageSummaryComponent, EmptyFallbackDataComponent, TranslatePipe],
})
export class SelectiveUsageSummaryComponent {
  showPlanSelection = input(true);
  planUsage = input<PlanUsage>();
  loadMoreEnabled = input(true);
  myProductList = input<UsageSummaryPlan[]>([]);
  loading = input(false);

  selectedPlan = model<UsageSummaryPlan>();
  loadMorePlans = output<void>();
  onRefresh = output<void>();

  updateUsageData(value: string) {
    const selectedOption = this.myProductList().find((data) => data.value === value);

    return selectedOption;
  }

  handleOptionsScroll(): void {
    if (this.loadMoreEnabled()) {
      this.loadMorePlans.emit();
    }
  }

  handleOptionSelect(e: CustomEvent<{ option: { value: string } }> | unknown) {
    const selectedPlan = this.updateUsageData((e as CustomEvent).detail.option.value);

    this.selectedPlan.set(selectedPlan);
  }

  handleRefresh() {
    this.onRefresh.emit();
  }
}
