:host {
  --eds-plans-usage-summary-content-gap: var(--plans-usage-summary-content-gap, var(--eds-spacing-400));
}

.plans-usage-summary {
  display: flex;
  flex-direction: column;
  gap: var(--eds-plans-usage-summary-content-gap);
}

.product-list-option {
  position: relative;
}

.product-list-option-tag {
  position: absolute;
  top: 0.8rem;
  right: 1rem;
}

.option-content {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .option-content-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.selected-plan-slot {
  display: flex;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selected-plan-container {
  position: relative;

  .selected-plan-tag {
    position: absolute;
    top: 0.45rem;
    right: 2.25rem;
  }
}

[part='select'] {
  &::part(select) {
    justify-content: space-between;
  }

  &::part(input) {
    width: 75%;
  }
}
