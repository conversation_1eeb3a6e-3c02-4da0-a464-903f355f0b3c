<div class="plans-usage-summary">
  @if (myProductList().length && showPlanSelection()) {
    <div class="selected-plan-container">
      <eds-select
        part="select"
        [isSearchable]="false"
        (option-selected)="handleOptionSelect($event)"
        (scroll-bottom)="handleOptionsScroll()"
        [maxOptionsShown]="5"
        [isLoading]="loading()"
      >
        @for (plan of myProductList(); track $index) {
          <eds-select-option
            [value]="plan.value"
            [name]="plan.name"
            [isSelected]="plan.isSelected"
            [isDisabled]="plan.isDisabled"
          >
            <div class="option-content">
              <span class="option-content-text">{{ plan.label }}</span>
              <eds-tag [content]="plan.statusDescription" [appearance]="plan.appearance"></eds-tag>
            </div>
          </eds-select-option>
        }
      </eds-select>
      <div class="selected-plan-tag">
        <eds-tag [content]="selectedPlan().statusDescription" [appearance]="selectedPlan().appearance"></eds-tag>
      </div>
    </div>
  }
  @if (planUsage().usageSummary.length) {
    <widget-usage-summary [usageOptions]="planUsage().usageOptions" [usageSummary]="planUsage().usageSummary">
    </widget-usage-summary>
  } @else {
    <widget-empty-fallback-data
      [title]="'unAccessibleData' | translate"
      [text]="'receiveYourUsageData' | translate"
      (onRefresh)="handleRefresh()"
    ></widget-empty-fallback-data>
  }
</div>
