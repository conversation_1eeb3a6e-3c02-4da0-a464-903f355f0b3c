import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  input,
  OnInit,
  output,
} from '@angular/core';
import { FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { FormControl, FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { SelectOption } from '@eds/components';

export interface BillActiveFilter {
  billingAccountId: number;
  date: string;
}

export interface BillActiveFilterForm {
  billingAccountId: FormControl<number>;
  date: FormControl<string>;
}

@Component({
  selector: 'widget-bill-filter',
  templateUrl: './bill-filter.component.html',
  styleUrls: ['./bill-filter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, ReactiveFormsModule, FormFieldComponent],
})
export class BillFilterComponent implements OnInit {
  private formGroupDirective = inject(FormGroupDirective);

  billAccountLov = input<SelectOption[]>([]);

  formReady = output<boolean>();

  form: FormGroup<BillActiveFilterForm>;

  ngOnInit() {
    if (!this.formGroupDirective) {
      throw new Error('widget-bill-filter must be used within a FormGroup.');
    }
    this.form = this.formGroupDirective.control as FormGroup<BillActiveFilterForm>;

    this.buildForm();
    this.formReady.emit(true);
  }

  private buildForm() {
    this.form?.addControl('billingAccountId', new FormControl<number>(null));
    this.form?.addControl('date', new FormControl<string>(''));
  }
}
