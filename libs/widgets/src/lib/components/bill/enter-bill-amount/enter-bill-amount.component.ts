import { ChangeDetectionStrategy, Component, effect, input, InputSignal, output } from '@angular/core';
import { TitledSectionComponent } from '@libs/widgets';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { createNumericMask, FormFieldComponent, TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-enter-bill-amount',
  imports: [TitledSectionComponent, FormsModule, FormFieldComponent, ReactiveFormsModule, TranslatePipe],
  templateUrl: './enter-bill-amount.component.html',
  styleUrl: './enter-bill-amount.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EnterBillAmountComponent {
  protected readonly numericMask = createNumericMask();

  onPayBillAmountChange = output<string>();

  form: InputSignal<FormGroup> = input();

  constructor() {
    effect(() => {
      this.form().controls.payBillAmount.valueChanges.subscribe((value) => {
        this.onPayBillAmountChange.emit(value);
      });
    });
  }

  protected readonly status = status;
}
