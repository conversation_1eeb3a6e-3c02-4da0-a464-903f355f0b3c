import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { Invoice } from '@libs/types';
import { CurrencyPipe, TitleCasePipe, UpperCasePipe } from '@angular/common';
import { CustomDatePipe, TranslatePipe } from '@libs/plugins';
import { DEFAULT_DATE_FORMAT, MONTH_YEAR_FORMAT } from '@libs/core';

@Component({
  selector: 'widget-bill-card',
  templateUrl: './bill-card.component.html',
  styleUrl: './bill-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CustomDatePipe, CurrencyPipe, TranslatePipe, UpperCasePipe, TitleCasePipe],
})
export class BillCardComponent {
  bill = input<Invoice.InvoiceDefinitionType>(null);

  accountName = input<string>(null);

  invoiceId = input<number>(null);

  isShowAction = input<boolean>(true);

  isShowOnlyTotalPrice = input<boolean>(false);

  billPrice = input<Invoice.InvoiceExternalPrice>(null);

  downloadClick = output<number>();

  payClick = output<number>();

  defaultDateFormat = DEFAULT_DATE_FORMAT;

  monthYearFormat = MONTH_YEAR_FORMAT;
}
