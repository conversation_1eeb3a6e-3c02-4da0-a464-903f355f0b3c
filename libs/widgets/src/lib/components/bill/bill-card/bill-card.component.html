<eds-card class="bill-card" [class]="billPrice()?.title ? 'eds-card-bg-color' : ''">
  <div [class]="isShowOnlyTotalPrice() ? 'content-alignment' : 'content'">
    @if (!isShowOnlyTotalPrice()) {
      <div class="info-section">
        <div class="info-section-status">
          @if (accountName()) {
            <eds-text size="lg" weight="medium" [text]="accountName() | titlecase"></eds-text>
          } @else {
            <eds-text size="lg" weight="medium" [text]="bill()?.invoiceDate | date: monthYearFormat"></eds-text>
          }

          @if (bill()?.invoiceStatus?.status) {
            <eds-tag
              [content]="bill().invoiceStatus.status | translate | uppercase"
              [appearance]="bill().invoiceStatus.appearance"
            ></eds-tag>
          }
          @if (bill()?.invoiceStatus?.isOverdue) {
            <eds-heading
              class="text-danger"
              size="xs"
              weight="medium"
              [text]="'overdueDays' | translate: { value: bill().invoiceStatus?.day } | uppercase"
            ></eds-heading>
          }
        </div>
        @if (invoiceId()) {
          <div class="info-section-date">
            <div>
              <eds-text
                class="text-light"
                size="md"
                [text]="'billDueDate' | translate: { value: bill()?.dueDate | date: defaultDateFormat }"
              ></eds-text>
            </div>
            <div>
              <eds-text size="md" [text]="'billInvoiceId' | translate: { value: bill()?.invoiceNumber }"></eds-text>
            </div>
          </div>
        }
      </div>
    }
    <div class="price-section">
      <div>
        @if (billPrice()?.title) {
          <div class="bill-external-price">
            <eds-text class="text-light" size="md" [text]="billPrice().title"></eds-text>
            <eds-heading size="xl" [text]="billPrice().amount | currency: billPrice().currencyCode"></eds-heading>
          </div>
        } @else if (bill().invoiceStatus?.isPaid) {
          <eds-text size="lg" weight="medium" [text]="bill().dueAmount | currency: bill().currencyCode"></eds-text>
        } @else {
          @if (bill()?.invoiceStatus?.isPartiallyPaid) {
            <div class="partially-paid-price">
              <div>
                <eds-text class="text-light" size="md" [text]="('remainingAmount' | translate) + ':'"></eds-text>
              </div>

              <div>
                <eds-text
                  size="lg"
                  weight="medium"
                  [text]="bill().openAmount | currency: bill().currencyCode"
                ></eds-text>
              </div>
            </div>
          } @else {
            <eds-text size="lg" weight="medium" [text]="bill().openAmount | currency: bill().currencyCode"></eds-text>
          }
        }
      </div>
      @if (isShowAction()) {
        <div class="actions">
          <eds-button
            appearance="default"
            iconOnly
            iconLeading="pdf"
            (button-click)="downloadClick.emit(bill().invoiceNumber)"
          >
          </eds-button>
          @if (!bill().invoiceStatus?.isPaid) {
            <eds-button appearance="primary" (button-click)="payClick.emit(bill().invoiceNumber)">
              {{ 'pay' | translate }}
            </eds-button>
          }
        </div>
      }
    </div>
  </div>
</eds-card>
