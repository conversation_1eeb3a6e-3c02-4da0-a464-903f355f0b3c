<div class="base">
  <div class="title">
    <eds-text size="lg" weight="medium" [text]="'unpaidBillAmount' | translate"></eds-text>
  </div>
  <div>
    <div class="content">
      <eds-heading
        class="account-name"
        size="xl"
        weight="medium"
        [text]="accountName() | titlecase"
      ></eds-heading>
      <eds-heading
        size="xl"
        weight="medium"
        [text]="totalOpenAmount() | currency: currencyCode()"
      ></eds-heading>
    </div>

    @if (outstandingAmount()) {
      <div class="overdue-balance">
        <eds-icon class="overdue-icon" name="informationCircle"></eds-icon>
        <eds-text
          class="overdue-text"
          [text]="
            'totalOverdueAmount' | translate: { amount: outstandingAmount() | currency: currencyCode() }
          "
          size="md"
        ></eds-text>
      </div>
    }
  </div>

  @if (totalOpenAmount()) {
    <eds-button appearance="secondary" (button-click)="payNow.emit()">
      {{ 'payNow' | translate }}
    </eds-button>
  }
</div>
