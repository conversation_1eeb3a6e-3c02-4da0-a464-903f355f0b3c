.base {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);

  .title {
    color: var(--eds-colors-text-light);
  }

  .content {
    display: flex;
    flex-direction: column;
    padding: var(--eds-spacing-200) 0;
    justify-content: space-between;
    align-items: flex-start;
    align-content: flex-start;
    gap: var(--eds-spacing-400);
    align-self: stretch;
    flex-wrap: wrap;

    .account-name {
      max-width: 194px;
    }

    @media (min-width: 834px) {
      flex-direction: row;
    }
  }

  .overdue-balance {
    display: flex;
    align-items: center;
    gap: var(--eds-spacing-200);
    align-self: stretch;

    .overdue-icon {
      width: var(--eds-sizing-500);
      height: var(--eds-sizing-500);
      color: var(--eds-colors-danger-default);
    }

    .overdue-text {
      color: var(--eds-colors-danger-default);
    }
  }
}
