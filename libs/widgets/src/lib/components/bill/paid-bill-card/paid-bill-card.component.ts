import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { Invoice } from '@libs/types';
import { BillCardComponent } from '../bill-card/bill-card.component';

@Component({
  selector: 'widget-paid-bill-card',
  templateUrl: './paid-bill-card.component.html',
  styleUrl: './paid-bill-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, BillCardComponent],
})
export class PaidBillCardComponent {
  bills = input<Invoice.InvoiceDefinitionType[]>();

  downloadClick = output<Invoice.InvoiceDefinitionType>();
}
