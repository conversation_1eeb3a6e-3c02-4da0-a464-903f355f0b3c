import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { CurrencyPipe } from '@angular/common';
import { Invoice } from '@libs/types';
import { Alert } from '../../../model';

@Component({
  selector: 'widget-bill-summary',
  templateUrl: './bill-summary.component.html',
  styleUrl: './bill-summary.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, CurrencyPipe],
})
export class BillSummaryComponent {
  payBillAmount = input<number>(null);
  currencyCode = input<string>();

  paymentAmountPrice = input<Invoice.InvoiceExternalPrice>(null);

  alertDetail = input<Alert>({});
}
