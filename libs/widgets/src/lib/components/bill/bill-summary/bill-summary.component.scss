.base {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-600);
}

.payment-option {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
  align-self: stretch;
}

.payment-option-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--eds-spacing-400);

  border-radius: var(--eds-radius-300);
  border: var(--eds-stroke-025) solid var(--eds-border-color-default);
}

.alert {
  &::part(wrapper) {
    align-items: center;
  }

  &::part(icon) {
    width: var(--eds-sizing-400);
    height: var(--eds-sizing-400);
  }

  &::part(title) {
    --eds-font-size-heading-sm: var(--eds-font-size-body-md);
    --eds-heading-font-weight: regular;
  }

  &::part(description) {
    padding-inline-start: 24px;
    --eds-font-size-heading-sm: var(--eds-font-size-body-md);
    --eds-heading-font-weight: regular;
  }
}
