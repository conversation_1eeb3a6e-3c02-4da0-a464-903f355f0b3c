import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { NavigationItem } from '../../model/navigation.model';
import { ConvertHrefToRouterLinkDirective } from '@libs/core';

@Component({
  selector: 'widget-navigation',
  templateUrl: './navigation.component.html',
  styleUrls: ['navigation.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ConvertHrefToRouterLinkDirective],
})
export class NavigationComponent {
  items = input<NavigationItem[]>([]);
}
