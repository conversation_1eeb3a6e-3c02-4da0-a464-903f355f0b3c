.base {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--eds-spacing-400);

  eds-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    color: var(--eds-colors-text-light);
  }

  .label {
    width: calc(var(--eds-size-multiplier) * 40);

    &::part(base) {
      white-space: nowrap;
    }
  }

  .value {
    &::part(base) {
      overflow-wrap: break-word;
    }
  }
}

.container {
  display: grid;
  grid-template-columns: var(--eds-sizing-500) 1fr;
  gap: var(--eds-spacing-200);
  width: 100%;
  align-items: center;

  .value {
    grid-column: span 2;
  }

  @media (min-width: 834px) {
    grid-template-columns: repeat(2, minmax(0, min-content)) minmax(0, auto);
    gap: var(--eds-spacing-300);

    .value {
      grid-column: span 1;
    }
  }
}

.line {
  width: 100%;
  height: 1px;
  background: var(--eds-border-color-default);
}

.order-steps-container {
  display: flex;
  flex-direction: column;
  align-self: stretch;
}
