@if (text() || icons().length) {
  <div class="empty-state">
    @if (iconName()) {
      <div class="empty-state__icons">
        <eds-icon [name]="iconName()"></eds-icon>
      </div>
    } @else {
      @if (icons() && icons().length > 0) {
        <div class="empty-state__icons">
          @for (icon of icons(); track $index) {
            <widget-icon [icon]="icon"></widget-icon>
          }
        </div>
      }
    }

    @if (text()) {
      <slot name="text" part="text">
        @if (headingText()) {
          <eds-heading size="md" [text]="headingText()"></eds-heading>
        }
        <eds-text as="p" [text]="text()"></eds-text>
      </slot>
    }

    @if (buttonText()) {
      <eds-button appearance="secondary" (button-click)="onButtonResetFilter()">
        {{ buttonText() | translate }}
      </eds-button>
    }
  </div>
}
