import { Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import {
  AtomIconCellularNetworkComponent,
  AtomIconSimcardComponent,
  AtomIconSmartPhoneComponent,
  AtomIconTabletComponent,
} from '../svg-icons';
import { IconComponent } from '../icon';
import { Icon } from '../../model';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-empty-state',
  templateUrl: './empty-state.component.html',
  styleUrls: ['./empty-state.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [IconComponent, TranslatePipe],
})
export class EmptyStateComponent {
  icons = input<Icon[]>([
    AtomIconTabletComponent,
    AtomIconCellularNetworkComponent,
    AtomIconSmartPhoneComponent,
    AtomIconSimcardComponent,
  ]);

  iconName = input<string>(null);
  headingText = input<string>(null);
  text = input<string>();
  buttonText = input<string | null>(null);
  buttonAction = output<void>();

  onButtonResetFilter() {
    this.buttonAction.emit();
  }
}
