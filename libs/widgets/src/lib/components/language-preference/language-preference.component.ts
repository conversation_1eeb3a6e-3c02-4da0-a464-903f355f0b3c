import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  input,
  OnInit,
  OnDestroy,
  output,
  signal,
  NgZone,
  ChangeDetectorRef,
  inject,
} from '@angular/core';
import { ConfigState, TranslatePipe } from '@libs/plugins';
import { SelectOption } from '@libs/widgets';
import { select } from '@ngxs/store';
import { FeatureFlagEnum } from '@libs/types';
import { KnownLocalStorageKeys, LocalStorageService } from '@libs/core';

@Component({
  selector: 'widget-language-preference',
  templateUrl: './language-preference.component.html',
  styleUrls: ['./language-preference.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe],
})
export class LanguagePreferenceComponent implements OnInit, OnDestroy {
  private ngZone = inject(NgZone);
  private cdr = inject(ChangeDetectorRef);

  private localStorageService = inject(LocalStorageService);

  languageSelectOptions = input<SelectOption[]>();
  geographyPlacesOptions = input<SelectOption[]>();
  languageDropdown = select(ConfigState.isFeatureEnabled(FeatureFlagEnum.languageDropdown));

  closePreferences = output<boolean>();
  onSave = output<{ language: SelectOption; geographyPlace: SelectOption }>();

  data = signal<{ language?: SelectOption; geographyPlace?: SelectOption }>({});

  private resizeListener: () => void;

  constructor() {
    this.resizeListener = this.handleResize.bind(this);
  }

  ngOnInit(): void {
    const selectedOnLocalstorage = {
      language: this.localStorageService.get<string>(KnownLocalStorageKeys.LANGUAGE),
      geographyPlace: this.localStorageService.get<string>(KnownLocalStorageKeys.REGION),
    };
    const initialLang =
      this.languageSelectOptions().find((o) => o.value === selectedOnLocalstorage.language) ??
      this.languageSelectOptions()?.find((o) => o.isSelected);

    const initialPlace =
      this.geographyPlacesOptions()?.find((o) => o.value === selectedOnLocalstorage.geographyPlace) ??
      this.geographyPlacesOptions()?.[0];

    if (initialPlace) {
      initialPlace.isSelected = true;
    }
    this.data.set({ language: initialLang, geographyPlace: initialPlace });
    this.addResizeListener();
  }

  ngOnDestroy(): void {
    this.removeResizeListener();
  }

  private handleResize(): void {
    this.ngZone.run(() => {
      this.closePreferences.emit(true);
      this.cdr.markForCheck();
    });
  }

  private addResizeListener(): void {
    this.ngZone.runOutsideAngular(() => {
      window.addEventListener('resize', this.resizeListener);
    });
  }

  private removeResizeListener(): void {
    window.removeEventListener('resize', this.resizeListener);
  }

  onLanguageChange(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    const selectedOption = this.languageSelectOptions()?.find((o) => o.value === value);
    this.data.update((current) => ({ ...current, language: selectedOption }));
  }

  onGeographyPlaceChange(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    const selectedOption = this.geographyPlacesOptions()?.find((o) => o.value === value);
    this.data.update((current) => ({ ...current, geographyPlace: selectedOption }));
  }

  handleSave(): void {
    this.onSave.emit({ language: this.data().language, geographyPlace: this.data().geographyPlace });
  }

  customEvent(): void {
    this.closePreferences.emit(true);
  }
}
