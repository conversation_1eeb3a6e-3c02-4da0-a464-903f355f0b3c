<div class="base">
  <div class="content">
    <eds-text [text]="'pleaseChooseYourCountryAndRegion' | translate"></eds-text>
    <div class="actions">
      <eds-select
        [placeholder]="'region' | translate"
        [value]="data().geographyPlace?.label"
        (option-selected)="onGeographyPlaceChange($event)"
      >
        @for (option of geographyPlacesOptions(); track option.value) {
          <eds-select-option
            [label]="option.label"
            [value]="option.value"
            [name]="option.name"
            [isSelected]="option.isSelected"
            [isDisabled]="option.isDisabled"
          ></eds-select-option>
        }
      </eds-select>
      @if (languageDropdown()) {
        <eds-select
          [placeholder]="'language' | translate"
          [value]="data().language?.label | translate"
          (option-selected)="onLanguageChange($event)"
        >
          @for (option of languageSelectOptions(); track option.value) {
            <eds-select-option
              [label]="option.label | translate"
              [value]="option.value"
              [name]="option.name"
              [isSelected]="option.isSelected"
              [isDisabled]="option.isDisabled"
            ></eds-select-option>
          }
        </eds-select>
      }
      <eds-button appearance="secondary" shouldFitContainer="true" (button-click)="handleSave()">{{
        'save' | translate
      }}</eds-button>
    </div>
    <eds-button
      class="language-preference-close"
      appearance="subtle"
      iconOnly
      iconLeading="cancel"
      (button-click)="customEvent()"
    ></eds-button>
  </div>
</div>
