import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { PageHeadingSizeValues } from '../../model/heading.model';
import { eProduct } from '@libs/types';
import { getColorByProductStatus } from '@libs/core';

@Component({
  selector: 'widget-page-heading',
  templateUrl: './page-heading.component.html',
  styleUrls: ['page-heading.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PageHeadingComponent {
  iconName = input('');
  headingText = input('');
  headingSize = input<PageHeadingSizeValues>('lg');
  subheadingText = input('');
  subheadingSize = input<PageHeadingSizeValues>('sm');

  statusText = input('');
  statusAppearance = input<eProduct.ProductStatusShortCodes | null>(null);

  getColorByProductStatus = getColorByProductStatus;
}
