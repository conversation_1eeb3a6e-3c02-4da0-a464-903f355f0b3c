<div part="base">
  @if (iconName()) {
    <eds-icon part="icon" [name]="iconName()"></eds-icon>
  }

  @if (statusText()) {
    <eds-tag
      part="status-tag"
      [content]="statusText().toUpperCase()"
      [appearance]="getColorByProductStatus(statusAppearance())"
    >
    </eds-tag>
  }

  <div part="content">
    @if (headingText()) {
      <eds-heading part="heading" as="h2" [text]="headingText()" [size]="headingSize()"></eds-heading>
    }
    @if (subheadingText()) {
      <eds-heading part="subheading" as="h3" [text]="subheadingText()" [size]="subheadingSize()"></eds-heading>
    }
  </div>
</div>
