import { Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { DataList } from '@libs/widgets';

@Component({
  selector: 'widget-order-information',
  templateUrl: './order-information.component.html',
  styleUrls: ['./order-information.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe],
})
export class OrderInformationComponent {
  showContentArea = input<boolean>(true);
  dataList = input<DataList>({});
}
