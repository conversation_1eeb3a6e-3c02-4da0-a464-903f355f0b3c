@if (suborders()?.length) {
  <div class="base">
    <div class="suborder-info">
      <eds-text size="lg" weight="medium" [text]="'itemsListedBelow' | translate"></eds-text>

      @if (viewAll()) {
        <eds-button appearance="link" size="compact" (button-click)="setViewAllSubOrder()">
          {{ 'hide' | translate }}
        </eds-button>
      } @else {
        @if (suborders()?.length > 1) {
          <eds-button appearance="link" size="compact" (button-click)="setViewAllSubOrder()">
            {{ 'subOrderViewAll' | translate: { count: suborders().length - 1 } }}
          </eds-button>
        }
      }
    </div>

    <div
      class="suborder-container {{ viewAll() ? 'suborder-container-view-all' : '' }}"
      [attr.data-suborder-count]="suborders()?.length || 0"
    >
      @for (order of suborders(); track $index) {
        <div class="suborder-card-container">
          <div class="suborder-card">
            <div class="suborder">
              <div class="suborder-product-info">
                <eds-icon name="simCard"></eds-icon>
                <eds-text size="lg" weight="medium" [text]="order.planName"></eds-text>
              </div>
              @if (order.deviceName) {
                <div class="device">
                  <eds-media-object [text]="order.deviceName" [iconName]="'smartphone'"></eds-media-object>
                </div>
              }
            </div>
          </div>
        </div>
      }
      @if (suborders()?.length > 1 && suborders()?.length < 3 && !viewAll()) {
        <div class="suborder-card-container">
          <div class="suborder-card"></div>
        </div>
      }
    </div>
  </div>
}
