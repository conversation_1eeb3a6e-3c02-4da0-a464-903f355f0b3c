.base {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: var(--eds-spacing-400);
  align-self: stretch;
}

.suborder-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}

.suborder-container {
  display: grid;
  grid-template-rows: 1fr repeat(2, 1rem);
  grid-template-columns: 1fr;
  align-items: end;
  justify-content: center;
  width: 100%;
  transition: all 150ms ease-in-out;

  .suborder-card-container {
    display: flex;
    overflow: hidden;
    opacity: 0;
    background: var(--eds-colors-surface-default);
    transition: all 150ms ease-in-out;

    &:first-child {
      opacity: 1;
      z-index: 2;
    }

    &:nth-child(2) {
      transform: scaleX(0.94);
      opacity: 0.6;
      z-index: 1;
    }

    &:nth-child(3) {
      transform: scaleX(0.88);
      opacity: 0.4;
      z-index: 0;
    }

    .suborder-card {
      display: flex;
      align-items: center;
      padding: var(--eds-spacing-400);
      gap: var(--eds-spacing-600);
      flex-shrink: 0;
      flex: 1;

      border-radius: var(--eds-radius-300);
      border: 1px solid var(--eds-border-color-default);

      .suborder {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: var(--eds-spacing-400);
        flex: 1 0 0;

        .suborder-product-info {
          display: flex;
          align-items: flex-start;
          gap: var(--eds-spacing-200);
          align-self: stretch;

          eds-icon {
            color: var(--eds-colors-secondary-default);
            width: var(--eds-sizing-600);
            height: var(--eds-sizing-600);
          }
        }

        .device {
          display: flex;
          padding: var(--eds-spacing-300);
          flex-direction: column;
          align-items: flex-start;
          gap: var(--eds-spacing-300);
          align-self: stretch;
        }
      }
    }
  }

  &.suborder-container-view-all {
    grid-template-rows: unset;
    gap: var(--eds-spacing-200);

    .suborder-card-container {
      opacity: 1;

      &:nth-child(2) {
        transform: scale(1);
        opacity: 1;
      }

      &:nth-child(3) {
        transform: scale(1);
        opacity: 1;
      }
    }
  }
}
