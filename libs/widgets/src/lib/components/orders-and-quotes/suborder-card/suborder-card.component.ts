import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, signal } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { CustomerOrder } from '@libs/types';

@Component({
  selector: 'widget-suborder-card',
  styleUrls: ['./suborder-card.component.scss'],
  templateUrl: './suborder-card.component.html',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SuborderCardComponent {
  suborders = input<CustomerOrder.PlanDevicePair[]>([]);

  viewAll = signal<boolean>(false);

  setViewAllSubOrder() {
    this.viewAll.set(!this.viewAll());
  }
}
