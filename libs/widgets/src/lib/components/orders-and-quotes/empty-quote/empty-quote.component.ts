import { Component, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { Router } from '@angular/router';

@Component({
  selector: 'widget-empty-quote',
  templateUrl: './empty-quote.component.html',
  styleUrls: ['./empty-quote.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe],
})
export class EmptyQuoteComponentComponent {
  private router = inject(Router);

  navigateToProducts(): void {
    this.router.navigate(['/']);
  }
}
