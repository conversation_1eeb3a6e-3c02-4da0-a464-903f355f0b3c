import { Component, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { Router } from '@angular/router';

@Component({
  selector: 'widget-empty-order',
  templateUrl: './empty-order.component.html',
  styleUrls: ['./empty-order.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe],
})
export class EmptyOrderComponent {
  private router = inject(Router);

  navigateToProducts(): void {
    this.router.navigate(['/']);
  }
}
