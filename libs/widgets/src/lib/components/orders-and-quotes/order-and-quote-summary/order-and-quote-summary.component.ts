import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';
import {
  OrderAndQuoteDetail,
  SegmentArea,
  SegmentAreaComponent,
  SegmentDirective,
  SegmentOptions,
} from '@libs/widgets';
import { OrderCardComponent } from '../order-card';
import { QuoteCardComponent } from '../quote-card';
import { CustomerOrder, SelectOption } from '@libs/types';
import { QuotePriceData } from '@libs/bss';

@Component({
  selector: 'widget-order-and-quote-summary',
  templateUrl: './order-and-quote-summary.component.html',
  styleUrls: ['./order-and-quote-summary.component.scss'],
  imports: [SegmentAreaComponent, SegmentDirective, OrderCardComponent, QuoteCardComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OrderAndQuoteSummaryComponent {
  segmentedOptions = input<SegmentOptions[]>([]);

  orderTypes = input<SelectOption[]>([]);
  orderStatuses = input<SelectOption[]>([]);

  orderAndQuoteSummaryData = input<SegmentArea<CustomerOrder.InquireCustomerOrder>>({ options: [], summary: [] });

  quotesPriceDetailMap = input<Record<number, QuotePriceData>>({});

  hasMore = input<boolean>(false);
  loading = input<boolean>(false);
  totalCount = input<number>(0);
  showCount = input<number>(0);
  loadMore = output<void>();

  changeSegment = output<string>();

  applyOrderFilter = output<CustomerOrder.InquireCustomerOrdersParams>();

  goToDetail = output<OrderAndQuoteDetail>();

  openCancelQuoteModal = output<number>();

  get segmentTypes(): typeof CustomerOrder.SegmentType {
    return CustomerOrder.SegmentType;
  }
}
