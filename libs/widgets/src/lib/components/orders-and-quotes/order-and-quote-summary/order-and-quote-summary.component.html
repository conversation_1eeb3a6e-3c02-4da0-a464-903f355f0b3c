<widget-segment-area
  part="base"
  [segmentedOptions]="orderAndQuoteSummaryData().options"
  (changeSegment)="changeSegment.emit($event)"
>
  @for (segment of orderAndQuoteSummaryData().summary; track $index) {
    <div segment [id]="segment.id" part="content">
      <div class="card-container">
        @if (segment.id === segmentTypes.ORDER) {
          <widget-order-card
            [orders]="segment.data"
            [orderTypes]="orderTypes()"
            [orderStatuses]="orderStatuses()"
            [hasMore]="hasMore()"
            [loading]="loading()"
            [totalCount]="totalCount()"
            [showCount]="showCount()"
            (loadMore)="loadMore.emit()"
            (applyOrderFilter)="applyOrderFilter.emit($event)"
            (goToDetail)="goToDetail.emit($event)"
          ></widget-order-card>
        } @else {
          <widget-quote-card
            [quotes]="segment.data"
            [quotesPriceDetailMap]="quotesPriceDetailMap()"
            (openCancelQuoteModal)="openCancelQuoteModal.emit($event)"
          ></widget-quote-card>
        }
      </div>
    </div>
  }
</widget-segment-area>
