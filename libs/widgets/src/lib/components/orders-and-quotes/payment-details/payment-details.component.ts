import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { DataList } from '@libs/widgets';

@Component({
  selector: 'widget-payment-details',
  templateUrl: './payment-details.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe],
})
export class PaymentDetailsComponent {
  dataList = input<DataList>({});
}
