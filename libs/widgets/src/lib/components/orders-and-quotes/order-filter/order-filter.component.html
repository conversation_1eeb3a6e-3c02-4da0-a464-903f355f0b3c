<eds-card [hidden]="openFilterCard()">
  <div class="button-card">
    <eds-button appearance="default" iconLeading="filter" (button-click)="collapseFilterCard()" shouldFitContainer>
      {{ 'filters' | translate }}
      @if (totalFilteredControl() > 0) {
        <eds-badge [label]="totalFilteredControl()" type="rounded" size="medium" appearance="stroke"></eds-badge>
      }
    </eds-button>

    <div #sortByButton class="sortby-button">
      <eds-button appearance="default" iconLeading="sortBy" shouldFitContainer (button-click)="openSortByDropdown()">
        {{ sortTypeLabel }}
      </eds-button>
      @if (sortByDropdown()) {
        <div class="sortby-dropdown">
          <eds-radio-group (radio-change)="selectSortType($event)">
            @for (sort of sortTypeOptions; track $index) {
              <eds-radio [id]="sort.value" [value]="sort.value" [isChecked]="sort.isSelected">
                <label [for]="sort.value">
                  {{ sort.label }}
                </label>
              </eds-radio>
            }
          </eds-radio-group>
        </div>
      }
    </div>
  </div>
</eds-card>

<eds-card [hidden]="!openFilterCard()">
  <eds-heading size="xs" [text]="('filters' | translate).toUpperCase()"></eds-heading>

  <form [formGroup]="filterForm" class="form">
    <div class="line">
      <widget-form-field
        [placeholder]="'orderId' | translate"
        formControlName="orderId"
        type="number"
        [mask]="customerOrderIdMask"
      ></widget-form-field>

      <widget-form-field
        [placeholder]="'orderType' | translate"
        formControlName="orderType"
        type="select"
        [isSearchable]="true"
        [options]="{ options: orderTypes() }"
      >
      </widget-form-field>
    </div>

    <div class="line">
      <widget-form-field
        [placeholder]="'status' | translate"
        formControlName="status"
        type="select"
        [isSearchable]="true"
        [options]="{ options: orderStatuses() }"
      >
      </widget-form-field>

      <widget-form-field
        [placeholder]="'phoneNumber' | translate"
        formControlName="phoneNumber"
        type="number"
        [options]="{ type: 'tel' }"
        [mask]="phoneNumberMask"
      ></widget-form-field>
    </div>

    <div class="line">
      <widget-form-field
        formControlName="startDate"
        [placeholder]="'startDate' | translate"
        [options]="{
          iconTrailing: 'calendar',
          options: {
            autoClose: true,
          },
        }"
        type="date"
      >
      </widget-form-field>
      <widget-form-field
        formControlName="endDate"
        [placeholder]="'endDate' | translate"
        [options]="{
          iconTrailing: 'calendar',
          options: {
            autoClose: true,
            minDate: minEndDate(),
          },
        }"
        type="date"
      >
      </widget-form-field>
    </div>
  </form>

  <div class="buttons-line">
    <eds-button appearance="default" (button-click)="collapseFilterCard()">
      {{ 'cancel' | translate }}
    </eds-button>
    <div class="buttons">
      <eds-button appearance="subtle" (button-click)="clearForm()">
        {{ 'clearAll' | translate }}
      </eds-button>
      <eds-button appearance="secondary" (button-click)="onApplyFilter()" [disabled]="!hasAnyControlValue()">
        {{ 'applyFilter' | translate }}
      </eds-button>
    </div>
  </div>
</eds-card>
