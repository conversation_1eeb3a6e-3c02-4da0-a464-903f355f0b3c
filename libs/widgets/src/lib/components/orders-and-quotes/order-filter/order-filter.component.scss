:host {
  --eds-sortby-item-height: var(--sortby-item-height, var(--eds-sizing-800));
  --eds-sortby-item-gap: var(--sortby-item-gap, var(--eds-spacing-200));
  --eds-sortby-item-padding: var(--sortby-item-padding, var(--eds-spacing-200) var(--eds-spacing-400));
  --eds-sortby-item-background-color: var(--sortby-item-background-color, var(--eds-colors-primary-default));
  --eds-sortby-item-text-color: var(--sortby-item-text-color, var(--eds-colors-text-white));
  --eds-sortby-item-border-radius: var(--sortby-item-border-radius, var(--eds-sizing-200));
  --eds-sortby-item-border: var(
    --sortby-item-border,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
}

.button-card {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: var(--eds-spacing-400);

  eds-button::part(base) {
    --eds-button-default-background-color: var(--eds-colors-primary-light);
  }

  @media (min-width: 834px) {
    grid-template-columns: 2fr 1fr;
    white-space: nowrap;
  }
}

eds-card::part(base) {
  overflow: visible;
  gap: var(--eds-spacing-400);
}

.form {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
}

.line {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--eds-spacing-400);
}

.buttons-line {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  align-self: stretch;
}

.buttons {
  display: flex;
  align-items: center;
  gap: var(--eds-spacing-400);
}

.sortby-button {
  position: relative;
}

.sortby-dropdown {
  position: absolute;
  top: calc(100% + var(--eds-spacing-200));
  right: 0;
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-200);
  padding: var(--eds-spacing-200);
  border-radius: var(--eds-radius-300);
  background: var(--eds-colors-surface-default);
  border: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default);
  box-shadow: var(--eds-shadow-sm);
  z-index: 1;

  eds-radio::part(base) {
    width: 100%;
    white-space: nowrap;
    justify-content: flex-start;
    border-radius: 0;

    display: flex;
    padding: var(--eds-spacing-200);
    align-items: center;
    align-self: stretch;
  }
}

@media (min-width: 834px) {
  :host {
    --eds-sortby-item-height: var(--sortby-item-height, calc(var(--eds-size-multiplier) * 10));
  }
}
