import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  inject,
  input,
  effect,
  output,
  signal,
  untracked,
  viewChild,
} from '@angular/core';
import {
  createCustomerOrderIdMask,
  createPhoneNumberMask,
  FormFieldComponent,
  TranslatePipe,
  TranslateService,
} from '@libs/plugins';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CustomerOrder, SelectOption } from '@libs/types';
import { DEFAULT_DATE, RadioGroupDirective } from '@libs/core';

@Component({
  selector: 'widget-order-filter',
  templateUrl: './order-filter.component.html',
  styleUrls: ['./order-filter.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, ReactiveFormsModule, FormFieldComponent, RadioGroupDirective],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OrderFilterComponent {
  private translate = inject(TranslateService);

  orderTypes = input<SelectOption[]>([]);
  orderStatuses = input<SelectOption[]>([]);
  resetCounter = input<number>(0);

  applyOrderFilter = output<CustomerOrder.InquireCustomerOrdersParams>();

  openFilterCard = signal<boolean>(false);

  filterForm = new FormGroup({
    orderId: new FormControl('', [Validators.minLength(10)]),
    orderType: new FormControl(''),
    status: new FormControl(''),
    phoneNumber: new FormControl('', [Validators.minLength(10)]),
    startDate: new FormControl(''),
    endDate: new FormControl(''),
    sortColumn: new FormControl(CustomerOrder.SortColumn.SUBMIT_DATE),
    sortType: new FormControl(CustomerOrder.SortType.DESC),
  });

  totalFilteredControl = signal<number>(0);

  sortByButton = viewChild.required<ElementRef<HTMLElement>>('sortByButton');
  sortByDropdown = signal(false);

  phoneNumberMask = createPhoneNumberMask();
  customerOrderIdMask = createCustomerOrderIdMask();

  minEndDate = signal<Date>(new Date(DEFAULT_DATE));

  sortTypeOptions: SelectOption[] = [
    {
      name: CustomerOrder.SortType.DESC,
      label: this.translate.translate('newestToOldest'),
      value: CustomerOrder.SortType.DESC,
      isSelected: true,
      isDisabled: false,
    },
    {
      name: CustomerOrder.SortType.ASC,
      label: this.translate.translate('oldestToNewest'),
      value: CustomerOrder.SortType.ASC,
      isSelected: false,
      isDisabled: false,
    },
  ];

  constructor() {
    this.filterForm.valueChanges.subscribe(() => {
      const filteredCount = Object.values(this.filterForm.controls)
        .filter(
          (control) => control !== this.filterForm.get('sortColumn') && control !== this.filterForm.get('sortType'),
        )
        .filter((control) => !!control.value).length;
      this.totalFilteredControl.set(filteredCount);

      const startDateValue = this.filterForm.get('startDate')?.value;
      const startDate = startDateValue ? new Date(startDateValue) : new Date(DEFAULT_DATE);
      this.minEndDate.set(startDate);
    });

    effect(() => {
      if (this.resetCounter() > 0) {
        untracked(() => this.clearForm());
      }
    });
  }

  collapseFilterCard() {
    this.openFilterCard.set(!this.openFilterCard());
  }

  clearForm() {
    this.filterForm.reset({
      orderId: '',
      orderType: '',
      status: '',
      phoneNumber: '',
      startDate: '',
      endDate: '',
    });

    this.totalFilteredControl.set(0);
    this.onApplyFilter();
  }

  onApplyFilter() {
    this.applyOrderFilter.emit(this.getFilterParams());
  }

  hasAnyControlValue(): boolean {
    return Object.values(this.filterForm.controls).some((control) => !!control.value);
  }

  getFilterParams(): CustomerOrder.InquireCustomerOrdersParams {
    return {
      customerOrderId: +this.filterForm.get('orderId')?.value || null,
      orderTypeShortCode: this.filterForm.get('orderType')?.value || null,
      statusShortCode: this.filterForm.get('status')?.value || null,
      subscriptionIdentifier: this.filterForm.get('phoneNumber')?.value || null,
      submitDateFilterInputStartDate: this.formatDate(this.filterForm.get('startDate')?.value),
      submitDateFilterInputEndDate: this.formatDate(this.filterForm.get('endDate')?.value),
      sortColumn: this.filterForm.get('sortColumn')?.value || CustomerOrder.SortColumn.SUBMIT_DATE,
      sortType: this.filterForm.get('sortType')?.value || CustomerOrder.SortType.DESC,
    };
  }

  private formatDate(dateString: string | null): string | null {
    if (!dateString) return null;
    const dateParts = dateString.split('/');
    return `${dateParts[2]}-${dateParts[0]}-${dateParts[1]}`;
  }

  openSortByDropdown() {
    this.sortByDropdown.update((prev) => !prev);
    setTimeout(() => {
      document.addEventListener('click', this.handleSortByDropdown);
    });
  }

  private handleSortByDropdown = (event: MouseEvent): void => {
    if (this.sortByButton().nativeElement && !this.sortByButton().nativeElement.contains(event.target as Node)) {
      this.closeSortByDropdown();
    }
  };

  closeSortByDropdown(): void {
    this.sortByDropdown.set(false);
    document.removeEventListener('click', this.handleSortByDropdown);
  }

  selectSortType(event: Event) {
    const value = (event.target as HTMLInputElement).value as CustomerOrder.SortType;
    if (value !== this.filterForm.get('sortType')?.value) {
      this.filterForm.get('sortType').setValue(value);
      this.onApplyFilter();
    }
  }

  get sortTypeLabel(): string {
    const sortType = this.filterForm.get('sortType')?.value;
    if (sortType === CustomerOrder.SortType.DESC) {
      return this.translate.translate('sortByNewest');
    } else if (sortType === CustomerOrder.SortType.ASC) {
      return this.translate.translate('sortByOldest');
    } else {
      return this.translate.translate('sortBy');
    }
  }
}
