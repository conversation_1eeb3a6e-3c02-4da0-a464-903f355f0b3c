import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  input,
  output,
  signal,
} from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { CurrencyPipe, UpperCasePipe } from '@angular/common';
import { RefundConfirmationResponse, SelectOption } from '@libs/types';
import { PaymentMethodSelectionTypes } from '../../../model';
import { SavedPaymentMethodComponent } from '../../saved-payment-method';
import { PaymentMethodData } from '@libs/bss';

@Component({
  selector: 'widget-cancel-quote',
  templateUrl: './cancel-quote.component.html',
  styleUrl: './cancel-quote.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe, UpperCasePipe, CurrencyPipe, SavedPaymentMethodComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [CurrencyPipe],
})
export class CancelQuoteComponent {
  reasonTypes = input<SelectOption[]>([]);
  selectedReasonCode = signal<string>(null);
  reasonCodeEvent = output<string>();

  refundInformation = input<RefundConfirmationResponse>(null);
  paymentMethod = computed(() => new PaymentMethodData(this.refundInformation()?.paymentMethod));
  totalRefundAmount = computed(() => this.refundInformation().totalRefundAmount);

  reasonTypeSelect(event: Event) {
    const value = (event.target as HTMLInputElement).value;
    this.reasonTypes().forEach((address) => (address.isSelected = address.value === value));
    this.selectedReasonCode.set(value);
    this.reasonCodeEvent.emit(value);
  }

  protected readonly PaymentMethodSelectionTypes = PaymentMethodSelectionTypes;
}
