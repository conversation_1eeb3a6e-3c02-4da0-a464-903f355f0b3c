<div class="base">
  <div class="container">
    <eds-heading size="sm" text="{{ 'selectReasonForCancellation' | translate }}"></eds-heading>
    <eds-select (option-selected)="reasonTypeSelect($event)">
      @for (option of reasonTypes(); track $index) {
        <eds-select-option
          [label]="option.label"
          [value]="option.value"
          [name]="option.name"
          [isSelected]="option.isSelected"
          [isDisabled]="option.isDisabled"
        >
        </eds-select-option>
      }
    </eds-select>
  </div>

  @if (totalRefundAmount() > 0) {
    <div class="line"></div>
    <div class="container">
      <eds-heading size="sm" text="{{ 'payment' | translate }}"></eds-heading>
      <eds-text size="md" weight="regular" text="{{ 'amountToPayBackDescription' | translate }}"></eds-text>

      <eds-card>
        <eds-text size="md" weight="regular" text="{{ 'amountToPayBack' | translate }}"></eds-text>
        <eds-heading size="xl" text="{{ refundInformation().totalRefundAmount | currency }}"></eds-heading>
      </eds-card>

      <eds-heading size="xs" text="{{ 'existingPaymentMethod' | translate | uppercase }}"></eds-heading>
      <widget-saved-payment-method
        [id]="paymentMethod().paymentMethodId"
        [name]="paymentMethod().name"
        [inputName]="PaymentMethodSelectionTypes.AUTHORIZED"
        [isChecked]="false"
        [isSelected]="false"
        [nameOnBankAccount]="paymentMethod().nameOnBankAccount"
        [networkLogo]="paymentMethod().paymentMethodLogo"
        [cardNumber]="paymentMethod().formatedLastFourDigit"
        [cardExpiry]="paymentMethod().expiryDate"
      ></widget-saved-payment-method>
    </div>
    <div class="line"></div>
  }

  <div class="confirm-container">
    @if (totalRefundAmount() > 0) {
      <eds-text
        size="lg"
        weight="regular"
        text="{{ 'confirmationDescriptionCancelQuote' | translate: { amount: totalRefundAmount() | currency } }}"
        class="text-light"
      ></eds-text>
    }
    <eds-text size="lg" weight="regular" text="{{ 'confirmationCancelQuote' | translate }}"></eds-text>
  </div>
  <div class="line"></div>
</div>
