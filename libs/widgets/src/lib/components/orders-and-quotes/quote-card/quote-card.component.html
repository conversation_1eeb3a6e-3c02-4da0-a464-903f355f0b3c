<div class="base">
  @if (quotes()?.length) {
    @for (quote of quotes(); track $index) {
      <eds-card>
        <div class="base">
          <eds-alert
            showIcon="true"
            iconName="informationCircle"
            [title]="'incompleteOrderMessage' | translate"
            class="alert no-description"
          >
          </eds-alert>

          <div class="order">
            <div class="order-info">
              <eds-text size="sm" weight="regular" text="{{ ('quoteId' | translate).toUpperCase() + ':' }}"></eds-text>
              <eds-text size="sm" weight="regular" text="{{ '#' + quote.customerOrderId }}"></eds-text>
            </div>
          </div>

          <div class="business-interaction-spec">
            <div class="spec-info">
              <eds-heading size="md" [text]="quote.businessFlowSpecification?.name"></eds-heading>
              <eds-heading size="md" [text]="totalAmountByCustomerOrderId(quote.customerOrderId)"></eds-heading>
            </div>

            @if (quote.createDate) {
              <div class="date-info">
                <eds-text size="lg" weight="medium" text="{{ ('createDate' | translate) + ':' }}"></eds-text>
                <eds-text size="lg" weight="medium" text="{{ quote.createDate | date }}"></eds-text>
              </div>
            }
          </div>

          @if (quote?.planDevicePairs?.length) {
            <div class="line"></div>
          }

          <widget-suborder-card [suborders]="quote.planDevicePairs"></widget-suborder-card>

          <div class="buttons">
            <eds-button appearance="default" (button-click)="openCancelQuoteModal.emit(quote.customerOrderId)">
              {{ 'cancelQuote' | translate }}
            </eds-button>
            <!--            <eds-button appearance="secondary">-->
            <!--              {{ 'continue' | translate }}-->
            <!--            </eds-button>-->
          </div>
        </div>
      </eds-card>
    }
  } @else {
    <widget-empty-quote></widget-empty-quote>
  }
</div>
