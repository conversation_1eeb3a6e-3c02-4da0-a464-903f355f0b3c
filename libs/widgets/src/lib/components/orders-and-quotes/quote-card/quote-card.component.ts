import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, inject, input, output } from '@angular/core';
import { CustomerOrder } from '@libs/types';
import { CurrencyPipe } from '@angular/common';
import { SuborderCardComponent } from '../suborder-card';
import { QuotePriceData } from '@libs/bss';
import { EmptyQuoteComponentComponent } from '@libs/widgets';
import { CustomDatePipe, TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-quote-card',
  templateUrl: './quote-card.component.html',
  styleUrls: ['./quote-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CustomDatePipe, SuborderCardComponent, EmptyQuoteComponentComponent, TranslatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [CurrencyPipe],
})
export class QuoteCardComponent {
  currencyPipe = inject(CurrencyPipe);

  quotes = input<Partial<CustomerOrder.InquireCustomerOrder[]>>([]);
  quotesPriceDetailMap = input<Record<number, QuotePriceData>>({});

  openCancelQuoteModal = output<number>();

  totalAmountByCustomerOrderId(id: number): string | number {
    return this.currencyPipe.transform(this.quotesPriceDetailMap()?.[id]?.price?.total) ?? 0;
  }
}
