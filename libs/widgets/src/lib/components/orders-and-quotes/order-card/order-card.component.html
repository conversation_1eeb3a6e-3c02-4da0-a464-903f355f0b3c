<div class="base">
  @if (appliedFilter() || orders()?.length) {
    <widget-order-filter
      [orderTypes]="orderTypes()"
      [orderStatuses]="orderStatuses()"
      (applyOrderFilter)="onApplyFilter($event)"
      [resetCounter]="resetFilterCounter()"
    ></widget-order-filter>
    @if (orders()?.length) {
      <div>
        {{ 'productCountOfTotal' | translate: { count: showCount(), total: totalCount() } }}
      </div>

      @for (order of orders(); track $index) {
        <eds-card>
          <div class="base">
            <div
              class="order"
              [style.cursor]="renderDetailLink(order.orderStatus?.shortCode) ? 'pointer' : ''"
              (click)="navigateToDetail(order.customerOrderId, order.businessFlowSpecification?.shortCode)"
            >
              <div class="order-info">
                <eds-text
                  size="sm"
                  weight="regular"
                  text="{{ ('orderId' | translate).toUpperCase() + ':' }}"
                ></eds-text>
                <eds-text size="sm" weight="regular" text="{{ '#' + order.customerOrderId }}"></eds-text>
                <eds-tag
                  [content]="order.orderStatus?.name?.toUpperCase()"
                  [appearance]="getOrderStatusAppearance(order.orderStatus?.shortCode)"
                ></eds-tag>
              </div>
              @if (renderDetailLink(order.orderStatus?.shortCode)) {
                <eds-icon name="arrowRight" class="icon"></eds-icon>
              }
            </div>

            <div class="business-interaction-spec">
              <eds-heading size="md" [text]="order.businessFlowSpecification?.name"></eds-heading>
              @if (order.submitDate) {
                <div class="date-info">
                  <eds-text size="md" weight="medium" text="{{ ('submitDate' | translate) + ':' }}"></eds-text>
                  <eds-text size="md" weight="medium" text="{{ order.submitDate | date: 'medium' }}"></eds-text>
                </div>
              }
            </div>

            @if (order?.planDevicePairs?.length) {
              <div class="line"></div>
            }

            <widget-suborder-card [suborders]="order.planDevicePairs"></widget-suborder-card>
          </div>
        </eds-card>
      }

      @if (hasMore()) {
        <button class="load-more-button" [class.loading]="loading()" (click)="loadMore.emit()" [disabled]="loading()">
          <span>{{ 'loadMore' | translate }}</span>
          <eds-icon name="arrowDown"></eds-icon>
        </button>
      }
    } @else {
      <widget-empty-filter (resetFilter)="onResetFilter()"></widget-empty-filter>
    }
  } @else {
    <widget-empty-order></widget-empty-order>
  }
</div>
