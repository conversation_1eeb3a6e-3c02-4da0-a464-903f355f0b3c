import { Component, CUSTOM_ELEMENTS_SCHEMA, output } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-empty-filter',
  templateUrl: './empty-filter.component.html',
  styleUrls: ['./empty-filter.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe],
})
export class EmptyFilterComponent {
  resetFilter = output<void>();

  onResetFilter() {
    this.resetFilter.emit();
  }
}
