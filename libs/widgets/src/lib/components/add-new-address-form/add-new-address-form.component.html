<form class="base" [formGroup]="addressForm()">
  <div class="delivery-address">
    @if (formAddressType() === eAddressType.Billing) {
      <eds-form-field [label]="'addressSelectionMode' | translate">
        <eds-radio-group name="addressSelectionType" (radio-change)="addressTypeChange($event)">
          @for (type of addressSelectionTypes; track $index) {
            <eds-radio [id]="type.id" [value]="type.value" [isChecked]="type.isChecked" [isDisabled]="type.isDisabled">
              <label [for]="type.id">{{ type.label }}</label>
            </eds-radio>
          }
        </eds-radio-group>
      </eds-form-field>
    }

    @if (this.selectedAddressSelectionType() === eAddressSelectionMode.EXIST_ADDRESS) {
      <eds-form-field [label]="'addresses' | translate">
        <eds-select (option-selected)="addressSelect($event)">
          @for (option of addressList(); track $index) {
            <eds-select-option
              [label]="option.label"
              [value]="option.value"
              [name]="option.name"
              [isSelected]="option.isSelected"
              [isDisabled]="option.isDisabled"
            >
            </eds-select-option>
          }
        </eds-select>
      </eds-form-field>
    }

    @if (this.selectedAddressSelectionType() === eAddressSelectionMode.NEW_ADDRESS) {
      @if (formAddressType() === eAddressType.Shipment) {
        <eds-heading size="sm" [text]="'deliveryAddress' | translate"></eds-heading>
      }

      <div class="delivery-address-form">
        <widget-form-field
          [label]="addressLabel()"
          [placeholder]="'addressLabelPlaceholder' | translate"
          formControlName="addressLabel"
          maxLength="450"
        >
        </widget-form-field>

        <widget-form-field
          [label]="'countryRegion' | translate"
          formControlName="country"
          [placeholder]="'select' | translate"
          type="select"
          [options]="{ options: countryOptions() }"
          (option-selected)="selectCountry($event)"
        >
        </widget-form-field>
        @for (key of [countryChangeKey()]; track key) {
          <div class="province-city-area">
            <widget-form-field
              [label]="'province' | translate"
              formControlName="state"
              [placeholder]="'select' | translate"
              type="select"
              [options]="{ options: stateOptions() }"
              (option-selected)="selectProvince($event)"
            >
            </widget-form-field>

            <widget-form-field
              [label]="'city' | translate"
              formControlName="city"
              [placeholder]="'select' | translate"
              type="select"
              [options]="{ options: cityOptions() }"
              (option-selected)="selectCity($event)"
            >
            </widget-form-field>
          </div>
        }

        <widget-form-field
          [label]="'address' | translate"
          [placeholder]="'addressPlaceholder' | translate"
          formControlName="addressDescription"
          maxLength="200"
        >
        </widget-form-field>

        <widget-form-field
          [label]="'postalCode' | translate"
          [placeholder]="'postalCodePlaceholder' | translate"
          formControlName="postalCode"
          maxLength="5"
        >
        </widget-form-field>
      </div>

      <div class="contact-person">
        <eds-heading size="sm" [text]="'contactPerson' | translate"></eds-heading>

        <div class="contact-person-form">
          <widget-form-field
            [label]="'fullName' | translate"
            [placeholder]="'fullNamePlaceholder' | translate"
            formControlName="fullName"
            maxLength="100"
          >
          </widget-form-field>

          <widget-phonenumber
            formControlName="phoneNumber"
            [countryOptions]="countryOptionsForPhoneNumber()"
          ></widget-phonenumber>
        </div>
      </div>
    }
  </div>
</form>
