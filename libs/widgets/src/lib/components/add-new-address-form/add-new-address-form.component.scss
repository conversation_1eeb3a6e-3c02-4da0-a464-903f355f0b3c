.base {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-600);
}

.delivery-address {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
}

.delivery-address-form {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
}

.province-city-area {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--eds-spacing-400);
}

.contact-person {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
}

.contact-person-form {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
}
