<eds-card>
  <div class="body">
    <widget-success-icon></widget-success-icon>
    <div class="header">
      <eds-heading size="lg" [text]="'activateESimSuccessTitle' | translate"></eds-heading>
      <eds-text as="p" size="lg" weight="regular" [text]="'activateESimSuccessDescription' | translate"></eds-text>
    </div>

    @if (eSim()) {
      <div class="content">
        <eds-text size="lg" weight="regular" [text]="'iccidNumber' | translate"></eds-text>
        <eds-text size="lg" weight="medium" [text]="eSim() | imask: { mask: eSimMask }"></eds-text>
      </div>
    }
  </div>
  <eds-button
    class="button"
    appearance="secondary"
    size="default"
    shouldFitContainer
    (button-click)="goToHomepageClick()"
  >
    {{ 'goToHomepage' | translate }}
  </eds-button>
</eds-card>
