:host {
  --eds-invoice-overview-alert-icon-size: var(--invoice-overview-alert-icon-size, var(--eds-sizing-400));

  --eds-plan-display: var(--plan-display, flex);
  --eds-plan-gap: var(--plan-gap, var(--eds-spacing-600));

  --eds-plan-data-list-wrapper-gap: var(--plan-data-list-wrapper-gap, var(--eds-spacing-600));
  --eds-plan-data-list-wrapper-align-items: var(--plan-data-list-wrapper-align-items, normal);

  --eds-plan-alert-box-content-icon-size: var(--plan-alert-box-content-icon-size, var(--eds-sizing-400));
}

// eds-alert
.alert {
  &::part(wrapper) {
    align-items: center;
    flex-direction: row;
  }

  &::part(description) {
    color: var(--eds-colors-danger-default);
  }

  &::part(icon) {
    width: var(--eds-invoice-overview-alert-icon-size);
    height: var(--eds-invoice-overview-alert-icon-size);
  }

  &::part(head) {
    width: unset;
  }
}

.base {
  display: var(--eds-plan-display);
  flex-direction: column;
  gap: var(--eds-plan-gap);
}

eds-data-list {
  --eds-data-list-wrapper-gap: var(--eds-plan-data-list-wrapper-gap);

  &::part(item) {
    gap: var(--eds-spacing-400);
  }

  @media (max-width: 834px) {
    &::part(item) {
      flex-direction: column;
      align-items: flex-start;
    }
  }
}

eds-alert {
  --eds-alert-box-content-icon-size: var(--eds-plan-alert-box-content-icon-size);
}

// eds- tile
.tile-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
  gap: var(--eds-spacing-300);
}
