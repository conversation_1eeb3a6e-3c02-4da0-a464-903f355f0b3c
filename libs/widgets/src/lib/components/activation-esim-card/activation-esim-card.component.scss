.base {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--eds-spacing-0);
  align-self: stretch;

  .title {
    display: flex;
    align-items: flex-start;
    align-self: stretch;
    padding: var(--eds-spacing-200) var(--eds-spacing-600);
    gap: var(--eds-spacing-400);

    border-radius: var(--eds-radius-500) var(--eds-radius-500) 0 0;
    border: 1px solid var(--eds-border-color-default);
    background-color: var(--eds-colors-surface-default);

    .title-wrapper {
      display: flex;
      align-items: center;
      gap: var(--eds-spacing-300);
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-600);
    padding: var(--eds-spacing-600);
    align-self: stretch;

    border-radius: 0 0 var(--eds-radius-500) var(--eds-radius-500);
    border-right: 1px solid var(--eds-border-color-default);
    border-bottom: 1px solid var(--eds-border-color-default);
    border-left: 1px solid var(--eds-border-color-default);
    background-color: var(--eds-colors-surface-default);
  }

  .steps {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--eds-spacing-200);
    align-self: stretch;
  }
}
