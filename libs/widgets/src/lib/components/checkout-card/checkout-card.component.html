<div class="base" [class.active]="isActive()" [class.completed]="isCompleted()">
  <div class="title">
    <div class="title-wrapper">
      <eds-icon [name]="icon()"></eds-icon>
      <eds-heading as="h6" size="md" [text]="title()"></eds-heading>
      @if (itemAmount() > 0) {
        <eds-badge [label]="itemAmount()" type="circle" size="medium" appearance="secondary"></eds-badge>
      }
    </div>
    @if (isCompleted()) {
      <eds-tag content="Completed" appearance="green"></eds-tag>
    }
  </div>
  @if (isActive() || isCompleted()) {
    <div class="content">
      <ng-content></ng-content>
    </div>
  }
</div>
