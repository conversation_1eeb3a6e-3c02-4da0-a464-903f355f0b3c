import { Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';

@Component({
  selector: 'widget-checkout-card',
  templateUrl: './checkout-card.component.html',
  styleUrls: ['./checkout-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CheckoutCardComponent {
  title = input('');
  icon = input('');
  itemAmount = input<number>(0);
  isCompleted = input(false);
  isActive = input(false);
}
