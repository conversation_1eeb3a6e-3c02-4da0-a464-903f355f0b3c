.base {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--eds-border-color-default);
  border-radius: var(--eds-radius-300);
  background-color: var(--eds-colors-surface-default);

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--eds-spacing-400);
    background-color: var(--eds-colors-surface-default);
    border-radius: var(--eds-radius-300);

    .title-wrapper {
      display: flex;
      align-items: center;
      gap: var(--eds-spacing-300);

      eds-icon {
        color: var(--eds-colors-secondary-default);
        width: var(--eds-sizing-600);
        height: var(--eds-sizing-600);
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);
    padding: var(--eds-spacing-300);
    border-top: 1px solid var(--eds-border-color-default);

    @media (min-width: 834px) {
      gap: var(--eds-spacing-600);
      padding: var(--eds-spacing-600);
    }
  }

  &.active {
    .title {
      background-color: var(--eds-colors-surface-level-2);
      border-radius: var(--eds-radius-300) var(--eds-radius-300) 0 0;
    }
  }

  &.completed {
    .title {
      border-radius: var(--eds-radius-300) var(--eds-radius-300) 0 0;
    }
  }
}
