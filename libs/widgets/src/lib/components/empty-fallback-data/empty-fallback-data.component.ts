import { CUSTOM_ELEMENTS_SCHEMA, ChangeDetectionStrategy, Component, input, output } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-empty-fallback-data',
  templateUrl: './empty-fallback-data.component.html',
  styleUrls: ['./empty-fallback-data.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe],
})
export class EmptyFallbackDataComponent {
  icon = input('router');
  title = input('');
  text = input('');

  onRefresh = output();

  handleRefresh() {
    this.onRefresh.emit();
  }
}
