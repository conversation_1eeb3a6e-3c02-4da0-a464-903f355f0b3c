eds-card {
  --eds-card-overflow: visible;

  &::part(base) {
    background-color: var(--eds-colors-body);
    display: flex;
    align-items: center;
    flex-direction: column;
  }

  .icon-container {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background-color: var(--eds-colors-primary-light);
    display: flex;
    align-items: center;
    justify-content: center;

    .router-icon {
      width: 2.25rem;
      height: 2.25rem;
    }
  }
}
