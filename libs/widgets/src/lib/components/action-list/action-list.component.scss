:host {
  --eds-action-item-icon-color: var(--action-item-icon-color, var(--eds-colors-text-light));
  --eds-action-list-icon-size: var(--action-list-icon-size, var(--eds-sizing-600));
  --eds-action-list-border-properties: var(
    --action-list-border-properties,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-action-list-border-radius: var(--action-list-border-radius, var(--eds-radius-300));

  --eds-action-element-gap: var(--action-element-gap, var(--eds-spacing-400));
  --eds-action-element-label-gap: var(--action-element-label-gap, var(--eds-spacing-200));

  --eds-action-list-show-more-button-background-color: var(
    --action-list-show-more-button-background-color,
    var(--eds-border-color-default)
  );
  --eds-action-list-show-more-button-padding: var(--action-list-show-more-button-padding, var(--eds-spacing-300) 0);
  --eds-action-list-show-more-button-gap: var(--action-list-show-more-button-gap, var(--eds-spacing-100));

  --eds-action-item-text-color: var(--action-item-text-color, var(--eds-colors-text-dark));
  --eds-action-item-text-decoration: var(--action-item-text-decoration, none);

  --eds-action-show-more-btn-icon-rotate: var(--action-show-more-icon-rotate, var(--eds-rotate-icon-base));
  --eds-action-show-more-btn-icon-size: var(--action-show-more-btn-icon-size, var(--eds-sizing-400));

  --eds-action-show-more-btn-color: var(--action-show-more-btn-color, var(--eds-colors-text-dark));
  --eds-action-show-more-btn-decoration: var(--action-show-more-btn-decoration, none);

  --eds-action-item-elembefore-color: var(--action-item-elembefore-color, var(--eds-colors-secondary-default));
}

.action-list {
  display: flex;
  gap: var(--eds-action-element-gap);
  flex-direction: column;
  border-radius: var(--eds-action-list-border-radius);
  overflow: hidden;
}

.action {
  cursor: pointer;
  text-decoration: none;
}

.action:last-of-type {
  border-bottom: none;
}

.action eds-text::part(base) {
  color: var(--eds-action-item-text-color);
  text-decoration: var(--eds-action-item-text-decoration);
}

.action eds-icon {
  color: var(--eds-action-item-icon-color);
  width: var(--eds-action-list-icon-size);
  height: var(--eds-action-list-icon-size);
  flex-shrink: 0;
}

[part='label'] {
  display: flex;
  align-items: center;
  gap: var(--eds-action-element-label-gap);
  color: var(--eds-action-item-text-color);
}

[part='elemBefore'] {
  color: var(--eds-action-item-elembefore-color);
}

.action-show-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--eds-action-list-show-more-button-gap);
  padding: var(--eds-action-list-show-more-button-padding);
  background: var(--eds-action-list-show-more-button-background-color);
  cursor: pointer;
}

.action-show-more eds-text::part(base) {
  color: var(--eds-action-show-more-btn-color);
  text-decoration: var(--eds-action-show-more-btn-decoration);
}

.action-show-more eds-icon {
  width: var(--eds-action-list-icon-size);
  height: var(--eds-action-list-icon-size);
}

.action-show-more.open eds-icon {
  transform: var(--eds-action-show-more-btn-icon-rotate);
}
