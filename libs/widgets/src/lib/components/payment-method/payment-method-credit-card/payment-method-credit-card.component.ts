import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { SavedPaymentMethod } from '../../../model/payment-method.model';
import { SavedPaymentMethodComponent } from '../../saved-payment-method';
import { CreditCardFormComponent } from '../../../forms/credit-card-form/credit-card-form.component';
import { CreditCardFormFields } from '@libs/widgets';
import { uuid } from '@libs/bss';

@Component({
  selector: 'widget-payment-method-credit-card',
  templateUrl: './payment-method-credit-card.component.html',
  styleUrls: ['./payment-method-credit-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [SavedPaymentMethodComponent, CreditCardFormComponent],
})
export class PaymentMethodCreditCardComponent {
  savedCreditCards = input<SavedPaymentMethod[]>([]);

  createCreditCard = output<CreditCardFormFields | void>();
  onDelete = output<string | number>();
  selectionChange = output<string>();

  uuid = uuid();

  handleDelete(id: string | number) {
    this.onDelete.emit(id);
  }
}
