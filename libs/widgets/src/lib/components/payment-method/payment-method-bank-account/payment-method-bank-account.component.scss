.payment-method_bank-account {
  padding: var(--eds-spacing-400);
}

.payment-method_credit-card {
  border-bottom: 1px solid var(--eds-border-color-default);
}

.payment-method_saved-payment-methods {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-600);

  .payment-method_saved-payment-methods_default,
  .payment-method_saved-payment-methods_saved {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);

    eds-text {
      height: var(--eds-sizing-400);

      &::part(base) {
        text-transform: uppercase;
      }
    }
  }
}
