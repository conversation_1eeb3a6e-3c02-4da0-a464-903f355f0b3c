import { Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';

@Component({
  selector: 'widget-error',
  templateUrl: './error.component.html',
  styleUrls: ['./error.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ErrorComponent {
  title = input<string>('Error');
  message = input<string>('An error has occurred.');
  icon = input<string>('alertCircle');
  buttonText = input<string>('Go Back');
  showButton = input<boolean>(true);

  buttonClick = output<void>();
}
