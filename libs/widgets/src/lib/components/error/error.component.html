<div class="error-container">
  <div class="error-icon">
    <eds-icon [name]="icon()"></eds-icon>
  </div>
  <div class="error-content">
    <eds-text as="h1" size="xl" weight="medium" [text]="title()"></eds-text>
    <eds-text as="p" size="md" [text]="message()"></eds-text>
  </div>
  @if (showButton()) {
    <div class="error-actions">
      <eds-button appearance="default" (button-click)="buttonClick.emit()">
        {{ buttonText() }}
      </eds-button>
    </div>
  }
</div>
