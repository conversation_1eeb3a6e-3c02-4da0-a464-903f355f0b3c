.base {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: var(--eds-spacing-400);
  align-self: stretch;
}

.subscription-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}

.subscription-info-text {
  color: #242442;
  font-size: large;
}

.subscription-product-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.subscription-product-info-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.subscription-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  gap: var(--eds-spacing-200);

  .subscription-card {
    display: flex;
    align-items: center;
    align-self: stretch;
    padding: var(--eds-spacing-400);
    gap: var(--eds-spacing-600);
    border-radius: var(--eds-radius-300);
    border: 1px solid var(--eds-border-color-default);

    .subscription {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: var(--eds-spacing-400);
      flex: 1 0 0;

      .subscription-product-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .info-block {
        display: flex;
        flex-direction: column;
      }

      .info-item {
        display: flex;
        flex-direction: column;
        gap: var(--eds-spacing-100);
      }

      .info-label {
        color: var(--eds-colors-text-light);
      }

      .device {
        display: flex;
        padding: var(--eds-spacing-300);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--eds-spacing-300);
        align-self: stretch;
      }
    }
  }
}

.custom-subscription-info-text::part(base) {
  font-size: large;
  color: #242442;
}

.tooltip-icon {
  width: var(--eds-sizing-600);
  height: var(--eds-sizing-600);
  color: var(--eds-colors-text-light);
}
