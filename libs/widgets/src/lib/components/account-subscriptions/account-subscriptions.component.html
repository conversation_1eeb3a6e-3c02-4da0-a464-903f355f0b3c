<div class="base">
  @if (subscriptions().length > 0) {
    @if (!hideSubscriptionInfo()) {
      <div class="subscription-info">
        <eds-text
          size="lg"
          weight="medium"
          class="subscription-info-text"
          [text]="
            (subscriptions().length > 1 ? 'subscriptionListedBelow' : 'subscriptionListedBelowSingle') | translate
          "
        ></eds-text>

        @if (viewAll()) {
          <eds-button appearance="link" size="compact" (button-click)="toggleViewAll()">
            {{ 'hide' | translate }}
          </eds-button>
        } @else {
          @if (subscriptions().length > 1) {
            <eds-button appearance="link" size="compact" (button-click)="toggleViewAll()">
              {{ 'subscriptionViewAll' | translate: { count: hiddenSubscriptionsCount() } }}
            </eds-button>
          }
        }
      </div>
    }

    @if (hideSubscriptionInfo()) {
      <div class="custom-subscription-info">
        <eds-text
          size="lg"
          weight="medium"
          class="custom-subscription-info-text"
          [text]="customHeaderText() | translate"
        >
        </eds-text>
      </div>
    }
  }

  <div class="subscription-container">
    @for (subscription of visibleSubscriptions(); track $index) {
      <div class="subscription-card">
        <div class="subscription">
          <div class="subscription-product-info">
            <div class="info-block">
              <div class="info-item">
                <eds-text class="info-label" [text]="getSubscriptionLabel(subscription) | translate"></eds-text>
                <eds-text size="lg" weight="medium" [text]="getSubscriptionValue(subscription)"></eds-text>
              </div>
            </div>

            <div class="subscription-product-info-right">
              <eds-tag
                [content]="getDisplayStatusText(subscription.productStatus)"
                [appearance]="getColorByProductStatus(subscription.productStatus?.shortCode)"
              ></eds-tag>

              @if (isSuspended(subscription)) {
                <eds-tooltip placement="right">
                  <eds-icon class="tooltip-icon" name="informationCircle" slot="trigger"></eds-icon>
                  <div slot="content">
                    <eds-text [text]="getSuspendedReason(subscription)"></eds-text>
                  </div>
                </eds-tooltip>
              }
            </div>
          </div>
        </div>
      </div>
    }
  </div>
</div>
