import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  input,
  signal,
} from '@angular/core';
import { TranslatePipe, TranslateService } from '@libs/plugins';
import { getColorByProductStatus, PhoneNumberPipe } from '@libs/core';
import { BillingAccountInfo, eCommon, EPaymentTypeShortCode, eProduct, GeneralStatus } from '@libs/types';
@Component({
  selector: 'widget-account-subscriptions',
  templateUrl: './account-subscriptions.component.html',
  styleUrls: ['./account-subscriptions.component.scss'],
  imports: [TranslatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [PhoneNumberPipe],
})
export class AccountSubscriptionsComponent {
  eProduct = eProduct;
  subscriptions = input<BillingAccountInfo[]>([]);
  hideSubscriptionInfo = input<boolean>(false);
  customTitle = input<string>('');
  showCount = input<boolean>(false);
  defaultExpanded = input<boolean>(false);

  phoneNumberPipe = inject(PhoneNumberPipe);
  translateService = inject(TranslateService);

  viewAll = signal(false);

  getColorByProductStatus = getColorByProductStatus;
  EPaymentTypeShortCode = EPaymentTypeShortCode;

  constructor() {
    effect(() => {
      this.viewAll.set(this.defaultExpanded());
    });
  }

  visibleSubscriptions = computed(() => (this.viewAll() ? this.subscriptions() : this.subscriptions().slice(0, 1)));

  hiddenSubscriptionsCount = computed(() => this.subscriptions().length - this.visibleSubscriptions().length);

  customHeaderText = computed(() =>
    this.showCount()
      ? `${this.subscriptions().length} ${this.translateService.translate(this.customTitle())}`
      : this.translateService.translate(this.customTitle()),
  );

  toggleViewAll() {
    this.viewAll.set(!this.viewAll());
  }

  trackByIndex(index: number): number {
    return index;
  }

  getSubscriptionLabel(subscription: BillingAccountInfo): string {
    const shortCode = subscription?.subscriptionIdentities?.[0]?.productCharShortCode;

    if (shortCode === eCommon.ProductCharType.MSISDN) {
      return 'phoneNumber';
    }

    if (subscription?.serviceAddress) {
      return 'serviceAddress';
    }

    return shortCode || '';
  }

  getSubscriptionValue(subscription: BillingAccountInfo): string {
    const shortCode = subscription?.subscriptionIdentities?.[0]?.productCharShortCode;

    if (shortCode === eCommon.ProductCharType.MSISDN) {
      return this.phoneNumberPipe.transform(subscription.subscriptionIdentities[0].productCharValue);
    }

    const { addressDescription, countryName, cityName } = subscription.serviceAddress;

    let result = addressDescription || '';

    if (countryName) {
      result += ` - ${countryName}`;
      if (cityName) {
        result += `, ${cityName}`;
      }
    } else if (cityName) {
      result += `, ${cityName}`;
    }

    return result;
  }

  isMsisdn(subscription: BillingAccountInfo): boolean {
    return (
      subscription?.subscriptionIdentities?.[0]?.productCharShortCode?.toUpperCase() === eCommon.ProductCharType.MSISDN
    );
  }

  isSuspended(subscription: BillingAccountInfo): boolean {
    return subscription.productStatus?.shortCode === eProduct.ProductStatusShortCodes.SPND;
  }

  getSuspendedReason(subscription: BillingAccountInfo): string {
    const suspendReasonMessages = {
      SuspendForNonPayment: this.translateService.translate('suspendForNonPayment'),
      SuspendForFraud: this.translateService.translate('suspendForFraud'),
      ServicePause: this.translateService.translate('servicePause'),
    } as const;

    const uniqueReasonCodes = [
      ...new Set(
        subscription.accountSubStatus
          ?.filter((status) => status.accountSubStatusShortCode in suspendReasonMessages)
          ?.map((status) => status.accountSubStatusShortCode),
      ),
    ] as Array<keyof typeof suspendReasonMessages>;

    const messages = uniqueReasonCodes.map((reasonCode) => suspendReasonMessages[reasonCode]).filter(Boolean);

    return messages.join(',');
  }

  getDisplayStatusText(status: GeneralStatus): string {
    if (status?.shortCode === eProduct.ProductStatusShortCodes.CNCL) {
      return this.translateService.translate('deactivated').toUpperCase();
    }

    return status?.name?.toUpperCase();
  }
}
