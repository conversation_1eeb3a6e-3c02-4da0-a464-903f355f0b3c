import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  input,
  OnInit,
  output,
  signal,
} from '@angular/core';
import { ItemDeleteActionComponent } from '../item-delete-action';
import { TranslateService } from '@libs/plugins';
import { BusinessWorkflowStepService } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';
@Component({
  selector: 'widget-saved-payment-method',
  templateUrl: './saved-payment-method.component.html',
  styleUrls: ['./saved-payment-method.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ItemDeleteActionComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SavedPaymentMethodComponent implements OnInit {
  translateService = inject(TranslateService);
  stepService = inject(BusinessWorkflowStepService);

  id = input<string | number>('');
  name = input<string>();
  nameOnBankAccount = input<string>();
  inputName = input<string>();
  value = input<string | number>();
  isChecked = input<boolean>();
  isDisabled = input<boolean>();
  isInvalid = input<boolean>();
  networkLogo = input<string>();
  cardNumber = input<string>();
  cardExpiry = input<string>();
  isRemovable = input<boolean>();
  deleteConfirmationText = input<string>(this.translateService.translate('yesDelete'));
  isSelected = input<boolean>();

  delete = output<string | number>();
  selectionChange = output<string>();

  deleteActionTriggered = signal(false);

  ngOnInit() {
    if (this.isChecked()) {
      this.selectionChange.emit(this.value()?.toString());
    }
  }

  onDelete() {
    this.delete.emit(this.id());
  }

  onSelectionChange(event: Event) {
    this.selectionChange.emit((event.target as HTMLInputElement).value);
  }

  protected readonly eBusinessFlow = eBusinessFlow;
}
