<label class="base" [for]="id()">
  <input
    type="radio"
    [id]="id()"
    [name]="inputName()"
    [value]="value()"
    [checked]="isChecked()"
    (change)="onSelectionChange($event)"
  />
  <div class="content">
    @if (networkLogo()) {
      <eds-image class="network" [src]="networkLogo()" [alt]="'Network Logo'" width="40"></eds-image>
    }
    <div class="card-details">
      <eds-text [size]="'md'" [weight]="'medium'" [text]="nameOnBankAccount() || name()"></eds-text>
      <div class="card-number">
        @if (nameOnBankAccount()) {
          <div class="bank-name">
            <eds-text [size]="'sm'" [text]="name()"></eds-text>
          </div>
        }
        <eds-text [size]="'sm'" [text]="cardNumber()"></eds-text>
        @if (cardExpiry()) {
          <eds-text [size]="'sm'" [text]="cardExpiry()"></eds-text>
        }
      </div>
    </div>
  </div>
  @if (
    (!isSelected() && stepService?.activeStep()?.shortCode === eBusinessFlow.WorkflowStateType.PAYMENT_BILLING) ||
    stepService?.activeStep()?.shortCode === eBusinessFlow.WorkflowStateType.CREDIT_CHECK
  ) {
    <widget-item-delete-action [deleteConfirmationText]="deleteConfirmationText()" (deleteItem)="onDelete()">
    </widget-item-delete-action>
  }
</label>
