:host {
  width: 100%;
}

.base {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  height: calc(var(--eds-size-multiplier) * 35);
  width: 100%;

  &:before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 85%;
    z-index: 1;
    background: linear-gradient(
      0deg,
      var(--eds-colors-surface-default) 24%,
      oklch(from var(--eds-colors-surface-default) l c h / 0) 100%
    );
    pointer-events: none;
  }

  .checkmark {
    width: calc(var(--eds-size-multiplier) * 14);
    height: calc(var(--eds-size-multiplier) * 14);
    z-index: 1;
  }

  .stars {
    position: absolute;
    inset: 0;
    pointer-events: none;
    z-index: 1;

    svg {
      position: absolute;
      animation: starfall var(--duration) linear infinite;
      transform-origin: center;
      color: var(--eds-colors-success-light);
      --scale: 1;

      &:nth-child(1) {
        top: 52px;
        left: 148px;
        --opacity: 1;
        --duration: 4s;
      }
      &:nth-child(2) {
        top: 20px;
        left: 194px;
        --opacity: 1;
        --duration: 2.7s;
      }
      &:nth-child(3) {
        top: 65px;
        left: 299px;
        --opacity: 1;
        --duration: 1.8s;
      }
      &:nth-child(4) {
        top: 59px;
        left: 309px;
        --scale: calc(5 / 7);
        --opacity: 1;
        --duration: 2.9s;
      }
      &:nth-child(5) {
        top: 24px;
        left: 346px;
        --scale: calc(5 / 7);
        --opacity: 1;
        --duration: 3.3s;
      }
      &:nth-child(6) {
        top: 74px;
        left: 180px;
        --opacity: 1;
        --duration: 2.1s;
      }
    }
  }

  .circle {
    position: absolute;
    pointer-events: none;

    &:nth-child(1) {
      width: calc(var(--eds-size-multiplier) * 33);
      height: calc(var(--eds-size-multiplier) * 33);

      &:before {
        --scale: 0.96969696969;
        animation: waterDropWave 2.8s infinite ease-out 0.8s;
      }

      &:after {
        --scale: 1.69696969697;
        animation: waterDropWave 2.8s infinite ease-out 0.8s;
      }
    }

    &:nth-child(2) {
      width: calc(var(--eds-size-multiplier) * 86);
      height: calc(var(--eds-size-multiplier) * 86);

      &:before {
        --scale: 0.93023255814;
        animation: waterDropWave 2.8s infinite ease-out 0.4s;
      }

      &:after {
        --scale: 1.20930232558;
        animation: waterDropWave 2.8s infinite ease-out 0.4s;
      }
    }

    &:nth-child(3) {
      width: calc(var(--eds-size-multiplier) * 134);
      height: calc(var(--eds-size-multiplier) * 134);

      &:before {
        --scale: 0.95522388059;
        animation: waterDropWave 2.8s infinite ease-out;
      }

      &:after {
        --scale: 1.13432835821;
        animation: waterDropWave 2.8s infinite ease-out;
      }
    }

    &:after,
    &:before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      border: var(--eds-stroke-025) solid var(--eds-colors-success-light);
      width: 100%;
      height: 100%;
      opacity: 0;
    }
  }
}

@keyframes waterDropWave {
  0% {
    transform: translate(-50%, -50%) scale(calc(var(--scale) * 0.2));
    opacity: 0;
  }
  24% {
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(calc(var(--scale) * 1.2));
    opacity: 0;
  }
}

@keyframes starfall {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  20% {
    opacity: var(--opacity);
  }
  80% {
    opacity: var(--opacity);
  }
  100% {
    opacity: 0;
    transform: scale(var(--scale));
  }
}
