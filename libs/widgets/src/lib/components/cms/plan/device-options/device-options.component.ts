import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  input,
  output,
  signal,
} from '@angular/core';
import { Cms } from '../../../../model';
import { CmsDeviceCardComponent } from '../../device-card';

@Component({
  selector: 'widget-cms-plan-device-options',
  templateUrl: './device-options.component.html',
  styleUrls: ['./device-options.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CmsDeviceCardComponent],
})
export class CmsPlanDeviceOptionsComponent {
  initialPlansCount = input<number>(8);
  loadMoreCount = input<number>(24);
  devices = input<Cms.Device[]>([]);
  itemAmount = input<string>();

  onSelect = output<string>();

  visibleDevicesCount = signal<Record<string, number>>({});

  hasMoreDevices = computed(() => {
    return this.devices().length > this.getVisibleCount();
  });

  visibleDevices = computed(() => {
    const allDevices = this.devices();
    return allDevices
      .map((device) => ({
        ...device,
      }))
      .slice(0, this.getVisibleCount());
  });

  getVisibleCount(): number {
    // If any device has a specific count, use the maximum of those
    const counts = Object.values(this.visibleDevicesCount());
    return counts.length > 0 ? Math.max(...counts) : this.initialPlansCount();
  }

  loadMore(): void {
    const currentCount = this.getVisibleCount();
    this.visibleDevicesCount.update((counts) => ({
      ...counts,
      global: currentCount + this.loadMoreCount(),
    }));
  }
}
