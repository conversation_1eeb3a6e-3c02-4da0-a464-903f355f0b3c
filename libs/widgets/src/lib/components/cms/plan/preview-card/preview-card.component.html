<div class="base">
  @if (title()) {
    <eds-heading class="title" size="md" [text]="title()"></eds-heading>
  }
  <div class="card">
    <div class="content">
      @if (icon()) {
        <eds-icon [name]="icon()"></eds-icon>
      }
      <div class="details">
        <div class="properties">
          <eds-heading size="xs" [text]="plan()"></eds-heading>
          <div class="property-items">
            @for (property of properties(); track $index) {
              <div class="property-item">
                <eds-text class="property-item-key" size="sm" weight="regular" [text]="property.label + ':'"></eds-text>
                <eds-text size="sm" weight="regular" [text]="property.value"></eds-text>
              </div>
            }
          </div>
        </div>
        <eds-text size="lg" weight="medium" [text]="price()"></eds-text>
      </div>
    </div>
    <eds-button
      (button-click)="onEdit()"
      class="edit"
      appearance="subtle"
      size="compact"
      iconOnly
      iconLeading="pencilEdit"
    ></eds-button>
  </div>
  @if (continueButton()) {
    <eds-button appearance="primary" (button-click)="onContinue()">Continue without smartphone</eds-button>
  }
</div>
