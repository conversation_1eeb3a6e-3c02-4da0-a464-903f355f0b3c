import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { Cms } from '../../../../model/cms-plan.model';

@Component({
  selector: 'widget-cms-plan-preview-card',
  templateUrl: './preview-card.component.html',
  styleUrls: ['./preview-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsPlanPreviewCardComponent {
  title = input<string>();
  plan = input<string>();
  icon = input<string>();
  properties = input<Cms.PlanPreviewCardProperty[]>();
  price = input<string>();
  continueButton = input<boolean>();

  continue = output<void>();
  edit = output<void>();

  onContinue() {
    this.continue.emit();
  }

  onEdit() {
    this.edit.emit();
  }
}
