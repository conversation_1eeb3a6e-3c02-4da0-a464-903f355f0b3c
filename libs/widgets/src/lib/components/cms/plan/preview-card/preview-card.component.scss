.base {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--eds-spacing-600);
  .title::part(base) {
    text-align: center;
  }
  .card {
    display: flex;
    justify-content: space-between;
    gap: var(--eds-spacing-400);
    padding: var(--eds-spacing-300);
    border-radius: var(--eds-radius-300);
    border: 1px solid var(--eds-border-color-light);
    width: 100%;
    .content {
      display: flex;
      align-items: center;
      gap: var(--eds-spacing-400);
      eds-icon {
        display: none;
        padding: var(--eds-spacing-300);
        border-radius: var(--eds-radius-full);
        background-color: var(--eds-colors-primary-lighter);
        flex-shrink: 0;
        width: var(--eds-sizing-600);
        height: var(--eds-sizing-600);

        @media (min-width: 834px) {
          display: block;
        }

        &::part(base) {
          color: var(--eds-colors-icon-light);
        }
      }
      .details {
        display: grid;
        grid-template-columns: minmax(0, 100%);
        gap: var(--eds-spacing-200);
        flex-shrink: 0;
        width: 100%;

        @media (min-width: 834px) {
          width: clamp(calc(var(--eds-size-multiplier) * 100), 100%, calc(var(--eds-size-multiplier) * 120));
        }
        .properties {
          display: flex;
          flex-direction: column;
          gap: var(--eds-spacing-100);
          .property-items {
            display: flex;
            gap: var(--eds-spacing-200);
            flex-wrap: wrap;
            width: 100%;
            @media (min-width: 834px) {
              align-items: center;
              flex-direction: row;
            }
            .property-item {
              display: flex;
              align-items: center;
              gap: var(--eds-spacing-100);
              .property-item-key::part(base) {
                color: var(--eds-colors-text-light);
              }
            }
          }
        }
      }
    }

    @media (min-width: 834px) {
      max-width: 80%;
    }
  }
}
