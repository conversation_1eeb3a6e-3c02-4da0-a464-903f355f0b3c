import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  input,
  signal,
  computed,
  output,
} from '@angular/core';
import { Cms } from '../../../../model';
import { CurrencyPipe } from '@angular/common';
import { DEFAULT_CURRENCY_CODE } from '@libs/types';

@Component({
  selector: 'widget-cms-plan-catalog-detail',
  templateUrl: './catalog-detail.component.html',
  styleUrls: ['./catalog-detail.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CurrencyPipe],
})
export class CmsPlanCatalogDetailComponent {
  advantagesTitle = input('');
  advantages = input<Cms.PlanAdvantageTile[]>([]);
  appsTitle = input('');
  apps = input<Cms.PlanAppAdvantages[]>([]);
  benefitsTitle = input('');
  benefits = input<string[]>();
  benefit_icon = input<string>('check');
  informationTitle = input('');
  information = input<string[]>([]);
  additionalInformation = input<Cms.PlanAdditionalInformation[]>([]);
  commitmentTitle = input('');
  commitment = input<Cms.PlanCommitment[]>([]);
  priceTitle = input('');
  orderButton = input('');
  currency = input<string>(DEFAULT_CURRENCY_CODE);

  orderNow = output<void>();

  selectedCommitmentIndex = signal<number>(0);

  selectedCommitment = computed(() => {
    return this.commitment()[this.selectedCommitmentIndex() ?? 0];
  });

  price = computed(() => {
    return this.selectedCommitment()?.discountPrice ?? this.selectedCommitment()?.price;
  });

  selectCommitment(index: number = null): void {
    this.selectedCommitmentIndex.set(index);
  }
}
