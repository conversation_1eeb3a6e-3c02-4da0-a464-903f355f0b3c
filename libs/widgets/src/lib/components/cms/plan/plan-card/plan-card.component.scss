.base {
  display: grid;
  gap: var(--eds-spacing-600);
  width: 100%;
}

.summary {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: var(--eds-spacing-600);
  border-bottom: 1px solid var(--eds-border-color-default);
  padding-bottom: var(--eds-spacing-600);
  height: 100%;
}

.packages {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
  height: 100%;
}

.price {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: var(--eds-spacing-200);
  padding: var(--eds-spacing-300);
  border-radius: var(--eds-radius-200);
  background-color: var(--eds-colors-surface-level-1);

  .price-value {
    display: flex;
    align-items: baseline;
  }

  .discount {
    margin-left: var(--eds-spacing-100);
    text-decoration: line-through;
  }

  .description {
    color: var(--eds-colors-text-light);
  }
}

.actions {
  display: grid;
  gap: var(--eds-spacing-200);
}

.apps {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-300);
  flex-grow: 0;

  .items {
    display: flex;
    align-items: center;
    gap: var(--eds-spacing-200);

    eds-image {
      width: var(--eds-sizing-600);
      height: var(--eds-sizing-600);
    }
  }
}

eds-card {
  display: grid;
  height: 100%;
}

eds-card::part(tag) {
  max-width: calc(var(--eds-size-multiplier) * 27);
}

eds-card:hover::part(tag) {
  max-width: unset;
}
