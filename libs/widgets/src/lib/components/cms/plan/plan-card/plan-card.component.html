<eds-card [hoverBorderColor]="'var(--eds-colors-secondary-default)'" [tags]="tags()">
  <eds-heading size="sm" [text]="title()"></eds-heading>
  <div class="summary">
    <div class="packages">
      @if (subTitle()) {
        <eds-text size="lg" [text]="subTitle()"></eds-text>
      }
      @for (advantage of advantages(); track $index) {
        <eds-media-object
          [text]="advantage.title"
          [description]="advantage.description"
          [iconName]="advantage.icon.isIcon ? advantage.icon.value : undefined"
          [src]="advantage.icon.isIcon ? undefined : advantage.icon.value"
          width="24"
          height="24"
        >
        </eds-media-object>
      }
    </div>
    @if (apps()?.length) {
      <div class="apps">
        <eds-text size="lg" [text]="appsTitle()"></eds-text>
        <div class="items">
          @for (app of apps(); track $index) {
            <eds-image [src]="app.image" [alt]="app.alt"></eds-image>
          }
        </div>
      </div>
    }
  </div>
  <div class="price">
    <span class="price-value">
      <eds-heading class="base-price" size="sm" [text]="priceText()"></eds-heading>
      @if (price().showMonthly !== false) {
        <eds-text as="span" size="md" [text]="'mth '"></eds-text>
      }
      @if (discountValue()) {
        <eds-text class="discount" as="span" size="md" [text]="discountValue()"></eds-text>
      }
    </span>

    @if (price().description) {
      <eds-text class="description" size="sm" [text]="price().description"></eds-text>
    }

    @if (price().link) {
      <eds-button class="link" appearance="link" [iconLeading]="price().link.icon">{{ price().link.text }}</eds-button>
    }
  </div>
  <div class="actions">
    @for (action of actions(); track $index) {
      @if (action?.type === 'buttonWithCounter') {
        <widget-eds-button-with-counter
          [label]="action.text"
          [counter]="action.counter"
          (onValueChanged)="action.action?.({ id: id(), counter: $event })"
        ></widget-eds-button-with-counter>
      } @else {
        <eds-button
          shouldFitContainer
          [appearance]="action.appearance"
          (button-click)="action.action?.({ id: id() })"
          >{{ action.text }}</eds-button
        >
      }
    }
  </div>
</eds-card>
