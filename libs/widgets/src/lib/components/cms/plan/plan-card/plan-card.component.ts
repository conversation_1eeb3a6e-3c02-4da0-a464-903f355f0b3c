import { CurrencyPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { Cms } from '../../../../model';
import { EdsButtonWithCounterComponent } from '@libs/widgets';

@Component({
  selector: 'widget-cms-plan-card',
  templateUrl: './plan-card.component.html',
  styleUrls: ['./plan-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [CurrencyPipe],
  imports: [EdsButtonWithCounterComponent],
})
export class CmsPlanCardComponent {
  private currencyPipe = inject(CurrencyPipe);

  id = input<number>();
  title = input('');
  subTitle = input('');
  advantages = input<Cms.PlanAdvantages[]>([]);
  appsTitle = input('');
  apps = input<Cms.PlanAppAdvantages[]>([]);
  price = input<Cms.PlanPrice>();
  actions = input<Cms.PlanAction[]>([]);
  tags = input<Cms.PlanTag[]>([]);

  discountValue = computed(() => {
    return this.price().discount ? this.currencyPipe.transform(this.price().price, this.price().currency) : undefined;
  });

  priceValue = computed(() => {
    return this.price().discount ?? this.price().price;
  });

  priceText = computed(() => {
    const price = this.currencyPipe.transform(this.priceValue(), this.price().currency);
    return price + (this.price().showMonthly === false ? '' : '/');
  });
}
