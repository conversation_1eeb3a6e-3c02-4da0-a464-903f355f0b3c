eds-tabs::part(base) {
  gap: 2rem;
}

eds-tabs::part(tab-list) {
  justify-content: flex-start;
}

eds-tabs::part(tab-panels) {
  container-name: catalog;
  container-type: inline-size;
}

eds-tab-panel::part(base) {
  display: grid;
  grid-template-columns: minmax(0, 100%);
  gap: 1.5rem;
}

@media (min-width: 834px) {
  eds-tab-panel::part(base) {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 100%));
    gap: 1.5rem;
  }
}

@media (min-width: 1440px) {
  eds-tab-panel::part(base) {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 100%));
    gap: 1.5rem;
  }
}

:host ::ng-deep {
  eds-tab {
    flex-shrink: 0;
    &.loading {
      pointer-events: none;
      opacity: 0.5;
    }
  }
  ngx-skeleton-loader {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    margin-top: 1rem;
    .skeleton-loader {
      flex: 1 1 auto;
      border-radius: var(--eds-radius-400) !important;
      height: 400px !important;
    }
  }
}

.show-more-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(var(--eds-size-multiplier) * 18);
  grid-column: 1 / -1;
  background-color: var(--eds-colors-body);
  border: 1px solid var(--eds-border-color-default);
  padding: var(--eds-spacing-600);
  border-radius: var(--eds-radius-400);
}

.show-more-button:hover {
  background-color: var(--eds-colors-primary-light);
}
