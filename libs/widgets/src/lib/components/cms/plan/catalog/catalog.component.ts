import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  input,
  output,
  signal,
} from '@angular/core';
import { CmsPlanCardComponent } from '../plan-card/plan-card.component';
import { Cms } from '../../../../model';
import { NgxSkeletonLoaderComponent } from 'ngx-skeleton-loader';

@Component({
  selector: 'widget-cms-plan-catalog',
  templateUrl: './catalog.component.html',
  styleUrls: ['./catalog.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CmsPlanCardComponent, NgxSkeletonLoaderComponent],
})
export class CmsPlanCatalogComponent {
  initialPlansCount = input<number>(3);
  loadMoreCount = input<number>(15);
  tabs = input<Cms.PlansTab[]>([]);
  loading = input<boolean>(false);
  hasMorePlans = input<boolean>(false);

  onTabChange = output<string>();
  onLoadMore = output<string>();

  visiblePlansCount = signal<Record<string, number>>({});

  visibleTabs = computed(() =>
    this.tabs().map((tab) => {
      const count = this.visiblePlansCount()[tab.title] || this.initialPlansCount();
      return {
        ...tab,
        visiblePlans: tab.plans.slice(0, count),
        hasMorePlans: tab.plans.length > count,
      };
    }),
  );

  loadMore(tabTitle: string): void {
    const currentCount = this.visiblePlansCount()[tabTitle] || this.initialPlansCount();

    this.visiblePlansCount.update((counts) => ({
      ...counts,
      [tabTitle]: currentCount + this.loadMoreCount(),
    }));
    this.onLoadMore.emit(tabTitle);
  }

  onTabClick(e: Event) {
    const event = e as CustomEvent<{ selectedTab: { id: string } }>;
    this.onTabChange.emit(event.detail.selectedTab.id);
  }
}
