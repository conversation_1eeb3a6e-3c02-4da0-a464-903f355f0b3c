import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { FaqActions, FaqItem } from '../../../model';
import { SafePipe } from '@libs/core';
import { AccordionGroupComponent } from '../../accordion-group';

@Component({
  selector: 'widget-cms-faq',
  templateUrl: './faq.component.html',
  styleUrls: ['./faq.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AccordionGroupComponent, SafePipe],
})
export class CmsFaqComponent {
  title = input('');
  faqs = input<FaqItem[]>([]);
  buttons = input<FaqActions[]>([]);
}
