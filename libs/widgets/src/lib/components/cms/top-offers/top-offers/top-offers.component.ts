import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { TopOffer } from '../../../../model';
import { TopOfferComponent } from '../top-offer/top-offer.component';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-cms-top-offers',
  templateUrl: './top-offers.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./top-offers.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TopOfferComponent, TranslatePipe],
})
export class TopOffersComponent {
  topOffers = input<TopOffer[]>([]);
  viewAllUrl = input<string>();
}
