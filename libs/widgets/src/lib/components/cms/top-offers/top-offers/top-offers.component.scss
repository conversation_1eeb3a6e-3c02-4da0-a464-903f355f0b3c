.base {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--eds-spacing-700);
  align-self: stretch;
}

.header-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--eds-spacing-200);
  align-self: stretch;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--eds-spacing-200);

  eds-heading::part(base) {
    color: var(--eds-colors-text-dark);
    font-size: calc(var(--eds-size-multiplier) * 12);
    line-height: calc(var(--eds-size-multiplier) * 14);
    font-weight: 600;
    letter-spacing: -1px;
  }
}

.content {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--eds-spacing-600);
  width: 100%;

  @media (min-width: 834px) {
    grid-template-columns: repeat(2, 1fr);
  }
}
