.card {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-600);
  padding: var(--eds-spacing-700);
  border-radius: var(--eds-radius-600);
  background-color: var(--eds-colors-surface-level-2);
  background-position: 50%;
  background-size: cover;
  background-repeat: no-repeat;
  min-height: calc(var(--eds-size-multiplier) * 100);

  .content {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);
    flex: 1;
  }

  eds-button {
    &::part(base) {
      color: inherit;
      text-decoration: none;
      background-color: rgb(239, 239, 246, 0.3);
      transition: all 0.4s ease-in-out;
    }

    &::part(icon) {
      transition: all 0.4s ease-in-out;
      opacity: 0;
      width: 0;
      visibility: hidden;
    }

    &:hover::part(icon) {
      opacity: 1;
      width: auto;
      visibility: visible;
    }

    eds-link::part(base) {
      color: inherit;
      text-decoration: none;
    }
  }
}
