import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { CmsHighlight } from '../../../model';

@Component({
  selector: 'widget-cms-features-strip',
  templateUrl: './features-strip.component.html',
  styleUrls: ['./features-strip.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsFeaturesStripComponent {
  highlights = input<CmsHighlight[]>([]);
}
