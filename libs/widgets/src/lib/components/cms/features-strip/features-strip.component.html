<div class="base">
  <div class="highlights">
    <div class="highlights-wrapper">
      @for (highlight of highlights(); track highlight) {
        <div class="highlight">
          <img [src]="highlight.imageUrl" />
          <eds-text [text]="highlight.text"></eds-text>
        </div>
      }
      @for (highlight of highlights(); track highlight) {
        <div class="highlight">
          <img [src]="highlight.imageUrl" />
          <eds-text [text]="highlight.text"></eds-text>
        </div>
      }
      @for (highlight of highlights(); track highlight) {
        <div class="highlight">
          <img [src]="highlight.imageUrl" />
          <eds-text [text]="highlight.text"></eds-text>
        </div>
      }
    </div>
  </div>
</div>
