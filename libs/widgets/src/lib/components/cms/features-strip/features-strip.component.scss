.base {
  display: grid;
  justify-items: center;

  .highlights {
    position: relative;
    display: grid;
    justify-content: center;
    grid-template-columns: minmax(0, calc(var(--eds-size-multiplier) * 288));
    gap: var(--eds-spacing-400);
    background-color: var(--eds-colors-secondary-default);
    width: 100%;
    overflow: hidden;

    eds-text {
      color: var(--eds-colors-text-white);
    }

    .navigation {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      max-width: calc(var(--eds-size-multiplier) * 288);
      z-index: 2;

      .navigation-previous,
      .navigation-next {
        position: absolute;
        width: 24px;
        height: 24px;
        flex-shrink: 0;
        background-color: var(--eds-colors-secondary-default);
        color: var(--eds-colors-text-white);
      }

      .navigation-previous {
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }

      .navigation-next {
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .highlights-wrapper {
      display: flex;
      align-items: center;
      padding: 16px 48px;
      width: max-content;
      overflow: visible;
      white-space: nowrap;
      animation: marquee 22s linear infinite;

      @media (min-width: 834px) {
        animation-duration: 44s;
      }

      &:hover {
        animation-play-state: paused;
      }

      &::-webkit-scrollbar {
        display: none;
      }

      .highlight {
        display: flex;
        align-items: center;
        gap: var(--eds-spacing-300);
        flex-shrink: 0;
        width: 25%;
      }
    }
  }

  @keyframes marquee {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(calc(-200% + 96px));
    }
  }
}
