import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  input,
  model,
  output,
} from '@angular/core';
import { CmsPlanPreviewCardComponent } from '@libs/widgets';
import { Cms } from '../../../../model/cms-plan.model';
import { CurrencyPipe } from '@angular/common';

@Component({
  selector: 'widget-cms-device-properties',
  templateUrl: './device-properties.component.html',
  styleUrls: ['./device-properties.component.scss'],
  imports: [CmsPlanPreviewCardComponent, CurrencyPipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsDevicePropertiesComponent {
  tags = input<Cms.PlanTag[]>();
  plan = input<Cms.PlanPreviewCard>();
  name = input<string>();
  price = input<{ value: number | string; unit: string }>();
  discountPrice = input<{ value: number | string; unit: string }>();
  period = input<string>();
  availability = input<string>();
  colors = model<Cms.SelectOption[]>();
  storages = input<Cms.SelectOption[]>();
  installments = input<Cms.SelectOption[]>();
  installmentInfo = input<{ icon: string; text: string }[]>();

  onStorageChange = output<string>();
  onColorChange = output<string>();
  onInstallmentChange = output<string>();
  onSelect = output();

  choosenColor = computed(() => this.colors().find((color) => color.isSelected));

  calculatePrice = computed(() => {
    if (this.discountPrice()) {
      return this.discountPrice();
    }
    return this.price();
  });

  calculateDiscountPrice = computed(() => {
    if (this.discountPrice()) {
      return this.price();
    }
    return undefined;
  });
}
