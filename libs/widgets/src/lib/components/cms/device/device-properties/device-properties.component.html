<div class="base">
  <div class="product">
    @for (tag of tags(); track $index) {
      <eds-tag [content]="tag.text" [appearance]="tag.appearance"></eds-tag>
    }
    <div class="product-info">
      <eds-text size="lg" weight="medium" [text]="name()"></eds-text>
      @if (calculatePrice()?.value === 'NOT_ELIGIBLE') {
        <eds-text size="lg" weight="medium" [text]="'Not Eligible'"></eds-text>
      } @else if (calculatePrice()?.value) {
        <div class="price">
          <eds-heading size="md" [text]="calculatePrice().value | currency: calculatePrice().unit"></eds-heading>
          <eds-text size="md" [text]="'/' + period()"></eds-text>
          @if (calculateDiscountPrice()) {
            <eds-text
              size="md"
              [text]="calculateDiscountPrice().value | currency: calculateDiscountPrice().unit"
            ></eds-text>
          }
        </div>
      }
      <div class="availability">
        <span [class]="'indicator ' + availability()"></span>
        <eds-text size="sm" [text]="availability()"></eds-text>
      </div>
    </div>
  </div>
  <div class="colors">
    <eds-text size="lg" weight="medium" [text]="'Which color would you like?'"></eds-text>
    <eds-radio-group>
      @for (color of colors(); track $index) {
        <eds-radio
          style="--color: {{ color.value }}"
          name="color"
          [value]="color.label"
          [isChecked]="color.isSelected"
          (radio-change)="onColorChange.emit(color.value)"
        >
        </eds-radio>
      }
    </eds-radio-group>
    <div class="choosen-color">
      <eds-text class="color-key" size="sm" [text]="'Color:'"></eds-text>
      <eds-text class="color-name" size="sm" [text]="choosenColor()?.label"></eds-text>
    </div>
  </div>
  <div class="storage">
    <eds-text size="lg" weight="medium" [text]="'How much storage do you need?'"></eds-text>
    <eds-radio-group>
      @for (storage of storages(); track $index) {
        <eds-radio
          id="storage-{{ storage.value }}"
          name="storage"
          [value]="storage.value"
          [isChecked]="storage.isSelected"
          (radio-change)="onStorageChange.emit(storage.value)"
        >
          <label for="storage-{{ storage.value }}">
            <eds-text size="md" [text]="storage.label"></eds-text>
          </label>
        </eds-radio>
      }
    </eds-radio-group>
  </div>
  <div class="installment">
    <eds-text size="lg" weight="medium" [text]="'Installment Plan'"></eds-text>
    <eds-radio-group>
      @for (installment of installments(); track $index) {
        <eds-radio
          id="installment-{{ installment.value }}"
          name="installment"
          [value]="installment.value"
          [isChecked]="installment.isSelected"
          (radio-change)="onInstallmentChange.emit(installment.value)"
        >
          <label for="installment-{{ installment.value }}">
            <eds-text size="md" [text]="installment.label"></eds-text>
          </label>
        </eds-radio>
      }
    </eds-radio-group>
    <div class="installment-info">
      @for (info of installmentInfo(); track $index) {
        <div class="installment-info-item">
          <eds-icon [name]="info.icon"></eds-icon>
          <eds-text size="md" [text]="info.text"></eds-text>
        </div>
      }
    </div>
  </div>
  <widget-cms-plan-preview-card
    [plan]="plan().plan"
    [properties]="plan().properties"
    [price]="plan().price"
    [continueButton]="false"
    (edit)="plan().edit?.()"
  ></widget-cms-plan-preview-card>
  <eds-button
    shouldFitContainer
    appearance="primary"
    (button-click)="onSelect.emit()"
    [disabled]="!calculatePrice()?.value || calculatePrice().value === 'NOT_ELIGIBLE'"
  >
    @if (calculatePrice()?.value && calculatePrice().value !== 'NOT_ELIGIBLE') {
      Proceed with {{ calculatePrice().value | currency: calculatePrice().unit }}/month
    } @else {
      Not Eligible
    }
  </eds-button>
</div>
