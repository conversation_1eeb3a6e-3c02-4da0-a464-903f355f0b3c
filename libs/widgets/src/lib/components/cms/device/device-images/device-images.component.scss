.base {
  position: relative;
  background: var(--eds-colors-body);
  padding: var(--eds-spacing-600);
  display: flex;
  align-items: center;
  flex-direction: column-reverse;
  border-radius: var(--eds-radius-600);

  .navigation {
    display: flex;
    align-items: center;
    gap: var(--eds-spacing-700);

    .images {
      display: grid;
      gap: var(--eds-spacing-200);
      grid-template-columns: repeat(4, 1fr);

      eds-image {
        display: flex;
        align-items: center;
        justify-content: center;
        width: calc(var(--eds-size-multiplier) * 12);
        height: calc(var(--eds-size-multiplier) * 12);
        padding: var(--eds-spacing-100);
        border: 1px solid var(--eds-border-color-light);
        border-radius: var(--eds-radius-300);

        &::part(image) {
          max-width: 100%;
          max-height: 100%;
          object-fit: cover;
        }

        &.show-more {
          position: relative;
          overflow: hidden;

          &:after {
            content: '+' attr(data-count);
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--eds-colors-body);
            background: oklch(from var(--eds-black) l c h / 0.4);
          }
        }
      }
    }

    .actions {
      display: grid;
      gap: var(--eds-spacing-200);
    }
  }

  .preview-image {
    position: unset;
    top: 50%;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    padding: var(--eds-spacing-600);
    width: 100%;
    &::part(image) {
      max-width: calc(var(--eds-size-multiplier) * 90);
      max-height: calc(var(--eds-size-multiplier) * 90);
      width: calc(100% - var(--eds-spacing-600) * 2);
      object-fit: contain;
    }
  }

  @media (min-width: 834px) {
    align-items: unset;
    flex-direction: row;
    .navigation {
      display: grid;

      .images {
        grid-template-columns: unset;

        eds-image {
          width: calc(var(--eds-size-multiplier) * 16);
          height: calc(var(--eds-size-multiplier) * 16);
          padding: var(--eds-spacing-300);
        }
      }
    }

    .preview-image {
      position: absolute;
      transform: translate(-50%, -50%);
    }
  }
}

.slider-overlay {
  position: fixed;
  inset: 0;
  background-color: oklch(from var(--eds-colors-primary-light) l c h / 0.56);
  backdrop-filter: blur(calc(var(--eds-size-multiplier) * 1.5));
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-container {
  width: 100%;
  height: 100%;
  max-width: calc(var(--eds-size-multiplier) * 288);
  background-color: var(--eds-colors-surface);
  border-radius: var(--eds-radius-400);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding-inline: var(--eds-spacing-600);
}

.slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-block: var(--eds-spacing-400);
  border-bottom: 1px solid oklch(from var(--eds-colors-primary-default) l c h / 0.6);
}

.slider-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: var(--eds-spacing-600);

  .slider-image {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--eds-spacing-600);
    max-width: calc(var(--eds-size-multiplier) * 120);
    max-height: calc(var(--eds-size-multiplier) * 120);
    width: 100%;

    &::part(image) {
      max-width: 100%;
      max-height: 100%;
      width: calc(100% - var(--eds-spacing-600) * 2);
      object-fit: cover;
    }
  }

  .nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);

    &.prev {
      left: var(--eds-spacing-400);
    }

    &.next {
      right: var(--eds-spacing-400);
    }
  }
}

.slider-thumbnails {
  display: flex;
  gap: var(--eds-spacing-200);
  padding-block: var(--eds-spacing-400);
  overflow-x: auto;
  border-top: 1px solid oklch(from var(--eds-colors-primary-default) l c h / 0.6);

  .thumbnail {
    display: flex;
    align-items: center;
    justify-content: center;
    width: calc(var(--eds-size-multiplier) * 16);
    height: calc(var(--eds-size-multiplier) * 16);
    flex-shrink: 0;
    border: 1px solid oklch(from var(--eds-colors-primary-default) l c h / 0.6);
    border-radius: var(--eds-radius-300);
    padding: var(--eds-spacing-200);
    cursor: pointer;

    &.active {
      background-color: oklch(from var(--eds-colors-primary-darker) l c h / 0.6);
    }

    &::part(image) {
      max-width: 100%;
      max-height: 100%;
      width: calc(100% - var(--eds-spacing-200) * 2);
      object-fit: cover;
    }
  }
}
