<div class="base">
  <div class="navigation">
    <div class="images">
      @if (images().length > 4) {
        @for (image of images().slice(0, 3); track image.id) {
          <eds-image [src]="image.src" [id]="image.id" (click)="selectImage(image.id)"></eds-image>
        }
        @for (image of images().slice(3, 4); track image.id) {
          <eds-image
            #showMore
            class="show-more"
            [src]="image.src"
            [id]="image.id"
            (click)="openImageSlider()"
          ></eds-image>
        }
      } @else {
        @for (image of images(); track image.id) {
          <eds-image [src]="image.src" [id]="image.id" (click)="selectImage(image.id)"></eds-image>
        }
      }
    </div>
    <div class="actions">
      <eds-button appearance="default" circle iconOnly iconLeading="magnify"> </eds-button>
      <eds-button appearance="default" circle iconOnly iconLeading="ruler"> </eds-button>
    </div>
  </div>
  <eds-image class="preview-image" [src]="image()"></eds-image>
</div>

@if (sliderVisible()) {
  <div class="slider-overlay">
    <div class="slider-container">
      <div class="slider-header">
        <eds-heading [text]="productName()"></eds-heading>
        <eds-button appearance="primary" iconOnly iconLeading="cancel" (button-click)="closeSlider()"></eds-button>
      </div>

      <div class="slider-content">
        <eds-button
          class="nav-button prev"
          appearance="subtle"
          iconOnly
          iconLeading="arrowLeft"
          (button-click)="prevImage()"
        ></eds-button>

        <eds-image class="slider-image" [src]="currentImageSrc()"></eds-image>

        <eds-button
          class="nav-button next"
          appearance="subtle"
          iconOnly
          iconLeading="arrowRight"
          (button-click)="nextImage()"
        ></eds-button>
      </div>

      <div class="slider-thumbnails">
        @for (image of images(); track image.id; let i = $index) {
          <eds-image
            class="thumbnail"
            [class.active]="i === currentImageIndex()"
            [src]="image.src"
            (click)="openImageSlider(i)"
          >
          </eds-image>
        }
      </div>
    </div>
  </div>
}
