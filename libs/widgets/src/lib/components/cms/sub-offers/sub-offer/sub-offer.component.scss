.base {
  display: flex;
  gap: var(--eds-spacing-400);
  padding: var(--eds-spacing-600);
  background-color: var(--eds-colors-body);
  border-radius: var(--eds-radius-600);
  border: 1px solid var(--eds-border-color-light);
  flex-direction: column;

  @media (min-width: 834px) {
    flex-direction: row;
  }

  eds-image {
    width: 100%;
    height: 100%;
    max-width: calc(var(--eds-size-multiplier) * 18);
    max-height: calc(var(--eds-size-multiplier) * 21);
  }
}

.content {
  display: grid;
  gap: var(--eds-spacing-400);

  .information {
    display: grid;
    gap: var(--eds-spacing-100);
  }

  eds-link {
    --eds-link-decoration: none;
    --eds-link-justify-content: flex-start;
  }
}
