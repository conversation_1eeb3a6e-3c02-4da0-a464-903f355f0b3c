import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';

@Component({
  selector: 'widget-cms-sub-offers',
  templateUrl: './sub-offers.component.html',
  styleUrls: ['./sub-offers.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsSubOffersComponent {
  title = input('');
  link = input('');
  href = input('');
}
