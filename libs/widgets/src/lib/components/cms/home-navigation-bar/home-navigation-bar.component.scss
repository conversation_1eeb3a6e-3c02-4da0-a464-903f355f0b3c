.base {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--eds-spacing-400);
  align-self: stretch;

  @media (min-width: 834px) {
    display: flex;
  }
}

.menu-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: var(--eds-spacing-600);
  align-self: stretch;
}

.menu-box {
  display: flex;
  padding: 0 var(--eds-spacing-400);
  flex-direction: column;
  align-items: center;
  gap: var(--eds-spacing-200);
  flex: 1 0 0;

  eds-icon {
    width: 40px;
    height: 40px;
  }

  .title-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--eds-spacing-200);
    align-self: stretch;
  }
}
