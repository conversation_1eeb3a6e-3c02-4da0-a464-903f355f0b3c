import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { CmsNavigationItem } from '../../../model';

@Component({
  selector: 'widget-home-navigation-bar',
  templateUrl: './home-navigation-bar.component.html',
  styleUrl: './home-navigation-bar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class HomeNavigationBarComponent {
  navigationItems = input<CmsNavigationItem[]>([]);
}
