.base {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: var(--eds-spacing-400);
  background: var(--background-image) no-repeat center center / cover;
  min-height: calc(var(--eds-size-multiplier) * 72);
  max-height: calc(var(--eds-size-multiplier) * 72);
  padding: var(--eds-spacing-600);
  border-radius: var(--eds-radius-600);
  text-decoration: none;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-color: var(--eds-black);
    opacity: 0.2;
    border-radius: var(--eds-radius-600);
  }

  widget-cms-blog-badge {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
  }

  eds-text,
  eds-heading {
    z-index: 2;
  }

  eds-text::part(base),
  eds-heading::part(base) {
    color: var(--eds-colors-text-white);
  }
}
