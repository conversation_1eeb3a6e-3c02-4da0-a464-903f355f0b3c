<div class="base">
  <widget-swiper [config]="config()" [style.pointer-events]="banners().length === 1 ? 'none' : 'auto'">
    @for (banner of banners(); track $index) {
      <div class="banner" [style.backgroundImage]="'url(' + banner.image + ')'">
        <div class="box">
          <div class="information">
            @if (banner?.title) {
              <eds-heading class="title" size="sm" [text]="banner.title"></eds-heading>
            }
            <eds-heading class="superTitle" size="xl" [text]="banner.superTitle"></eds-heading>
            @if (banner?.subtitle) {
              <eds-text class="subtitle" size="lg" [text]="banner?.subtitle"></eds-text>
            }
          </div>

          <div class="buttons">
            @for (button of banner.buttons; track $index) {
              <eds-button [href]="button.link" class="button" appearance="default">
                {{ button.text }}
              </eds-button>
            }
          </div>
        </div>
      </div>
    }
  </widget-swiper>
</div>
