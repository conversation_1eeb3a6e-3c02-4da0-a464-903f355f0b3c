:host {
  --eds-carousel-bottom-spacing: var(--carousel-bottom-spacing, var(--eds-spacing-600));
  --eds-carousel-pagination-height: var(--carousel-pagination-height, var(--eds-sizing-200));
  --eds-carousel-pagination-color: var(--carousel-pagination-color, var(--eds-colors-primary-dark));
}

.base {
  display: flex;
  flex-direction: column;
  position: relative;
}

.banner {
  display: grid;
  justify-content: center;
  grid-template-columns: minmax(0, calc(var(--eds-size-multiplier) * 288));
  gap: var(--eds-spacing-200);
  padding: var(--eds-spacing-900) var(--eds-spacing-400);

  background-color: lightgray;
  background-position: 50%;
  background-size: cover;
  background-repeat: no-repeat;

  .box {
    display: flex;
    width: clamp(0px, 100%, calc(var(--eds-size-multiplier) * 136));
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: var(--eds-spacing-900);
    flex-shrink: 0;

    .information {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: var(--eds-spacing-400);

      * {
        color: var(--eds-colors-text-white);
      }

      .superTitle::part(base) {
        font-size: var(--font-size-heading-xxl);
        line-height: var(--line-height-heading-xxl);
      }

      .title {
        color: var(--eds-colors-secondary-default);
      }
    }

    .buttons {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: var(--eds-spacing-200);
    }
  }
}

:host ::ng-deep .swiper-pagination {
  position: absolute;
  bottom: 0;

  .bullet-active {
    width: 24px;
    --swiper-pagination-color: var(--eds-colors-secondary-default);
  }
}
