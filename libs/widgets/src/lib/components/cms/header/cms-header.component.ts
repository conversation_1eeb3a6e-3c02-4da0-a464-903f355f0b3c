import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  input,
  model,
  output,
  signal,
} from '@angular/core';
import { CMSMenuItem } from '@libs/widgets';
import { MiniShoppingCartComponent } from '../shopping-cart';
import { select } from '@ngxs/store';
import { ConfigState, TranslatePipe } from '@libs/plugins';
import { FeatureFlagEnum, User } from '@libs/types';
import { HeaderNavigationComponent } from '../../header-navigation';

@Component({
  selector: 'widget-cms-header',
  templateUrl: './cms-header.component.html',
  styleUrls: ['./cms-header.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [MiniShoppingCartComponent, HeaderNavigationComponent, TranslatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsHeaderComponent {
  loggedIn = input(false);
  name = input('');
  logoHref = input('');
  logoSrc = input('');
  brandName = input('');
  navigation = input<CMSMenuItem[]>([]);
  quickLinks = input<CMSMenuItem[]>([]);
  userTypeMenu = input(true);
  cartItemsCount = input(0);
  languageSection = select(ConfigState.isFeatureEnabled(FeatureFlagEnum.languageSection));
  activeUserType = signal<User.UserType>(User.UserType.PERSONAL);
  readonly UserType = User.UserType;

  languagePreferences = model<boolean>();

  navigateCart = output<void>();
  userTypeChange = output<User.UserType>();

  openLanguagePreference(): void {
    this.languagePreferences.set(true);
  }

  setUserType(type: User.UserType): void {
    this.activeUserType.set(type);
    this.userTypeChange.emit(type);
  }
}
