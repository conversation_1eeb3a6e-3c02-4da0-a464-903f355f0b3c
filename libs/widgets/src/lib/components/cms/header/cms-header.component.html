<div class="base" #base>
  <div class="language-preference">
    <ng-content select="[languagePreference]"></ng-content>
  </div>
  @if (userTypeMenu()) {
    <div class="user-type-menu">
      <div class="user-type-menu-content">
        <eds-button
          appearance="subtle"
          [class.active]="activeUserType() === UserType.PERSONAL"
          (button-click)="setUserType(UserType.PERSONAL)"
        >
          {{ 'personal' | translate }}
        </eds-button>
        <eds-button
          appearance="subtle"
          [class.active]="activeUserType() === UserType.BUSINESS"
          (button-click)="setUserType(UserType.BUSINESS)"
        >
          {{ 'business' | translate }}
        </eds-button>
      </div>
    </div>
  }
  <div class="content">
    <a class="brand" [href]="logoHref()">
      <eds-image [src]="logoSrc()" [alt]="brandName()"></eds-image>
    </a>

    <div class="actions">
      <widget-header-navigation [items]="navigation()" [quickLinks]="quickLinks()"></widget-header-navigation>
      <ng-content select="[search]"></ng-content>
      <widget-mini-shopping-cart
        [count]="cartItemsCount()"
        (navigateClick)="navigateCart.emit()"
      ></widget-mini-shopping-cart>
      <eds-button
        class="language-item"
        appearance="subtle"
        iconOnly
        iconLeading="internet"
        (button-click)="openLanguagePreference()"
      ></eds-button>
      <ng-content select="[mobileMenu]"></ng-content>
      <ng-content select="[login]"> ></ng-content>
    </div>
  </div>
</div>
