:host {
  --eds-header-padding: var(--header-padding, var(--eds-spacing-400));
  --eds-header-width: var(--header-width, 100%);
  --eds-header-background-color: var(--header-background-color, var(--eds-colors-surface-level-1));
  --eds-header-border-properties: var(
    --header-border-properties,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-header-content-gap: var(--header-content-gap, var(--eds-spacing-600));
  --eds-header-actions-gap: var(--header-actions-gap, var(--eds-spacing-200));
  --eds-header-container-size: var(--header-container-size, calc(var(--eds-size-multiplier) * 288));

  --eds-header-brand-logo-width: var(--header-brand-logo-width, calc(var(--eds-size-multiplier) * 28));
  --eds-header-language-item-display: var(--header-language-item-display, none);
  --eds-header-user-type-menu-background-color: var(
    --header-user-type-menu-background-color,
    var(--eds-colors-primary-default)
  );
  --eds-header-user-type-menu-item-padding: var(
    --header-user-type-menu-item-padding,
    var(--eds-spacing-200) var(--eds-spacing-400)
  );
  --eds-header-user-type-menu-item-active-background-color: var(
    --header-user-type-menu-item-active-background-color,
    var(--eds-colors-primary-dark)
  );
  --eds-header-user-type-menu-item-active-text-color: var(
    --header-user-type-menu-item-active-text-color,
    var(--eds-colors-text-white)
  );
  --eds-header-user-type-menu-item-text-color: var(
    --header-user-type-menu-item-text-color,
    var(--eds-colors-text-white)
  );
  --eds-header-nav-item-gap: var(--header-nav-item-gap, var(--eds-spacing-300));
  --eds-header-nav-item-padding: var(--header-nav-item-padding, var(--eds-spacing-100));

  width: var(--eds-header-width);
}

.base {
  --header-search-overflow: hidden;
  z-index: 2;

  position: relative;
  display: grid;
  place-items: center;
  grid-template-rows: var(--header-language-preference-row) auto auto;
  width: 100%;
  background: var(--eds-header-background-color);
  border-bottom: var(--eds-header-border-properties);

  .language-preference,
  widget-language-preference {
    width: 100%;
    height: 100%;
    overflow: var(--header-language-preference-overflow);
  }

  .user-type-menu {
    display: grid;
    grid-template-columns: minmax(0, var(--eds-header-container-size));
    justify-content: center;
    background-color: var(--eds-header-user-type-menu-background-color);
    width: 100%;

    &-content {
      display: flex;
      align-items: baseline;
      padding: 0 var(--eds-header-padding);

      eds-button {
        --eds-button-padding: var(--eds-header-user-type-menu-item-padding);
        --eds-button-subtle-text-color: var(--eds-header-user-type-menu-item-text-color);
        --eds-button-font-weight: 400;
        --eds-button-border-radius: 0;
        --eds-button-border: 0;
        --eds-button-subtle-hover-background-color: var(--eds-header-user-type-menu-item-active-background-color);
        --eds-button-subtle-active-background-color: var(--eds-header-user-type-menu-item-active-background-color);
      }

      eds-button.active {
        --eds-button-subtle-background-color: var(--eds-header-user-type-menu-item-active-background-color);
        --eds-button-subtle-text-color: var(--eds-header-user-type-menu-item-active-text-color);
      }
    }
  }

  .content {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: var(--eds-header-container-size);
    padding: var(--eds-header-padding);
    gap: var(--eds-header-content-gap);

    .brand {
      flex-shrink: 0;

      eds-image {
        display: block;
        width: var(--eds-header-brand-logo-width);
        height: var(--eds-header-brand-logo-height);
      }
    }

    .actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: var(--eds-header-actions-gap);
      flex: 1;

      widget-header-navigation {
        display: none;
        flex: 1;
      }

      .language-item {
        display: var(--eds-header-language-item-display);
      }
    }
  }
}

@media (min-width: 834px) {
  :host {
    --eds-header-padding: var(--header-padding, var(--eds-spacing-600));
    --eds-header-actions-gap: var(--header-actions-gap, var(--eds-spacing-400));
    --eds-header-profile-item-height: var(--header-profile-item-height, calc(var(--eds-size-multiplier) * 10));
  }

  .base {
    .content {
      .actions {
        position: relative;
        gap: var(--eds-header-actions-gap);

        widget-header-navigation {
          display: flex;
        }

        .hamburger-menu {
          display: none;
        }

        .language-item {
          --eds-header-language-item-display: flex;
        }

        .profile-item {
          eds-text {
            &.profile-item-mobile {
              display: none;
            }

            &.profile-item-desktop {
              display: block;
              text-transform: uppercase;
            }
          }

          eds-icon {
            display: block;
            flex-shrink: 0;
            width: var(--eds-sizing-600);
            height: var(--eds-sizing-600);
          }
        }
      }
    }
  }
}
