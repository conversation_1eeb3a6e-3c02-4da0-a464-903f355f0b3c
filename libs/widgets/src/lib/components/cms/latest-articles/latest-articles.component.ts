import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { CmsBlogCardComponent } from '../blog-card';
import { CmsBlogCard } from '../../../model/cms-blog-card.model';
import { PageHeadingComponent } from '../../page-heading';

@Component({
  selector: 'widget-cms-latest-articles',
  templateUrl: './latest-articles.component.html',
  styleUrls: ['./latest-articles.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CmsBlogCardComponent, PageHeadingComponent],
})
export class CmsLatestArticlesComponent {
  items = input<CmsBlogCard[]>();
  title = input<string>();
  helperText = input<string>();
}
