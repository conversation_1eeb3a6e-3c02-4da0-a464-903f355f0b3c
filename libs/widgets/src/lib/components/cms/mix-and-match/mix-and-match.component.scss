.base {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-600);
  align-items: center;

  .wrapper {
    display: flex;
    flex-direction: column-reverse;
    gap: var(--eds-spacing-600);
    width: 100%;

    .description {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: var(--eds-spacing-400);
      padding: var(--eds-spacing-400);
      background-color: var(--eds-colors-surface-default);
      border-radius: var(--eds-radius-600);
      border: 1px solid var(--eds-border-color-light);

      .description-item {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: var(--eds-spacing-100);
        padding-bottom: var(--eds-spacing-400);
        border-bottom: 1px solid var(--eds-border-color-light);

        &:last-of-type {
          padding-bottom: 0;
          border-bottom: none;
        }

        eds-image {
          width: var(--eds-sizing-600);
          height: var(--eds-sizing-600);
          flex-shrink: 0;
        }
      }

      @media (min-width: 834px) {
        min-width: 320px;
        max-width: 320px;
      }
    }

    .plan {
      width: 100%;
      background-color: var(--eds-colors-surface-default);
      border-radius: var(--eds-radius-600);
      border: 1px solid var(--eds-border-color-light);

      .mixer {
        display: flex;
        flex-direction: column;
        gap: calc(var(--eds-size-multiplier) * 8);
        padding: var(--eds-spacing-600) var(--eds-spacing-400);

        @media (min-width: 834px) {
          padding: calc(var(--eds-size-multiplier) * 8);
        }
      }

      .actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--eds-spacing-600) calc(var(--eds-size-multiplier) * 8);
        border-top: 1px solid var(--eds-border-color-light);
        flex-direction: column;
        gap: var(--eds-spacing-400);

        eds-button {
          flex-shrink: 0;
          width: 100%;

          &::part(base) {
            width: 100%;
          }

          @media (min-width: 834px) {
            width: auto;
          }
        }

        .content {
          display: flex;
          flex-direction: column;
          gap: var(--eds-spacing-100);

          eds-text {
            color: var(--eds-colors-text-light);
          }

          .amount {
            display: flex;
            align-items: center;
            gap: var(--eds-spacing-200);

            eds-icon {
              display: flex;
              align-items: center;
              justify-content: center;
              width: calc(var(--eds-size-multiplier) * 8);
              height: calc(var(--eds-size-multiplier) * 8);
              flex-shrink: 0;
            }

            input {
              width: 100%;
              border: none;
              outline: none;
              pointer-events: none;
              color: var(--eds-colors-text-default);
              font-size: var(--eds-font-size-lg);
              font-weight: var(--eds-font-weight-medium);
              line-height: var(--eds-line-height-lg);
            }
          }
        }

        @media (min-width: 834px) {
          flex-direction: row;
        }
      }
    }
    @media (min-width: 834px) {
      flex-direction: row;
    }
  }
  @media (min-width: 834px) {
    gap: var(--eds-spacing-700);
  }
}
