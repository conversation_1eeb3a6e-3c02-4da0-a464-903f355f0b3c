<div class="base">
  @if (title()) {
    <eds-heading size="sm" [text]="title()"></eds-heading>
  }
  <div class="wrapper">
    @if (description()?.length > 0) {
      <div class="description">
        @for (item of description(); track $index) {
          <div class="description-item">
            <eds-image [src]="item.image"></eds-image>
            <eds-text [text]="item.title" size="lg" weight="medium" align="center"></eds-text>
            <eds-text [text]="item.description" size="sm" align="center"></eds-text>
          </div>
        }
      </div>
    }
    <div class="plan">
      <div class="mixer">
        @for (item of mixer(); track item.label) {
          <eds-range-slider
            [label]="item.label"
            [min]="item.min"
            [max]="item.max"
            [value]="item.value"
            [step]="item.step"
            [customValues]="item.customValues"
            [unit]="item.unit"
            [icon]="item.icon"
            [disabled]="item.disabled || isLoading()"
            [showButtons]="item.showButtons"
            [showValue]="item.showValue"
            [unlimitedOffset]="item.unlimitedOffset"
            (slider-change)="onSliderChange(item.label, $any($event))"
          ></eds-range-slider>
        }
      </div>
      <div class="actions">
        <div class="content">
          <eds-text [text]="'Amount to be paid'" size="lg"></eds-text>
          <div class="amount">
            <eds-icon name="coins"></eds-icon>
            @if (isLoading()) {
              <eds-icon name="loading"></eds-icon>
            } @else {
              <eds-heading [text]="price()" size="sm"></eds-heading>
            }
          </div>
        </div>
        <eds-button [variant]="'primary'" [disabled]="isLoading() || disable()" (button-click)="onJoinNow()"
          >Join Now</eds-button
        >
      </div>
    </div>
  </div>
</div>
