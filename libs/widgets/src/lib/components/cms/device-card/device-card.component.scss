eds-card {
  height: 100%;
  display: block;

  &::part(base) {
    gap: var(--eds-spacing-400);
    height: 100%;
  }
  eds-image {
    display: flex;
    justify-content: center;
    align-items: center;

    &::part(image) {
      max-width: 100%;
      height: 200px;
    }
  }

  .info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--eds-spacing-200);

    .price,
    .product {
      display: grid;
      place-items: center;
      .brand {
        display: flex;
        align-items: center;
        gap: var(--eds-spacing-200);
        .color {
          width: 0.9rem;
          height: 0.9rem;
          border-radius: 50%;
          opacity: 0.5;
          cursor: pointer;
        }
      }
    }
  }

  eds-text[as='del']::part(base) {
    color: var(--eds-colors-text-light);
    text-decoration: line-through;
  }

  eds-heading::part(base) {
    text-align: center;
  }

  .buy-now-button {
    margin-top: auto;
  }
}
