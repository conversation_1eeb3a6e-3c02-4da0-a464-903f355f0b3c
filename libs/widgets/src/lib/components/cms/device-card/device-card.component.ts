import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { Cms } from '../../../model';
@Component({
  selector: 'widget-cms-device-card',
  templateUrl: './device-card.component.html',
  styleUrls: ['./device-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsDeviceCardComponent {
  deviceId = input<string>();
  title = input<string>();
  brand = input<string>();
  model = input<string>();
  price = input<string>();
  oldPrice = input<string>();
  deviceImage = input<string>();
  color = input<{ code: string; label: string }>();
  tags = input<Cms.PlanTag[]>([]);
  couponAvailable = input<boolean>(false);

  onSelect = output<string>();
}
