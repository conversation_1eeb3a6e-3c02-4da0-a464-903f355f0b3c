.base {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-600);
  align-items: center;
  justify-content: center;

  .content {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-200);
    align-items: center;
    justify-content: center;
    text-align: center;

    eds-heading {
      color: var(--eds-colors-text-dark);
    }
  }

  .buttons {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);

    @media (min-width: 834px) {
      flex-direction: row;
    }
  }
}
