.notification-preferences {
  display: flex;
  flex-direction: column;
  border: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default);
  border-radius: var(--eds-radius-300);
  padding: var(--eds-spacing-600) 0;
  gap: var(--eds-spacing-400);
  background-color: var(--eds-colors-surface-default);

  .preference-item {
    display: flex;
    justify-content: space-between;
    gap: var(--eds-spacing-400);
    padding: 0 var(--eds-spacing-400) var(--eds-spacing-400);
    border-bottom: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-light);

    &:last-child {
      padding-bottom: 0;
      border-bottom: none;
    }

    .preference-item-content {
      display: flex;
      flex-direction: column;

      [part='description'] {
        color: var(--eds-colors-text-light);
      }
    }

    .preference-item-toggle {
      display: flex;
      flex-direction: column;
      gap: var(--eds-spacing-100);
      flex-shrink: 0;
    }

    @media (max-width: 834px) {
      flex-direction: column;
    }
  }
}
