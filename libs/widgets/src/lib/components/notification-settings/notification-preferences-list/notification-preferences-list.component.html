<div class="notification-preferences">
  @for (preference of preferencesList(); track $index) {
    <div class="preference-item">
      <div class="preference-item-content">
        <eds-text part="title" size="lg" [text]="preference.name"></eds-text>
        @if (preference.description) {
          <eds-text part="description" size="sm" [text]="preference.description"></eds-text>
        }
      </div>
      <div class="preference-item-toggle">
        @for (item of preference.items; track $index) {
          <eds-toggle [checked]="item.authorizedFlag" (change)="onPreferenceChange.emit(item)">
            <eds-text
              part="label"
              size="md"
              [text]="'notificationChannelTypes.' + item.notificationChannelType | translate"
            ></eds-text>
          </eds-toggle>
        }
      </div>
    </div>
  }
</div>
