import { Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { CapturedPartyPrivacy } from '@libs/types';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-notification-preferences-list',
  templateUrl: './notification-preferences-list.component.html',
  styleUrls: ['./notification-preferences-list.component.scss'],
  imports: [TranslatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class NotificationPreferencesListComponent {
  preferencesList = input<CapturedPartyPrivacy.Content[]>([]);
  onPreferenceChange = output<CapturedPartyPrivacy.Item>();
}
