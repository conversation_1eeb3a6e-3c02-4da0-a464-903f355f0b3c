eds-accordion[isopen] {
  [part='icon'] {
    transform: rotate(180deg);
  }
}

.accordion-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .accordion-header-content {
    display: flex;
    align-items: center;
    gap: var(--eds-spacing-200);
    min-width: 0;

    .item-icon-area {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      width: var(--eds-sizing-500);
      height: var(--eds-sizing-500);

      eds-icon {
        width: var(--eds-sizing-500);
        height: var(--eds-sizing-500);
      }
    }

    .item-content,
    .item-content::part(base) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  [part='icon'] {
    flex-shrink: 0;
    transition: var(--eds-transition-property-base) var(--eds-transition-duration-base)
      var(--eds-transition-timing-function-base);
  }
}

.accordion-content {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-200);

  .actions {
    display: flex;
    gap: var(--eds-spacing-200);
    justify-content: flex-end;
  }
}

.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}
