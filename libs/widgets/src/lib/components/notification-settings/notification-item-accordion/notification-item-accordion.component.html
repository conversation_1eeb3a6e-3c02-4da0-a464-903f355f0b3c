<eds-accordion (accordion-click)="onAccordionChange($event)" [isDisabled]="isDisabled()">
  <div class="accordion-header" slot="header">
    <div class="accordion-header-content">
      @if (isCustomer()) {
        <div class="item-icon-area">
          @if (item().contactInfo.isPrimary) {
            <eds-icon name="checkmarkCircle"></eds-icon>
          }
        </div>
      }
      <eds-text class="item-content" as="p" [text]="displayText()" size="md" weight="medium"></eds-text>
    </div>
    <eds-icon part="icon" name="arrowDown"></eds-icon>
  </div>
  @if (item().isLoading) {
    <div class="spinner-container">
      <eds-icon name="loading"></eds-icon>
    </div>
  } @else {
    <div class="accordion-content">
      <widget-notification-preferences-list
        [preferencesList]="getDisplayData()?.privacyList ?? []"
        (onPreferenceChange)="onPreferenceChange($event)"
      ></widget-notification-preferences-list>
      <div class="actions">
        <eds-button [appearance]="'default'" shouldFitContainer (button-click)="onCancel($event)">
          {{ 'cancel' | translate }}
        </eds-button>
        <eds-button [appearance]="'primary'" shouldFitContainer (button-click)="onSave()" [disabled]="isSaveDisabled()">
          {{ 'save' | translate }}
        </eds-button>
      </div>
    </div>
  }
</eds-accordion>
