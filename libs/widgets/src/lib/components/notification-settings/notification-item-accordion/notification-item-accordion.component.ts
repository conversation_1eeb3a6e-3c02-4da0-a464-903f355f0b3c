import { Component, CUSTOM_ELEMENTS_SCHEMA, computed, input, output, inject } from '@angular/core';
import { Accordion } from '@eds/components';
import { ItemState } from '@libs/magwid';
import { TranslatePipe, TranslateService } from '@libs/plugins';
import { CapturedPartyPrivacy, ContactMediumType, eCommon } from '@libs/types';
import { NotificationPreferencesListComponent } from '../notification-preferences-list/notification-preferences-list.component';
import { PhoneNumberPipe } from '@libs/core';

@Component({
  selector: 'widget-notification-item-accordion',
  templateUrl: './notification-item-accordion.component.html',
  styleUrls: ['./notification-item-accordion.component.scss'],
  imports: [TranslatePipe, NotificationPreferencesListComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [PhoneNumberPipe],
})
export class NotificationItemAccordionComponent {
  phoneNumberPipe = inject(PhoneNumberPipe);
  translateService = inject(TranslateService);
  item = input.required<ItemState>();
  isDisabled = input<boolean>(false);
  isCustomer = computed(() => this.item().contactInfo.owner.dataTypeId === eCommon.EntityDataType.CUST);

  accordionChange = output<{ isOpen: boolean; itemId: number }>();
  preferenceChange = output<{ item: CapturedPartyPrivacy.Item; itemId: number }>();
  save = output<number>();

  displayText = computed(() => {
    const { contactData, contactDataPrefix, contactMediumType, contactMediumTypeId } = this.item().contactInfo;

    const groupCode = contactMediumType?.groupTypeCode;

    if (
      groupCode === ContactMediumType.ContactMediumTypeGroupCode.EMAIL ||
      contactMediumTypeId === ContactMediumType.ContactMediumTypeId.EMAIL
    ) {
      return contactData;
    }

    const isPhone =
      groupCode === ContactMediumType.ContactMediumTypeGroupCode.PHONE ||
      contactMediumTypeId === ContactMediumType.ContactMediumTypeId.GSM;
    const formatConfig = { prefix: contactDataPrefix };
    const formattedContactData = isPhone ? this.phoneNumberPipe.transform(contactData, formatConfig) : contactData;

    let typeName = contactMediumType?.name;
    if (!typeName) {
      typeName = Object.keys(ContactMediumType.ContactMediumTypeId).find(
        (key) =>
          ContactMediumType.ContactMediumTypeId[key as keyof typeof ContactMediumType.ContactMediumTypeId] ===
          contactMediumTypeId,
      );
    }

    if (typeName) {
      const translationKey =
        contactMediumTypeId === ContactMediumType.ContactMediumTypeId.GSM
          ? 'contactMediumTypes.GSM'
          : `contactMediumTypes.${typeName}`;
      return `${formattedContactData} - (${this.translateService.translate(translationKey)})`;
    }

    return formattedContactData;
  });

  isSaveDisabled = computed(() => {
    const currentItem = this.item();
    if (!currentItem.draftPreferences) {
      return true;
    }
    return JSON.stringify(currentItem.draftPreferences) === JSON.stringify(currentItem.preferences);
  });

  getDisplayData() {
    return this.item().draftPreferences ?? this.item().preferences;
  }

  onAccordionChange(event: Event) {
    const accordion = event.currentTarget as Accordion;
    this.accordionChange.emit({ isOpen: accordion.isOpen, itemId: this.item().id });
  }

  onPreferenceChange(changedItem: CapturedPartyPrivacy.Item) {
    this.preferenceChange.emit({ item: changedItem, itemId: this.item().id });
  }

  onSave() {
    this.save.emit(this.item().id);
  }

  onCancel(event: Event) {
    const accordion = (event.target as HTMLElement).closest('eds-accordion') as Accordion;
    if (accordion) {
      accordion.isOpen = false;
    }
    this.accordionChange.emit({ isOpen: false, itemId: this.item().id });
  }
}
