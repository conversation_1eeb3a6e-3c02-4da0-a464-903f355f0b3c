<div class="notification-preferences-marketing">
  <eds-card [title]="'defaultPreferences' | translate" [description]="'defaultPreferencesDescription' | translate">
    <eds-button
      [appearance]="'primary'"
      (button-click)="onDefaultPreferencesClick()"
      [disabled]="!hasPermissionToEdit()"
    >
      {{ 'setDefaultPreferences' | translate }}
    </eds-button>
  </eds-card>

  <eds-card [title]="'yourContacts' | translate" [description]="'yourContactsDescription' | translate">
    @for (group of contactGroups(); track group.title) {
      @if (group.contacts?.length) {
        <div class="contact-group">
          <eds-text as="h3" [text]="group.title | translate" size="lg" weight="medium"></eds-text>
          <widget-accordion-group>
            @for (contact of group.contacts; track trackById($index, contact)) {
              <widget-notification-item-accordion
                [item]="contact"
                [isDisabled]="!hasPermissionToEdit()"
                (accordionChange)="onAccordionChange($event)"
                (preferenceChange)="onPreferenceChange($event)"
                (save)="onSave($event)"
              ></widget-notification-item-accordion>
            }
          </widget-accordion-group>
        </div>
      }
    }
  </eds-card>

  <eds-card [title]="'yourSubscriptions' | translate">
    @if (subscriptions().length) {
      <widget-accordion-group>
        @for (subscription of subscriptions(); track trackById($index, subscription)) {
          <widget-notification-item-accordion
            [item]="subscription"
            [isDisabled]="!hasPermissionToEdit()"
            (accordionChange)="onAccordionChange($event)"
            (preferenceChange)="onPreferenceChange($event)"
            (save)="onSave($event)"
          ></widget-notification-item-accordion>
        }
      </widget-accordion-group>
    } @else {
      <eds-text as="p" [text]="'noSubscriptions' | translate" size="lg" weight="medium"></eds-text>
    }
  </eds-card>
</div>
