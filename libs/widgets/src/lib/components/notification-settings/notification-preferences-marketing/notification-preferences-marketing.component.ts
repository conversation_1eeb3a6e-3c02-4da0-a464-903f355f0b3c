import { Component, CUSTOM_ELEMENTS_SCHEMA, computed, input, output } from '@angular/core';
import { CapturedPartyPrivacy, ContactMediumType } from '@libs/types';
import { ItemState } from '@libs/magwid';
import { TranslatePipe } from '@libs/plugins';
import { AccordionGroupComponent } from '../../accordion-group';
import { NotificationItemAccordionComponent } from '../notification-item-accordion/notification-item-accordion.component';

@Component({
  selector: 'widget-notification-preferences-marketing',
  templateUrl: './notification-preferences-marketing.component.html',
  styleUrls: ['./notification-preferences-marketing.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, AccordionGroupComponent, NotificationItemAccordionComponent],
})
export class NotificationPreferencesMarketingComponent {
  contacts = input.required<ItemState[]>();
  subscriptions = input.required<ItemState[]>();
  hasPermissionToEdit = input.required<boolean>();

  accordionChange = output<{ isOpen: boolean; itemId: number }>();
  preferenceChange = output<{ item: CapturedPartyPrivacy.Item; itemId: number }>();
  save = output<number>();
  defaultPreferencesClick = output<void>();

  contactGroups = computed(() => {
    const contacts = this.contacts();
    const phoneContacts = contacts.filter(
      (c) => c.contactInfo?.contactMediumType?.groupTypeCode === ContactMediumType.ContactMediumTypeGroupCode.PHONE,
    );
    const emailContacts = contacts.filter(
      (c) => c.contactInfo?.contactMediumType?.groupTypeCode === ContactMediumType.ContactMediumTypeGroupCode.EMAIL,
    );

    return [
      { title: 'phoneNumber', contacts: phoneContacts },
      { title: 'email', contacts: emailContacts },
    ];
  });

  onAccordionChange(event: { isOpen: boolean; itemId: number }) {
    this.accordionChange.emit(event);
  }

  onPreferenceChange(event: { item: CapturedPartyPrivacy.Item; itemId: number }) {
    this.preferenceChange.emit(event);
  }

  onSave(itemId: number) {
    this.save.emit(itemId);
  }

  onDefaultPreferencesClick() {
    this.defaultPreferencesClick.emit();
  }

  trackById(index: number, item: { id: number }): number {
    return item.id;
  }
}
