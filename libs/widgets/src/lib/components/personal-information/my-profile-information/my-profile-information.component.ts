import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { DataList, Interaction } from '../../../model';
import { TranslatePipe } from '@libs/plugins';
import { InteractionListComponent } from '@libs/widgets';

@Component({
  selector: 'widget-my-profile-information',
  imports: [TranslatePipe, InteractionListComponent],
  templateUrl: './my-profile-information.component.html',
  styleUrls: ['./my-profile-information.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class MyProfileInformationComponent {
  onEdit = output<void>();
  interactions = input<Interaction[]>([]);
  hasPermissionToEdit = input<boolean>(false);
  dataList = input<DataList>({
    itemsSize: 5,
    trim: true,
    expandedText: '',
    unexpandedText: '',
    items: [],
  });
}
