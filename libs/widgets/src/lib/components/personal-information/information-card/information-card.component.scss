.card-item-wrapper {
  display: flex;
  flex-direction: column;
  border: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default);
  border-radius: var(--eds-radius-300);
  overflow: hidden;

  &:has(.no-content-container) {
    border-style: dashed;
  }

  ::ng-deep widget-information-card-item {
    &:not(:last-of-type) {
      border-bottom: 1px solid #efeff6;
    }
  }

  ::ng-deep .no-content-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: var(--eds-spacing-600);
    gap: var(--eds-spacing-300);

    eds-icon {
      width: var(--eds-sizing-700);
      height: var(--eds-sizing-700);
      color: var(--eds-colors-icon-default);
    }
  }
}
