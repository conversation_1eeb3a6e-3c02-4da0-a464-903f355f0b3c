import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';

@Component({
  selector: 'widget-information-card',
  templateUrl: './information-card.component.html',
  styleUrls: ['./information-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class InformationCardComponent {
  title = input<string>();
  addLabel = input<string>('Add');
  add = output<void>();
  canAdd = input<boolean>(true);

  onAdd() {
    this.add.emit();
  }
}
