import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  input,
  output,
  effect,
  signal,
} from '@angular/core';
import { ItemDeleteActionComponent } from '../../item-delete-action/item-delete-action.component';
import { TranslatePipe } from '@libs/plugins';
import { ModalService } from '@libs/plugins';
import { CustomerProfileService } from '@libs/bss';
import { injectDestroy } from '@libs/core';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'widget-information-card-item',
  imports: [ItemDeleteActionComponent, TranslatePipe],
  templateUrl: './information-card-item.component.html',
  styleUrls: ['./information-card-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class InformationCardItemComponent {
  private destroy$ = injectDestroy();
  private modalService = inject(ModalService);
  private customerProfileService = inject(CustomerProfileService);

  private isModalOpened = signal(false);
  protected shouldCancelDelete = computed(() => this.customerProfileService.isEditMode() || this.isModalOpened());

  isPrimary = input<boolean>(false);
  showEdit = input<boolean>(true);
  showDelete = input<boolean>(true);
  isDeleteConfirmation = signal<boolean>(false);

  edit = output<void>();
  delete = output<void>();

  constructor() {
    this.modalService.modalOpened$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.isModalOpened.set(true);
    });

    this.modalService.modalClosed$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.isModalOpened.set(false);
    });

    effect(() => {
      if (this.shouldCancelDelete()) {
        this.isDeleteConfirmation.set(false);
      }
    });
  }

  onDeleteAction() {
    this.isDeleteConfirmation.set(true);
  }

  onCancelDeleteAction() {
    this.isDeleteConfirmation.set(false);
  }
}
