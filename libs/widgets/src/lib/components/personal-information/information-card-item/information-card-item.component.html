<div class="card-item">
  @if (isDeleteConfirmation()) {
    <div class="item-overlay"></div>
  }
  <div class="item-icon-area">
    @if (isPrimary()) {
      <eds-icon name="checkmarkCircle"></eds-icon>
    }
  </div>
  <div class="item-content">
    <ng-content></ng-content>
  </div>
  @if (showEdit() || showDelete()) {
    <div class="item-actions">
      @if (showDelete()) {
        <widget-item-delete-action
          [deleteConfirmationText]="'yesDelete' | translate"
          (deleteItem)="delete.emit()"
          (onDeleteAction)="onDeleteAction()"
          (onCancelDeleteAction)="onCancelDeleteAction()"
          [triggerCancel]="shouldCancelDelete()"
        ></widget-item-delete-action>
      }
      @if (showEdit() && !isDeleteConfirmation()) {
        <eds-button
          [size]="'compact'"
          [iconOnly]="true"
          [iconLeading]="'pencilEdit'"
          [appearance]="'default'"
          (button-click)="edit.emit()"
        ></eds-button>
      }
    </div>
  }
</div>
