.card-item {
  display: flex;
  position: relative;
  padding: var(--eds-spacing-400);
  gap: var(--eds-spacing-200);
  align-items: start;

  .item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(2px);
    z-index: 1;
  }

  .item-icon-area {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: var(--eds-sizing-500);
    height: var(--eds-sizing-500);
    margin-top: var(--eds-spacing-100);

    eds-icon {
      width: var(--eds-sizing-500);
      height: var(--eds-sizing-500);
    }
  }

  .item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-right: 4rem;
    min-width: 0;

    ::ng-deep .email-address {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .item-actions {
    display: flex;
    gap: var(--eds-spacing-100);
    position: absolute;
    right: var(--eds-spacing-400);
    top: var(--eds-spacing-400);
    z-index: 1;
  }

  eds-icon::part(base) {
    display: flex;
    width: 100%;
    height: 100%;
  }
}
