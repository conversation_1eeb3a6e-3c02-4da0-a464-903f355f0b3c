@if (_counter() <= 0) {
  <eds-button shouldFitContainer [appearance]="'primary'" (button-click)="increment()">{{ label() }}</eds-button>
} @else {
  <div class="counter">
    <eds-button shouldFitContainer [appearance]="'subtle'" (button-click)="decrement()">
      <eds-icon name="minus"></eds-icon>
    </eds-button>
    <eds-text size="md" [text]="_counter()"></eds-text>
    <eds-button shouldFitContainer [appearance]="'subtle'" (button-click)="increment()">
      <eds-icon name="plus"></eds-icon>
    </eds-button>
  </div>
}
