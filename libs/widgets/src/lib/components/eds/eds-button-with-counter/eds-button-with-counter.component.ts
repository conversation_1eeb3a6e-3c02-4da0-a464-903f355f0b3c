import { Component, CUSTOM_ELEMENTS_SCHEMA, effect, input, output, signal } from '@angular/core';

@Component({
  selector: 'widget-eds-button-with-counter',
  templateUrl: './eds-button-with-counter.component.html',
  styleUrls: ['./eds-button-with-counter.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class EdsButtonWithCounterComponent {
  label = input<string>();
  counter = input<number>();
  controlled = input(false);

  onValueChanged = output<number>();
  onAction = output<'increment' | 'decrement'>();

  _counter = signal<number>(0);

  constructor() {
    effect(() => {
      this._counter.set(this.counter());
    });
  }

  increment() {
    if (!this.controlled()) {
      this._counter.update((counter) => counter + 1);
      this.onValueChanged.emit(this._counter());
    } else {
      this.onAction.emit('increment');
    }
  }

  decrement() {
    if (!this.controlled()) {
      this._counter.update((counter) => counter - 1);
      this.onValueChanged.emit(this._counter());
    } else {
      this.onAction.emit('decrement');
    }
  }
}

// CONTROLLED INPUT ADD FOR OPTIMISTIC UI CONTROL
