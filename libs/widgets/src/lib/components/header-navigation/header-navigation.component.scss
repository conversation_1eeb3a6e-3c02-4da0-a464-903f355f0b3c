.base {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  position: relative;

  .items {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-100);
    flex: 1;

    eds-button {
      display: none;
    }

    eds-accordion {
      --eds-accordion-expanded-background-color: var(--eds-colors-surface-default);
      --eds-accordion-border-properties: var(--eds-stroke-025) solid var(--eds-border-color-light);

      &::part(content) {
        display: grid;
        gap: var(--eds-spacing-400);
      }
    }

    .accordion-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: var(--eds-spacing-100);
      flex: 1;

      eds-icon {
        color: var(--eds-colors-icon-light);
      }
    }

    .menu-topic {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: var(--eds-spacing-100);

      .menu-topic-header {
        display: flex;
        align-items: center;
        gap: var(--eds-spacing-100);

        eds-icon {
          width: var(--eds-sizing-400);
          height: var(--eds-sizing-400);
        }
      }

      .topic-links {
        display: flex;
        flex-direction: column;
        gap: var(--eds-spacing-200);

        eds-link {
          --eds-link-justify-content: flex-start;
        }
        eds-text {
          cursor: pointer;
        }
      }
    }
    eds-button::part(wrapper) {
      width: 100%;
    }

    @media (min-width: 834px) {
      align-items: center;
      flex-direction: row;

      eds-button {
        display: block;

        &::part(base) {
          --eds-button-padding: var(--eds-spacing-100);
          --eds-button-justify-content: center;
          --eds-button-border: 0;
          --eds-button-subtle-background-color: transparent;
        }
      }

      eds-accordion {
        display: none;
      }
    }
  }
}

.mega-menu {
  position: fixed;
  top: calc(var(--eds-size-multiplier) * 22);
  left: 0;
  width: 100%;
  background-color: white;
  display: grid;
  justify-content: center;
  grid-template-columns: minmax(0, var(--eds-header-container-size));
  padding: var(--eds-spacing-600);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-1rem);
  transition: all 0.3s ease;
  box-shadow: var(--eds-shadow-sm);
  z-index: 10;

  .menus-wrapper {
    display: none;
    justify-content: space-between;
    gap: var(--eds-spacing-900);
    padding: 0 var(--eds-spacing-600);

    &.active {
      display: flex;
    }
  }

  .menus {
    display: flex;
    gap: var(--eds-spacing-900);
    padding: var(--eds-spacing-400) 0;

    .menu {
      display: flex;
      flex-direction: column;
      gap: var(--eds-spacing-900);

      .menu-topic {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: var(--eds-spacing-600);

        .menu-topic-header {
          display: flex;
          align-items: center;
          gap: var(--eds-spacing-100);

          eds-icon {
            width: var(--eds-sizing-400);
            height: var(--eds-sizing-400);
          }
        }

        .topic-links {
          display: flex;
          flex-direction: column;
          gap: var(--eds-spacing-400);

          eds-link {
            --eds-link-justify-content: flex-start;
          }
          eds-text {
            cursor: pointer;
          }
        }
      }
    }

    eds-link {
      --eds-link-text-color: var(--eds-colors-text-default);
      --eds-link-decoration: none;
      --eds-link-hover-text-color: var(--eds-colors-text-default);
      --eds-link-hover-decoration: none;
    }
  }

  .promotion {
    position: relative;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    gap: var(--eds-spacing-400);
    background: var(--background-image) 100% 100% / cover no-repeat;
    overflow: hidden;
    padding: var(--eds-spacing-600);
    max-width: calc(var(--eds-size-multiplier) * 90);
    width: 100%;
    border-radius: var(--eds-radius-200);
    transition: all 0.3s ease;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.2) 100%), transparent;
    }

    eds-heading,
    eds-text {
      color: var(--eds-colors-text-white);
    }

    eds-button {
      --eds-button-default-text-color: var(--eds-colors-text-darker);
    }
  }
}

.mega-menu.open {
  @media (min-width: 834px) {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}
