import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  input,
  signal,
  OnDestroy,
  viewChild,
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { CMSMenuItem } from '@libs/widgets';

@Component({
  selector: 'widget-header-navigation',
  templateUrl: './header-navigation.component.html',
  styleUrls: ['./header-navigation.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [RouterLink],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HeaderNavigationComponent implements OnDestroy {
  items = input<CMSMenuItem[]>([]);
  quickLinks = input<CMSMenuItem[]>([]);
  isMegaMenuOpen = signal(false);
  activeAccordionIndex = signal<number>(-1);
  activeMenuIndex = signal<number>(-1);
  private lastScrollPosition = 0;

  megaMenu = viewChild.required<ElementRef<HTMLElement>>('megaMenu');
  navigationItems = viewChild.required<ElementRef<HTMLElement>>('navigationItems');

  toggleAccordion(index: number): void {
    if (this.activeAccordionIndex() === index) {
      this.activeAccordionIndex.set(-1);
    } else {
      this.activeAccordionIndex.set(index);
    }
  }

  isAccordionOpen(index: number): boolean {
    return this.activeAccordionIndex() === index;
  }

  openMegaMenu(index: number = 0): void {
    if (this.isMegaMenuOpen() && this.activeMenuIndex() === index) {
      this.closeMegaMenu();
      return;
    }

    this.activeMenuIndex.set(index);
    this.isMegaMenuOpen.set(true);
    this.lastScrollPosition = window.scrollY;
    document.addEventListener('click', this.handleDocumentEvent);
    document.addEventListener('scroll', this.handleDocumentEvent, true);
  }

  closeMegaMenu(): void {
    this.isMegaMenuOpen.set(false);
    document.removeEventListener('click', this.handleDocumentEvent);
    document.removeEventListener('scroll', this.handleDocumentEvent, true);
  }

  isActiveMenuIndex(index: number): boolean {
    return this.activeMenuIndex() === index;
  }

  private handleDocumentEvent = (event: Event): void => {
    const target = event.target as Node;
    const menuEl = this.megaMenu().nativeElement;
    const navItemsEl = this.navigationItems().nativeElement;

    if (event.type === 'scroll') {
      if (menuEl && menuEl.contains(target)) {
        return;
      }
      this.closeMegaMenu();
    } else if (event.type === 'click') {
      if (menuEl && navItemsEl && !menuEl.contains(target) && !navItemsEl.contains(target)) {
        this.closeMegaMenu();
      }
    }
  };

  ngOnDestroy(): void {
    document.removeEventListener('click', this.handleDocumentEvent);
    document.removeEventListener('scroll', this.handleDocumentEvent, true);
  }
}
