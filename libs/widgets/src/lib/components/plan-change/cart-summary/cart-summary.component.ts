import { Component, CUSTOM_ELEMENTS_SCHEMA, inject, input, output } from '@angular/core';
import { TranslatePipe, TranslateService } from '@libs/plugins';
import { SelectOption } from '../../../model';

@Component({
  selector: 'widget-plan-change-cart-summary',
  templateUrl: './cart-summary.component.html',
  styleUrls: ['./cart-summary.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe],
})
export class PlanChangeCartSummaryComponent {
  private translate = inject(TranslateService);

  showEffectiveDateOptions = input<boolean>(true);
  showLegalInformation = input<boolean>(true);
  currentPlanTitle = input<string>('currentPlan');
  newPlanTitle = input<string>('newPlan');
  penaltyAmount = input<number>(0);
  showPenaltyAlert = input<boolean>(false);

  changeEffectiveDateOptions = input<SelectOption[]>([]);
  date = input<string>();
  deleteItem = output<void>();
  dateChanged = output<string>();

  showDateSelect = false;

  changeDateClick() {
    this.showDateSelect = true;
  }

  changeEffectiveDateClick(event: Event) {
    const customEvent = event as CustomEvent;
    const target = event.target as HTMLSelectElement;
    const selectedValue = customEvent?.detail?.value || target?.value;

    const selectedOption = this.changeEffectiveDateOptions().find((option) => option.value === selectedValue);

    if (selectedOption) {
      this.dateChanged.emit(selectedOption.label);
      this.showDateSelect = false;
    }
  }

  getPenaltyAlert() {
    if (this.penaltyAmount()) {
      return this.translate.translate('planChangePenaltyAlert', { amount: this.penaltyAmount() });
    }
    return this.translate.translate('planChangeNoPenaltyAlert');
  }
}
