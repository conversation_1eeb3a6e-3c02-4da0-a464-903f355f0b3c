import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  ElementRef,
  input,
  output,
  signal,
  viewChild,
} from '@angular/core';
import { ActionListItem } from '../../model';

@Component({
  selector: 'widget-profile-menu',
  templateUrl: './profile-menu.component.html',
  styleUrl: './profile-menu.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ProfileMenuComponent {
  loggedIn = input(false);
  name = input('');
  loginClick = output<void>();
  profileItems = input<ActionListItem[]>([]);
  profileItemDropdown = signal(false);

  base = viewChild.required<ElementRef<HTMLElement>>('base');

  constructor() {
    effect((onCleanup) => {
      if (this.profileItemDropdown()) {
        const handler = (event: MouseEvent) => {
          if (!this.base().nativeElement.contains(event.target as Node)) {
            this.profileItemDropdown.set(false);
          }
        };
        document.addEventListener('click', handler);

        onCleanup(() => {
          document.removeEventListener('click', handler);
        });
      }
    });
  }

  openProfileItemDropdown() {
    this.profileItemDropdown.update((prev) => !prev);
  }
}
