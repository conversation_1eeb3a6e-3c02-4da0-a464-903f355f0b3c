:host {
  --eds-header-profile-item-height: var(--header-profile-item-height, var(--eds-sizing-800));
  --eds-header-profile-item-gap: var(--header-profile-item-gap, var(--eds-spacing-200));
  --eds-header-profile-item-padding: var(--header-profile-item-padding, var(--eds-spacing-200) var(--eds-spacing-400));
  --eds-header-profile-item-background-color: var(
    --header-profile-item-background-color,
    var(--eds-colors-primary-default)
  );
  --eds-header-profile-item-text-color: var(--header-profile-item-text-color, var(--eds-colors-text-white));
  --eds-header-profile-item-border-radius: var(--header-profile-item-border-radius, var(--eds-sizing-200));
  --eds-header-profile-item-border: var(
    --header-profile-item-border,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
}

.profile-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--eds-header-profile-item-gap);
  padding: var(--eds-header-profile-item-padding);
  background: var(--eds-header-profile-item-background-color);
  color: var(--eds-header-profile-item-text-color);
  border-radius: var(--eds-header-profile-item-border-radius);
  height: var(--eds-header-profile-item-height);
  border: var(--eds-header-profile-item-border);
  cursor: pointer;

  eds-icon {
    display: none;
    color: var(--eds-header-profile-item-text-color);
    width: var(--eds-sizing-600);
    height: var(--eds-sizing-600);
  }

  eds-text {
    &::part(base) {
      color: var(--eds-header-profile-item-text-color);
    }

    &.mobile {
      text-transform: uppercase;
    }

    &.desktop {
      display: none;
    }
  }
}

.profile-item-dropdown {
  position: absolute;
  top: calc(100% + var(--eds-spacing-200));
  right: 0;
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-200);
  padding: var(--eds-spacing-200) 0;
  border-radius: var(--eds-radius-300);
  background: var(--eds-colors-surface-default);
  border: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default);
  box-shadow: var(--eds-shadow-sm);

  eds-button::part(base) {
    width: 100%;
    white-space: nowrap;
    justify-content: flex-start;
    border-radius: 0;
  }
}
@media (min-width: 834px) {
  :host {
    --eds-header-profile-item-height: var(--header-profile-item-height, calc(var(--eds-size-multiplier) * 10));
  }

  .profile-item {
    eds-text {
      &.mobile {
        display: none;
      }

      &.desktop {
        display: block;
        text-transform: uppercase;
      }
    }

    eds-icon {
      display: block;
      flex-shrink: 0;
    }
  }
}
