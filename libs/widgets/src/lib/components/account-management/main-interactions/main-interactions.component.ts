import { Component, input } from '@angular/core';
import { ActionCardComponent, ActionListItem } from '@libs/widgets';

@Component({
  selector: 'widget-main-interactions',
  templateUrl: './main-interactions.component.html',
  styleUrls: ['./main-interactions.component.scss'],
  imports: [ActionCardComponent],
})
export class MainInteractionsComponent {
  ordersInteraction = input<ActionListItem>({
    text: 'Orders',
  });

  ticketsInteraction = input<ActionListItem>();
}
