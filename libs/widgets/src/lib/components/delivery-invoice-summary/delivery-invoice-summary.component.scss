.base {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--eds-spacing-400);

  eds-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    color: var(--eds-colors-text-light);
  }

  .label {
    width: calc(var(--eds-size-multiplier) * 40);
    &::part(base) {
      white-space: nowrap;
    }
  }

  .value {
    &::part(base) {
      overflow-wrap: break-word;
    }
  }
}

.delivery-address {
  display: grid;
  grid-template-columns: var(--eds-sizing-500) 1fr;
  gap: var(--eds-spacing-200);
  width: 100%;

  .value {
    grid-column: span 2;
  }

  @media (min-width: 834px) {
    grid-template-columns: repeat(2, minmax(0, min-content)) minmax(0, auto);
    gap: var(--eds-spacing-300);

    .value {
      grid-column: span 1;
    }
  }
}

.delivery-options {
  display: grid;
  grid-template-columns: var(--eds-sizing-500) 1fr;
  align-items: center;
  gap: var(--eds-spacing-200);
  width: 100%;
  padding-top: var(--eds-spacing-400);
  padding-bottom: var(--eds-spacing-400);
  border-top: 1px solid var(--eds-border-color-default);
  border-bottom: 1px solid var(--eds-border-color-default);

  .value {
    grid-column: span 2;
  }

  .delivery-option-details {
    display: grid;
    gap: var(--eds-spacing-200);
    grid-column: span 2;
  }

  .delivery-option-detail {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    gap: var(--eds-spacing-200);
  }

  .delivery-option-detail-key {
    --text-color: var(--eds-colors-text-light);
  }

  @media (min-width: 834px) {
    grid-template-columns: repeat(2, minmax(0, min-content)) minmax(0, auto);
    gap: var(--eds-spacing-300);
    .value {
      grid-column: span 1;
    }

    .delivery-option-details {
      grid-column: 3;
    }

    .delivery-option-detail {
      flex-direction: row;
    }
  }
}
