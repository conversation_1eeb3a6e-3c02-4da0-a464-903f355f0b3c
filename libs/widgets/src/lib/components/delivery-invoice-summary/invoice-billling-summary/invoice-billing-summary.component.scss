.invoice-account {
  display: grid;
  grid-template-columns: var(--eds-sizing-500) 1fr;
  align-items: center;
  gap: var(--eds-spacing-200);
  width: 100%;

  eds-icon {
    width: 20px;
    height: 20px;
    color: var(--eds-colors-text-light);
  }

  .label {
    width: calc(var(--eds-size-multiplier) * 40);
    &::part(base) {
      white-space: nowrap;
    }
  }

  .value {
    grid-column: span 2;
    &::part(base) {
      overflow-wrap: break-word;
    }
  }

  .invoice-account-details {
    display: grid;
    gap: var(--eds-spacing-200);
    grid-column: span 2;
  }

  .invoice-account-detail {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    gap: var(--eds-spacing-200);
  }

  .invoice-account-detail-key {
    --text-color: var(--eds-colors-text-light);
  }

  @media (min-width: 834px) {
    grid-template-columns: repeat(2, minmax(0, min-content)) minmax(0, auto);
    gap: var(--eds-spacing-300);

    .value {
      grid-column: span 1;
    }

    .invoice-account-details {
      grid-column: 3;
    }

    .invoice-account-detail {
      flex-direction: row;
    }
  }
}
