import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  forwardRef,
  input,
  OnDestroy,
} from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import { createPhoneNumberMask, FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { SelectOption, Phonenumber } from '@libs/widgets';
import { Subscription } from 'rxjs';

const PHONE_NUMBER_MIN_LENGTH = 10;

interface PhoneNumberFormControls {
  country: FormControl<string>;
  phoneNumber: FormControl<string>;
}

@Component({
  selector: 'widget-phonenumber',
  templateUrl: './phone-number.component.html',
  styleUrls: ['./phone-number.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, FormFieldComponent, TranslatePipe],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PhoneNumberComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PhoneNumberComponent),
      multi: true,
    },
  ],
})
export class PhoneNumberComponent implements ControlValueAccessor, OnDestroy, Validator {
  countryOptions = input<SelectOption[]>([]);
  required = input<boolean>(false);

  readonly phoneNumberMask = createPhoneNumberMask();

  form = new FormGroup<PhoneNumberFormControls>({
    country: new FormControl('', { nonNullable: true }),
    phoneNumber: new FormControl('', {
      nonNullable: true,
      validators: [Validators.minLength(PHONE_NUMBER_MIN_LENGTH)],
    }),
  });

  private readonly valueSub: Subscription;
  private isValidationInProgress = false;

  onChange: (value: Phonenumber | null) => void = () => {};
  onTouched: () => void = () => {};

  constructor() {
    this.valueSub = this.initializeValueSubscription();
    this.setupValidationEffects();
  }

  // ControlValueAccessor implementation
  writeValue(obj: Phonenumber | null): void {
    if (obj) {
      this.form.patchValue(obj, { emitEvent: false });
    } else {
      this.form.reset(undefined, { emitEvent: false });
    }
  }

  registerOnChange(fn: (value: Phonenumber | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = () => {
      fn();
      this.markAllControlsAsTouched();
    };
  }

  setDisabledState(isDisabled: boolean): void {
    if (isDisabled) {
      this.form.disable();
    } else {
      this.form.enable();
    }
  }

  // Validator implementation
  validate(control: AbstractControl): ValidationErrors | null {
    this.handleParentControlTouchedState(control);
    return this.getValidationErrors();
  }

  ngOnDestroy(): void {
    this.valueSub?.unsubscribe();
  }

  // Private methods
  private initializeValueSubscription(): Subscription {
    return this.form.valueChanges.subscribe((value) => {
      const { country, phoneNumber } = value;
      if (this.isEmptyValue(country, phoneNumber)) {
        this.onChange(null);
      } else {
        this.onChange(value as Phonenumber);
      }
    });
  }

  private setupValidationEffects(): void {
    effect(() => {
      this.updateValidators();
    });
  }

  private updateValidators(): void {
    const countryValidators = this.required() ? [Validators.required] : [];
    const phoneNumberValidators = this.required()
      ? [Validators.required, Validators.minLength(PHONE_NUMBER_MIN_LENGTH)]
      : [Validators.minLength(PHONE_NUMBER_MIN_LENGTH)];

    this.form.get('country')?.setValidators(countryValidators);
    this.form.get('country')?.updateValueAndValidity({ emitEvent: false });

    this.form.get('phoneNumber')?.setValidators(phoneNumberValidators);
    this.form.get('phoneNumber')?.updateValueAndValidity({ emitEvent: false });
  }

  private handleParentControlTouchedState(control: AbstractControl): void {
    if (control?.touched && !this.isValidationInProgress) {
      this.isValidationInProgress = true;
      this.markAllControlsAsTouched();
      this.scheduleValidationUpdate();
    }
  }

  private markAllControlsAsTouched(): void {
    this.form.markAsTouched();
    this.form.controls.country.markAsTouched();
    this.form.controls.phoneNumber.markAsTouched();
  }

  private scheduleValidationUpdate(): void {
    setTimeout(() => {
      this.form.controls.country.updateValueAndValidity();
      this.form.controls.phoneNumber.updateValueAndValidity();
      this.isValidationInProgress = false;
    }, 0);
  }

  private getValidationErrors(): ValidationErrors | null {
    if (this.form.valid) {
      return null;
    }

    const errors: ValidationErrors = {};

    if (this.form.controls.country.errors) {
      errors['country'] = this.form.controls.country.errors;
    }

    if (this.form.controls.phoneNumber.errors) {
      errors['phoneNumber'] = this.form.controls.phoneNumber.errors;
    }

    return errors;
  }

  private isEmptyValue(country: string | null, phoneNumber: string | null): boolean {
    return (!country || country === '') && (!phoneNumber || phoneNumber === '');
  }
}
