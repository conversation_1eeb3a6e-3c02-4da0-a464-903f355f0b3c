.container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
}

.label {
  display: flex;
  padding-bottom: var(--eds-spacing-100);
  align-items: flex-start;
  align-self: stretch;
}

.required::after {
  content: '*';
  color: var(--eds-colors-danger-default);
}

.input {
  display: flex;
  align-items: flex-start;
  gap: var(--eds-spacing-200);
  align-self: stretch;
}

.country {
  flex: 1;
}

.phonenumber {
  flex: 2;
}
