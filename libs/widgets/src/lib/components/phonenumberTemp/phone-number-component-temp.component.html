<form [formGroup]="form">
  <div class="container">
    <div class="label">
      <eds-text size="sm" weight="medium" [text]="'phoneNumber' | translate" [class.required]="required()"></eds-text>
    </div>
    <div class="input">
      <widget-form-field
        class="country"
        formControlName="country"
        type="select"
        [options]="{ options: countryOptions() }"
        (option-selected)="emitForm()"
      >
      </widget-form-field>
      <widget-form-field
        class="phonenumber"
        formControlName="phoneNumber"
        [placeholder]="'phoneNumberPlaceholder' | translate"
        [options]="{ type: 'tel' }"
        [mask]="phoneNumberMask"
        (input)="emitForm()"
      >
      </widget-form-field>
    </div>
  </div>
  <widget-form-field-error-message [errors]="errors()"></widget-form-field-error-message>
</form>
