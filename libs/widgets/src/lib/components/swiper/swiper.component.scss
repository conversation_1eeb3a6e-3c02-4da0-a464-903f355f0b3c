:host {
  --swiper-pagination-color: var(--eds-carousel-pagination-color);
}

:host ::ng-deep .swiper-container {
  --n: 1;
  --g: var(--eds-invoice-card-gap, 8px);
  --gerr: calc(var(--i) * var(--g, 0) / var(--n, 1));
  --spw: 1;
  width: 100%; // fallback

  .swiper-wrapper {
    display: flex;
    width: calc((var(--n) * 100% + var(--g) * (var(--n) - 1)) / var(--spw));
    align-items: center;
    overflow-y: hidden;
    transform: translate(calc(var(--tx, 0px) + var(--i, 0) / var(--n) * -100% - var(--gerr)));
    gap: var(--g);
    transition: transform calc(var(--f, 1) * 0.3s) ease-out;

    > * {
      width: 100%; // fallback
      width: calc(100% / var(--spw));
      user-select: none;
      pointer-events: none;
    }

    eds-link,
    eds-button,
    a,
    button {
      pointer-events: auto;
    }
  }
}

.swiper-pagination {
  line-height: 1em;
  text-align: center;
  transition: 0.3s opacity;
  transform: translate3d(0, 0, 0);
  margin-top: var(--eds-carousel-bottom-spacing, 16px);
  display: flex;
  justify-content: center;
  gap: var(--swiper-pagination-bullet-horizontal-gap, 4px);
}

.swiper-pagination-bullets {
  bottom: var(--swiper-pagination-bottom, 8px);
  top: var(--swiper-pagination-top, auto);
  left: 0;
  width: 100%;
}

.swiper-pagination-bullet {
  width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px));
  height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px));
  display: inline-block;
  border-radius: var(--swiper-pagination-bullet-border-radius, var(--eds-size-multiplier));
  background: var(--swiper-pagination-bullet-inactive-color, var(--eds-colors-primary-default));
  opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);
  transition: all 0.2s ease;
}

.bullet-active {
  width: var(--swiper-pagination-active-bullet-width, var(--swiper-pagination-bullet-size, 32px));
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: var(--swiper-pagination-color, var(--eds-colors-primary-default));
}
