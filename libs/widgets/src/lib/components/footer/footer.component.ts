import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  input,
  AfterViewInit,
  HostListener,
  viewChildren,
} from '@angular/core';
import { CMSMenuItem } from '../../model';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'widget-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class FooterComponent implements AfterViewInit {
  brandName = input('');
  information = input('');
  links = input<CMSMenuItem[]>([]);
  items = input<CMSMenuItem[]>([]);

  detailsElements = viewChildren<ElementRef>('detailsElement');

  ngAfterViewInit() {
    this.updateDetailsOpenState();
  }

  //! todo remove
  @HostListener('window:resize')
  updateDetailsOpenState() {
    const isDesktop = window.innerWidth >= 1200;

    this.detailsElements().forEach((details) => {
      details.nativeElement.open = isDesktop;
    });
  }
}
