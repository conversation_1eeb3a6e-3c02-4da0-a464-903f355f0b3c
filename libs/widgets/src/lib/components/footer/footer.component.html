<div class="base">
  <div class="content-wrapper">
    <div class="content">
      @for (item of items(); track $index) {
        <div class="item">
          <eds-icon [name]="item.icon"></eds-icon>
          <eds-text as="p" size="md" [text]="item.label"></eds-text>
        </div>
      }
    </div>
  </div>
  <div class="links-wrapper">
    <div class="links">
      @for (item of links(); track $index) {
        <details class="item" [attr.name]="item.label" #detailsElement>
          <summary>
            <eds-heading as="h4" size="sm" [text]="item.label"></eds-heading>
            <eds-icon name="arrowDown"></eds-icon>
          </summary>
          <div class="links-list">
            @for (link of item.items; track $index) {
              @if (link.isExternal) {
                <eds-link [href]="link.href" target="_blank">{{ link.label }}</eds-link>
              } @else {
                <eds-text [routerLink]="link.href" [text]="link.label"></eds-text>
              }
            }
          </div>
        </details>
      }
    </div>
    <div class="information-area">{{ information() }}</div>
  </div>
</div>
