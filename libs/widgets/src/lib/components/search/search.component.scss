:host {
  --eds-search-gap: var(--search-gap, var(--eds-spacing-400));
}

:host ::ng-deep {
  --form-field-gap: 0;
}

.wrapper {
  --header-search-width: 0;

  display: block;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: var(--header-search-width);
  z-index: 1;
  transition: width 0.4s ease-in-out;
  overflow: var(--header-search-overflow);

  .base {
    padding: var(--eds-header-padding);
    display: flex;
    justify-content: center;
    gap: var(--eds-search-gap);
    background-color: var(--eds-header-background-color);
  }

  widget-form-field {
    flex: 1;
  }
}
