import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  model,
  output,
  viewChild,
} from '@angular/core';
import { FormFieldComponent } from '@libs/plugins';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'widget-search',
  templateUrl: './search.component.html',
  styleUrls: ['./search.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormFieldComponent, FormsModule],
})
export class SearchComponent {
  searchClose = output<void>();
  sendSearch = output<string>();
  searchText = model<string>();
  base = viewChild.required<ElementRef<HTMLElement>>('base');

  onClose(): void {
    this.closeSearchPanel();
    this.searchClose.emit();
  }

  openSearch(): void {
    this.base().nativeElement.style.setProperty('--header-search-width', '100%');

    setTimeout(() => {
      this.base().nativeElement.style.setProperty('--header-search-overflow', 'visible');
    }, 400);
  }

  closeSearchPanel(): void {
    this.base().nativeElement.style.setProperty('--header-search-width', '0');
    this.base().nativeElement.style.setProperty('--header-search-overflow', 'hidden');
  }

  onSearch() {
    this.sendSearch.emit(this.searchText());
  }
}
