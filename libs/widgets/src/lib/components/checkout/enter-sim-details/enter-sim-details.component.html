<widget-titled-section
  [sectionTitle]="'enterSimDetails' | translate"
  [sectionDescription]="'pleaseEnterIccid' | translate"
>
  <div class="form-field" [formGroup]="form()">
    <div>
      <widget-form-field
        [placeholder]="'iccid' | translate"
        formControlName="iccid"
        [mask]="iccidMask"
        [options]="{ iconTrailing: validationStatus() === 'success' ? 'checkmarkCircle' : '' }"
        [errorBounce]="500"
      >
      </widget-form-field>
    </div>
    <!--        [errors]="form.controls.iccid.errors?.['invalidPhoneNumber'] ? ('invalidPhoneNumber' | translate) : ''"-->
    <small id="iccid-hint" class="form-hint">{{ 'iccidHint' | translate }}</small>
  </div>
</widget-titled-section>
