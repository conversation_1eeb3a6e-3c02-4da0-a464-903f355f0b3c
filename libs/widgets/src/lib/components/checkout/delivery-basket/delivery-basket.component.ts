import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, signal } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-delivery-basket',
  templateUrl: './delivery-basket.component.html',
  styleUrls: ['./delivery-basket.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe],
})
export class DeliveryBasketComponent {
  titleText = input<string>('');
  basketItems = input<string[]>([]);
  isOpen = signal(true);

  toggleOpen() {
    this.isOpen.update((value) => !value);
  }
}
