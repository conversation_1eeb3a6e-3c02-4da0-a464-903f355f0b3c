<div class="base">
  <div class="title" (click)="toggleOpen()">
    <div class="title-wrapper">
      <eds-heading as="h6" size="md" [text]="titleText() | translate"></eds-heading>
      <eds-badge [label]="basketItems().length" type="circle" size="medium" appearance="secondary"></eds-badge>
    </div>
    <eds-icon class="expandable-icon" [name]="isOpen() ? 'arrowUp' : 'arrowDown'"></eds-icon>
  </div>
  @if (isOpen()) {
    <div class="basket-items">
      @for (item of basketItems(); track item) {
        <div class="basket-item">
          <span class="item-name">{{ item }}</span>
        </div>
      }
    </div>
  }
</div>
