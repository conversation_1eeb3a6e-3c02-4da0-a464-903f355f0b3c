.base {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--eds-border-color-default);
  border-radius: var(--eds-radius-300);
  background-color: var(--eds-colors-surface-default);

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--eds-spacing-200);
    background-color: var(--eds-colors-surface-default);
    border-radius: var(--eds-radius-300);

    @media (min-width: 834px) {
      padding: var(--eds-spacing-400);
    }

    .title-wrapper {
      display: flex;
      align-items: center;
      gap: var(--eds-spacing-300);

      .expandable-icon {
        color: var(--eds-colors-secondary-default);
      }
    }

    &:hover {
      cursor: pointer;
    }
  }

  .basket-items {
    display: flex;
    padding: var(--eds-spacing-200);
    padding-top: 0;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-space-0, 0px);
    align-self: stretch;
    border-radius: var(--radius-radius-0, 0px);

    @media (min-width: 834px) {
      padding: var(--eds-spacing-400);
      padding-top: 0;
    }

    .basket-item {
      display: flex;
      padding: var(--spacing-space-300, 12px) 0;
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-space-100, 4px);
      align-self: stretch;
      border-bottom: 1px solid var(--colors-border-light, #efeff6);

      &:first-child {
        padding-top: 0;
      }

      &:last-child {
        padding-bottom: 0;
        border-bottom: none;
      }

      .item-name {
        color: #444;
        font-size: 14px;
      }
    }
  }
}
