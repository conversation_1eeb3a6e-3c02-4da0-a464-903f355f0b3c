import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { DataList } from '../../../model';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-offer-addon-card',
  templateUrl: 'offer-addon-card.component.html',
  styleUrl: 'offer-addon-card.component.scss',
  imports: [TranslatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OfferAddonCardComponent {
  addons = input<DataList>({});
}
