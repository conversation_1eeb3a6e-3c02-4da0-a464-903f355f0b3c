.base {
  display: flex;
  border: 1px solid var(--eds-border-color-default);
  border-radius: var(--eds-radius-400);

  &::part(base) {
    width: 100%;
  }

  eds-radio {
    padding: var(--eds-spacing-400);

    &:not(:last-of-type) {
      border-bottom: 1px solid var(--eds-border-color-default);
    }

    .radio-action_label {
      display: flex;
      flex-direction: column;
      gap: var(--eds-spacing-100);
    }
  }
}
