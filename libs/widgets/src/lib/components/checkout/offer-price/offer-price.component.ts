import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { Offer } from '@libs/types';
import { CurrencyPipe } from '@angular/common';

@Component({
  selector: 'widget-offer-price',
  templateUrl: './offer-price.component.html',
  styleUrls: ['./offer-price.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CurrencyPipe],
})
export class OfferPriceComponent {
  price = input<Offer.Price>();
  discount = input<Offer.Price>();
}
