<div class="base">
  <eds-heading as="h4" size="sm" [text]="titleText()" class="title"></eds-heading>

  <eds-radio-group class="delivery-method-group" name="delivery-method" (radio-change)="selectRadio($event)">
    @for (item of deliveryMethods(); track item.value) {
      <eds-radio
        [id]="'method-' + item.id"
        [value]="item.value"
        [name]="'method-' + item.name"
        [isChecked]="item.isChecked"
        [isDisabled]="item.isDisabled"
      >
        <label [for]="'method-' + item.id">{{ item.label }}</label>
      </eds-radio>
    }
  </eds-radio-group>
</div>
