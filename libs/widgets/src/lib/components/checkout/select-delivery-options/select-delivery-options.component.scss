.base {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: var(--eds-spacing-400, 16px);
  align-self: stretch;

  eds-radio-group {
    width: 100%;
    border-radius: var(--radius-radius-300, 12px);
    border: var(--stroke-stroke-025, 1px) solid var(--colors-border-default, #dedeed);

    eds-radio::part(content) {
      width: 100%;
    }

    eds-radio {
      padding: var(--eds-spacing-200);

      @media (min-width: 834px) {
        padding: var(--eds-spacing-400);
      }

      label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: var(--eds-spacing-400, 16px);

        .label-wrapper {
          display: flex;
          flex-direction: column;
          gap: var(--eds-spacing-100, 4px);
        }

        .label-description {
          display: flex;
          flex-direction: column;
          gap: var(--eds-spacing-100);

          @media (min-width: 834px) {
            flex-direction: row;
          }
        }

        .label-title {
          font-size: var(--eds-font-size-md, 16px);
          font-weight: var(--eds-font-weight-bold, 500);
        }

        .label-price {
          font-size: var(--eds-font-size-md, 16px);
          font-weight: var(--eds-font-weight-bold, 500);
        }
      }

      &:not(:last-of-type) {
        border-bottom: 1px solid var(--eds-border-color-default);
      }
    }
  }
}
