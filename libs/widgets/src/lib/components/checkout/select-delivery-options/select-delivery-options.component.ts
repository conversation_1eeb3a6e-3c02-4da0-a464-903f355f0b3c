import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { CurrencyPipe } from '@angular/common';
import { RadioDeliveryOption } from '@libs/types';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-select-delivery-options',
  templateUrl: './select-delivery-options.component.html',
  styleUrl: './select-delivery-options.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CurrencyPipe, TranslatePipe],
})
export class SelectDeliveryOptionsComponent {
  titleText = input<string>('');
  deliveryOptions = input<RadioDeliveryOption[]>([]);

  radioChange = output<number>();

  selectRadio(event: Event) {
    this.radioChange.emit(Number((event.target as HTMLInputElement).value));
  }
}
