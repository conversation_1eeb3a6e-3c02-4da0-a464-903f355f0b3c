import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  input,
  output,
  signal,
} from '@angular/core';
import { AddressModel, SelectOption } from '../../../model';
import { EmptyAddressComponent } from '../empty-address/empty-address.component';

@Component({
  selector: 'widget-select-address',
  templateUrl: './select-address.component.html',
  styleUrl: './select-address.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [EmptyAddressComponent],
})
export class SelectAddressComponent {
  addressProp = input<AddressModel>();

  emptyAddress = input<AddressModel>();

  addressList = input<SelectOption[]>([]);

  addAddress = output<void>();

  selectAddress = output<string>();

  countryChangeKey = signal(0);

  constructor() {
    effect(() => {
      const addresses = this.addressList();
      if (addresses && addresses.length) {
        const selectedOption = addresses.find((option) => option.isSelected);
        if (selectedOption) {
          this.selectAddress.emit(String(selectedOption?.value));
          this.countryChangeKey.update((key) => key + 1);
        }
      }
    });
  }

  selectedOption(event: Event) {
    const value = (event.target as HTMLInputElement).value;
    this.selectAddress.emit(value);
    this.countryChangeKey.update((key) => key + 1);
  }
}
