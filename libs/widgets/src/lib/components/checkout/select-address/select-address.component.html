<div class="base">
  <eds-heading as="h4" size="sm" [text]="addressProp().title" class="title"></eds-heading>

  @if (!addressList()?.length) {
    <widget-empty-address [addressProp]="emptyAddress()" (addAddress)="addAddress.emit()"></widget-empty-address>
  } @else {
    @for (key of [countryChangeKey()]; track key) {
      <eds-form-field [label]="addressProp().selectLabel">
        <eds-select [isSearchable]="false" (option-selected)="selectedOption($event)">
          @for (option of addressList(); track $index) {
            <eds-select-option
              [label]="option.label"
              [value]="option.value"
              [name]="option.name"
              [isSelected]="option.isSelected"
              [isDisabled]="option.isDisabled"
            >
            </eds-select-option>
          }
        </eds-select>
      </eds-form-field>
    }
    <eds-button appearance="default" [iconLeading]="addressProp().buttonIcon" (button-click)="addAddress.emit()">
      {{ addressProp().buttonText }}
    </eds-button>
  }
</div>
