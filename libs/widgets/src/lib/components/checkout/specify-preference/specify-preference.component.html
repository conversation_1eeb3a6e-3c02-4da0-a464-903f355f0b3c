<div class="base">
  <eds-heading as="h4" size="sm" [text]="titleText() | translate" class="title"></eds-heading>

  <eds-radio-group class="specify-preferences-group" name="specify-preferences" (radio-change)="selectRadio($event)">
    @for (item of specifyPreferences(); track item.value) {
      <eds-radio
        [id]="'preference-' + item.id"
        [value]="item.value"
        [name]="item.name"
        [isChecked]="item.isChecked"
        [isDisabled]="item.isDisabled"
      >
        <label [for]="'preference-' + item.id">{{ item.label }}</label>
      </eds-radio>
    }
  </eds-radio-group>
</div>
