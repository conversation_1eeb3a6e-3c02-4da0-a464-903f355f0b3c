.base {
  display: flex;
  gap: var(--eds-spacing-400);
}

.content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--eds-spacing-200);
  justify-content: space-between;
  width: 100%;

  @media (min-width: 834px) {
    flex-direction: row;
    gap: var(--eds-spacing-400);
  }

  .tags {
    display: flex;
    align-items: flex-start;
    gap: var(--eds-spacing-300);
    flex-direction: column;

    @media (min-width: 834px) {
      flex-direction: row;
      flex-wrap: wrap;
    }
  }

  .info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--eds-spacing-200);
    flex: 1 0 0;
    align-self: stretch;

    .title-container {
      position: relative;
      display: flex;
      align-items: center;
      gap: var(--eds-spacing-200);
      justify-content: space-between;
      width: 100%;
      padding-right: calc(var(--eds-size-multiplier) * 10);

      widget-item-delete-action {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }

      @media (min-width: 834px) {
        padding-right: 0;
        widget-item-delete-action {
          display: none;
        }
      }
    }

    .property,
    .sim-property {
      display: flex;
      align-items: flex-start;
      gap: var(--eds-spacing-200);

      .key {
        overflow: hidden;
        color: var(--eds-colors-text-light);
        font-feature-settings:
          'liga' off,
          'clig' off;
        text-overflow: ellipsis;
      }
    }

    .property {
      flex-direction: column;
    }

    .dynamic-chars {
      display: flex;
      align-items: baseline;
      gap: var(--eds-spacing-050);
      flex-wrap: wrap;
    }
  }

  .price {
    position: relative;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    gap: var(--eds-spacing-300);

    widget-item-delete-action {
      display: none;
    }

    @media (min-width: 834px) {
      width: unset;
      widget-item-delete-action {
        display: block;
      }
    }

    &:has(.hidden) {
      padding-right: calc(var(--eds-size-multiplier) * 11);
      align-items: flex-start;

      widget-item-delete-action {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .hidden {
      opacity: 0;
      pointer-events: none;
    }
  }
}
