import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { Characteristic, Offer } from '@libs/types';
import { OfferPriceComponent } from '../offer-price/offer-price.component';
import { PhoneNumberPipe } from '@libs/core';
import { TranslatePipe } from '@libs/plugins';
import { ItemDeleteActionComponent } from '../../item-delete-action';

@Component({
  selector: 'widget-offer-plan-card',
  templateUrl: './offer-plan-card.component.html',
  styleUrls: ['offer-plan-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [OfferPriceComponent, PhoneNumberPipe, ItemDeleteActionComponent, TranslatePipe],
})
export class OfferPlanCardComponent {
  showPrice = input(false);
  showDetails = input(false);

  orderItemId = input<number>();
  offerName = input<string>();
  price = input<Offer.Price>();
  discount = input<Offer.Price>();
  chars = input<Characteristic[]>();
  phoneNumber = input<string>();
  simType = input<string>();
  isOrderSummary = input(false);
  showDelete = input(true);
  deleteConfirmationText = input<string>('');
  tags = input<{ text: string; appearance: string }[]>([]);
  delete = output();
  dynamicChars = input<string[]>([]);

  onDelete() {
    this.delete.emit();
  }
}
