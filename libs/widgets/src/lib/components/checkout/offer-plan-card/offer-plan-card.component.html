<div class="base">
  <div class="content">
    <div class="info">
      @if (tags().length) {
        <div class="tags">
          @for (tag of tags(); track $index) {
            <eds-tag part="status" [content]="tag.text" [appearance]="tag.appearance"></eds-tag>
          }
        </div>
      }
      <div class="title-container">
        <eds-heading as="h3" size="md" [text]="offerName()" class="title"></eds-heading>
        @if (showDelete()) {
          <widget-item-delete-action
            [deleteConfirmationText]="deleteConfirmationText()"
            (deleteItem)="onDelete()"
            #deleteAction
          ></widget-item-delete-action>
        }
      </div>
      @if (phoneNumber() && showDetails()) {
        <eds-text size="md" [text]="phoneNumber() | phoneNumber"></eds-text>
      }

      @if (simType() && showDetails()) {
        <div class="sim-property">
          <eds-text class="key" size="sm" [text]="'simType' | translate"></eds-text>
          <eds-text size="sm" weight="medium" [text]="simType()"></eds-text>
        </div>
      }
      @if (showDetails()) {
        @if (dynamicChars().length) {
          <div class="dynamic-chars">
            <eds-text class="key" size="sm" [text]="('whatsIncluded' | translate) + ':'"></eds-text>
            @for (char of dynamicChars(); track $index) {
              <eds-text size="sm" weight="medium" [text]="char + (!$last ? ' +' : '')"></eds-text>
            }
          </div>
        } @else {
          @for (char of chars(); track $index) {
            <div class="property">
              <eds-text class="key" size="sm" [text]="char.name"></eds-text>
              <eds-text size="sm" weight="medium" [text]="char.value"></eds-text>
            </div>
          }
        }
      }
    </div>
    @if (showPrice()) {
      <div class="price">
        <widget-offer-price
          [price]="price()"
          [discount]="discount()"
          [class.hidden]="deleteAction.deleteActionTriggered()"
        ></widget-offer-price>
        <widget-item-delete-action
          [deleteConfirmationText]="deleteConfirmationText()"
          (deleteItem)="onDelete()"
          #deleteAction
        ></widget-item-delete-action>
      </div>
    }
  </div>
</div>
