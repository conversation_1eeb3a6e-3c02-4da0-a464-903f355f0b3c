import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  input,
  output,
  signal,
  OnInit,
} from '@angular/core';
import { ShoppingCardItem, ShoppingCardTotal } from '../../../model';
import { ShoppingSummaryListComponent } from '../shopping-summary-table/shopping-summary-list.component';

@Component({
  selector: 'widget-shopping-card',
  templateUrl: './shopping-card.component.html',
  styleUrls: ['./shopping-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ShoppingSummaryListComponent],
})
export class ShoppingCardComponent implements OnInit {
  items = input<ShoppingCardItem[]>();
  total = input<ShoppingCardTotal>();
  actionButtonText = input<string>();
  actionButtonDisabled = input<boolean>();

  shoppingSummaryList = signal<boolean>(false);
  isDesktop = signal<boolean>(false);

  onAction = output();
  onFooterAction = output();

  ngOnInit(): void {
    this.checkScreenWidth();
    window.addEventListener('resize', () => this.checkScreenWidth());
  }

  private checkScreenWidth(): void {
    this.isDesktop.set(window.innerWidth >= 1440);
    if (this.isDesktop()) {
      this.shoppingSummaryList.set(true);
    } else {
      this.shoppingSummaryList.set(false);
    }
  }

  onButtonClick() {
    this.onAction.emit();
  }

  onFooterClick() {
    if (!this.isDesktop()) {
      this.shoppingSummaryList.set(!this.shoppingSummaryList());
    }
  }
}
