<div class="base">
  @if (preAuthorized()) {
    <div class="edit-line">
      <div>
        <eds-text size="lg" [weight]="'medium'" [text]="computedPaymentMethodTitle() | translate"></eds-text>
      </div>
      @if (showEditButton()) {
        <eds-button size="compact" appearance="link" (button-click)="editPaymentMethod.emit()">
          {{ 'edit' | translate }}
        </eds-button>
      }
    </div>
    <widget-saved-payment-method
      [id]="preAuthorized().paymentMethodId"
      [name]="preAuthorized().name"
      [inputName]="PaymentMethodSelectionTypes.AUTHORIZED"
      [isChecked]="true"
      [nameOnBankAccount]="preAuthorized().nameOnBankAccount"
      [networkLogo]="preAuthorized().paymentMethodLogo"
      [cardNumber]="preAuthorized().formatedLastFourDigit"
      [cardExpiry]="preAuthorized().expiryDate"
      [isSelected]="isSelected()"
    ></widget-saved-payment-method>
  }

  @if (payNow()) {
    <div class="edit-line">
      <div>
        <eds-text size="lg" [weight]="'medium'" [text]="computedPaymentMethodTitle() | translate"></eds-text>
      </div>
      @if (showEditButton()) {
        <eds-button size="compact" appearance="link" (button-click)="editPaymentMethod.emit()">
          {{ 'edit' | translate }}
        </eds-button>
      }
    </div>

    <widget-saved-payment-method
      [id]="payNow().paymentMethodId"
      [name]="payNow().name"
      [inputName]="PaymentMethodSelectionTypes.PAY_NOW"
      [isChecked]="true"
      [nameOnBankAccount]="payNow().nameOnBankAccount"
      [networkLogo]="payNow().paymentMethodLogo"
      [cardNumber]="payNow().formatedLastFourDigit"
      [cardExpiry]="payNow().expiryDate"
      [isSelected]="isSelected()"
    ></widget-saved-payment-method>
  }
</div>
