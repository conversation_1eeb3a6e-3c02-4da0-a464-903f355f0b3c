import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { PaymentMethodSelectionData, PaymentMethodSelectionTypes } from '../../../model/payment-method.model';
import { SavedPaymentMethodComponent } from '../../saved-payment-method/saved-payment-method.component';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-payment-selected-methods',
  templateUrl: './payment-selected-methods.component.html',
  styleUrls: ['./payment-selected-methods.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [SavedPaymentMethodComponent, TranslatePipe],
})
export class PaymentSelectedMethodsComponent {
  savedPaymentMethods = input<Record<PaymentMethodSelectionTypes, PaymentMethodSelectionData>>({
    [PaymentMethodSelectionTypes.AUTHORIZED]: undefined,
    [PaymentMethodSelectionTypes.PAY_NOW]: undefined,
  });

  isSelected = input<boolean>();

  paymentMethodTitle = input<string>('');

  showEditButton = input<boolean>(false);

  editPaymentMethod = output<void>();

  preAuthorized = computed(() => this.savedPaymentMethods()?.[PaymentMethodSelectionTypes.AUTHORIZED]?.data);
  payNow = computed(() => this.savedPaymentMethods()?.[PaymentMethodSelectionTypes.PAY_NOW]?.data);

  computedPaymentMethodTitle = computed(() => {
    return this.paymentMethodTitle()
      ? this.paymentMethodTitle()
      : this.preAuthorized()
        ? 'paymentMethod'
        : 'payNowPaymentMethod';
  });

  PaymentMethodSelectionTypes = PaymentMethodSelectionTypes;
}
