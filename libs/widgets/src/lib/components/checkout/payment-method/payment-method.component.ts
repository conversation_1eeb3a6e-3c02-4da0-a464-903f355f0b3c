import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  input,
  output,
  signal,
} from '@angular/core';
import { ePayment } from '@libs/types';
import { BankAccountFormFields, CreditCardFormFields } from '@libs/widgets';
import { PaymentMethodSelectionData, SavedPaymentMethod } from '../../../model/payment-method.model';
import { PaymentMethodBankAccountComponent } from '../../payment-method/payment-method-bank-account';
import { PaymentMethodCreditCardComponent } from '../../payment-method/payment-method-credit-card';
import { PaymentMethodPayInStoreComponent } from '../../payment-method/payment-method-pay-in-store';
import { PaymentMethodPayPalComponent } from '../../payment-method/payment-method-paypal';
import { uuid } from '@libs/bss';
import { RadioGroupDirective } from '@libs/core';

@Component({
  selector: 'widget-payment-method',
  templateUrl: './payment-method.component.html',
  styleUrls: ['./payment-method.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    PaymentMethodCreditCardComponent,
    PaymentMethodBankAccountComponent,
    PaymentMethodPayPalComponent,
    PaymentMethodPayInStoreComponent,
    RadioGroupDirective,
  ],
})
export class PaymentMethodComponent {
  inputName = input<string>();
  authorized = input<boolean>(false);
  savedCreditCards = input<SavedPaymentMethod[]>([]);
  savedBankAccounts = input<SavedPaymentMethod[]>([]);
  showBankAccounts = input<boolean>(false);
  showCreditCards = input<boolean>(false);
  latestPaymentMethod = input<ePayment.PaymentMethodType>(undefined);
  defaultSelection = input<PaymentMethodSelectionData | undefined>(undefined);

  createCreditCard = output<CreditCardFormFields | void>();
  createBankAccount = output<BankAccountFormFields | void>();
  deleteCreditCard = output<string | number>();
  deleteBankAccount = output<string | number>();

  currentSelection = signal<PaymentMethodSelectionData | undefined>(undefined);
  selectedPaymentMethodType = signal<ePayment.PaymentMethodType>(this.latestPaymentMethod());
  selectionChange = output<PaymentMethodSelectionData>();

  bankPaymentMethodType = ePayment.PaymentMethodType;

  uuid = uuid();

  constructor() {
    effect(() => {
      const defaultValue = this.defaultSelection();
      if (defaultValue?.id) {
        this.onSelectionChange(defaultValue);
      }
    });
  }

  handleInputChange(newPaymentMethodType: ePayment.PaymentMethodType) {
    if (this.currentSelection()?.type === newPaymentMethodType && this.currentSelection()?.id !== undefined) {
      this.selectedPaymentMethodType.set(newPaymentMethodType);
      return;
    }

    const newSelection: PaymentMethodSelectionData = { type: newPaymentMethodType, id: undefined };
    this.currentSelection.set(newSelection);
    this.selectedPaymentMethodType.set(newPaymentMethodType);
    this.selectionChange.emit(newSelection);
  }

  onSelectionChange(paymentMethodData: PaymentMethodSelectionData) {
    this.currentSelection.set(paymentMethodData);
    this.selectedPaymentMethodType.set(paymentMethodData.type);

    this.selectionChange.emit(paymentMethodData);
  }

  onDeleteCreditCard(id: string | number) {
    this.deleteCreditCard.emit(id);
  }

  onDeleteBankAccount(id: string | number) {
    this.deleteBankAccount.emit(id);
  }
}
