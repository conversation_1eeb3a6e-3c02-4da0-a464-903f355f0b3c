import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { ShoppingCardItem, ShoppingCardTotal } from '../../../model';

@Component({
  selector: 'widget-shopping-summary-list',
  templateUrl: './shopping-summary-list.component.html',
  styleUrls: ['./shopping-summary-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ShoppingSummaryListComponent {
  items = input<ShoppingCardItem[]>();
  total = input<ShoppingCardTotal>();
}
