<div class="list">
  @for (item of items(); track $index) {
    <div class="list-item">
      <p [class]="item.class">{{ item.name }}</p>
      <p [class]="item.class">{{ item.amount }}</p>
    </div>
    @if ($index !== items().length - 1) {
      <div class="divider"></div>
    }
  }
</div>

@if (total()) {
  <div class="footer">
    <div class="total">
      <p>{{ total()?.key }}</p>
      <p class="amount">{{ total()?.amount }}</p>
    </div>
  </div>
}
