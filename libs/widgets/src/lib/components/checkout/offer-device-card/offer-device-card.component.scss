.base {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-200);
  padding: var(--eds-spacing-300);
  border-radius: var(--eds-radius-300);
  background: var(--eds-colors-surface-level-1);

  @media (min-width: 834px) {
    flex-direction: row;
    gap: var(--eds-spacing-400);
  }

  .image-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    widget-item-delete-action {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    @media (min-width: 834px) {
      padding-right: 0;
      width: unset;
      widget-item-delete-action {
        display: none;
      }
    }
  }

  eds-image {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--eds-spacing-300);
    width: var(--eds-sizing-900);
    height: var(--eds-sizing-900);
    border-radius: var(--eds-radius-200);
    border: var(--eds-stroke-025) solid var(--eds-border-color-light);
    background: var(--eds-colors-surface-default);
  }
}

.content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--eds-spacing-200);
  justify-content: space-between;
  width: 100%;

  @media (min-width: 834px) {
    flex-direction: row;
    gap: var(--eds-spacing-400);
  }

  .device-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--eds-spacing-200);
    flex: 1;

    .device-chars {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      gap: var(--eds-spacing-200);

      .device-property {
        display: flex;
        align-items: flex-start;
        gap: var(--eds-spacing-200);

        .key {
          overflow: hidden;
          color: var(--eds-colors-text-light);
          font-feature-settings:
            'liga' off,
            'clig' off;
          text-overflow: ellipsis;
        }

        .value {
          overflow: hidden;
          color: var(--eds-colors-text-default);
          font-feature-settings:
            'liga' off,
            'clig' off;
          text-overflow: ellipsis;
        }

        .property-value {
          display: flex;
          align-items: center;
          gap: var(--eds-spacing-100);

          .color-indicator {
            width: calc(var(--eds-size-multiplier) * 3.5);
            height: calc(var(--eds-size-multiplier) * 3.5);
            flex-shrink: 0;
            border-radius: var(--eds-radius-full);
            border: var(--eds-stroke-025) solid var(--eds-border-color-default);
          }
        }
      }
    }
  }

  .device-price {
    position: relative;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    gap: var(--eds-spacing-300);
    width: 100%;

    widget-item-delete-action {
      display: none;
    }

    @media (min-width: 834px) {
      width: unset;
      align-items: flex-start;
      widget-item-delete-action {
        display: block;
      }
    }

    &:has(.hidden) {
      padding-right: calc(var(--eds-size-multiplier) * 11);
      align-items: flex-start;

      widget-item-delete-action {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .hidden {
      opacity: 0;
      pointer-events: none;
    }
  }
}
