@if (offerName()) {
  <div class="base">
    <div class="image-container">
      <eds-image
        [src]="media()?.imageSrc || 'assets/images/phone.png'"
        [alt]="media()?.imageAlt"
        width="100%"
      ></eds-image>
      @if (showDelete()) {
        <widget-item-delete-action
          [deleteConfirmationText]="deleteConfirmationText()"
          (deleteItem)="onDelete()"
          #deleteAction
        ></widget-item-delete-action>
      }
    </div>

    <div class="content">
      <div class="device-info">
        <eds-text size="lg" weight="medium" [text]="offerName()"></eds-text>
        <div class="device-chars">
          @for (char of chars(); track $index) {
            <div class="device-property">
              <eds-text class="key" size="sm" [text]="char.name + ':' | titlecase"></eds-text>

              <div class="property-value">
                @if (char.colorCode) {
                  <span [style.background-color]="char.colorCode" class="color-indicator"></span>
                }
                <eds-text class="value" size="sm" weight="medium" [text]="char.value | titlecase"></eds-text>
              </div>
            </div>
          }
        </div>
      </div>
      @if (showPrice()) {
        <div class="device-price">
          @if (!deleteActionTriggered()) {
            <widget-offer-price [price]="price()" [discount]="discount()"></widget-offer-price>
          }
          @if (showDelete()) {
            <widget-item-delete-action
              [deleteConfirmationText]="deleteConfirmationText()"
              (deleteItem)="onDelete()"
              (onDeleteAction)="onDeleteAction()"
              (onCancelDeleteAction)="onCancelDelete()"
            ></widget-item-delete-action>
          }
        </div>
      }
    </div>
  </div>
}
