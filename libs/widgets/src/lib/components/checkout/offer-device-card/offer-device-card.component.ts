import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output, signal } from '@angular/core';
import { Offer } from '@libs/types';
import { OfferPriceComponent } from '../offer-price/offer-price.component';
import { TitleCasePipe } from '@angular/common';
import { Media } from '../../../model';
import { ItemDeleteActionComponent } from '../../item-delete-action';

@Component({
  selector: 'widget-offer-device-card',
  templateUrl: './offer-device-card.component.html',
  styleUrls: ['./offer-device-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [OfferPriceComponent, TitleCasePipe, ItemDeleteActionComponent],
})
export class OfferDeviceCardComponent {
  deleteActionTriggered = signal(false);

  offerName = input<string>();

  price = input<Offer.Price>();

  discount = input<Offer.Price>();

  chars = input<Offer.Char[]>();

  media = input<Media>();

  showPrice = input<boolean>(false);

  showDelete = input<boolean>(false);

  deleteConfirmationText = input<string>('');

  delete = output();

  onDelete() {
    this.delete.emit();
  }

  onDeleteAction() {
    this.deleteActionTriggered.set(true);
  }

  onCancelDelete() {
    this.deleteActionTriggered.set(false);
  }
}
