import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { AddressModel } from '../../../model';

@Component({
  selector: 'widget-empty-address',
  templateUrl: './empty-address.component.html',
  styleUrl: './empty-address.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class EmptyAddressComponent {
  addressProp = input<AddressModel>();

  addAddress = output<void>();
}
