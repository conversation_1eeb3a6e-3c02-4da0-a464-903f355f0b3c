.base {
  display: flex;
  padding: var(--eds-spacing-600) var(--eds-spacing-400);
  flex-direction: column;
  align-items: center;
  gap: var(--eds-spacing-400);
  align-self: stretch;
  border-radius: var(--radius-radius-300, 12px);
  border: 1px solid var(--colors-border-default, #dedeed);
}

eds-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.icon-circle {
  display: flex;
  width: 48px;
  height: 48px;
  padding: 12px;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-radius-full, 999px);
  border: 1px solid var(--colors-border-default, #dedeed);
}

.text-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--eds-spacing-300);
  align-self: stretch;
}

.title {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--eds-spacing-100);
  align-self: stretch;
}
