import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  input,
  output,
  signal,
} from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { PAYMENT_TYPE_MAP, PRODUCT_STATUS_MAP, SelectOption } from '@libs/types';

interface ActiveFilters {
  billingAccountId: string;
  type: string;
  status: string;
}

@Component({
  selector: 'widget-account-filter',
  templateUrl: './account-filter.component.html',
  styleUrls: ['./account-filter.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe],
})
export class AccountFilterComponent {
  rawBillingAccountIds = input<string[]>([]);
  activeFilters = input<ActiveFilters>();

  readonly typeOptions: SelectOption[] = this.createTypeOptions();
  readonly statusOptions: SelectOption[] = this.createStatusOptions();

  filtersChanged = output<ActiveFilters>();

  filters = signal<ActiveFilters>({
    billingAccountId: '',
    type: '',
    status: '',
  });

  constructor() {
    effect(() => {
      const filters = this.activeFilters();

      if (filters) {
        this.filters.set(filters);
      }
    });
  }

  readonly billingAccountIdOptions = computed(() => {
    const billingAccountIds = this.rawBillingAccountIds();
    const options = billingAccountIds
      .map((sub: string) => {
        return {
          label: `${sub}`,
          value: sub,
          name: sub,
        } as SelectOption;
      })
      .filter((option: SelectOption) => option !== null) as SelectOption[];

    const uniqueOptions = Array.from(new Map(options.map((item) => [item.value, item])).values());
    return uniqueOptions;
  });

  onBillingAccountIdSelected(event: Event) {
    this.filters.update((f) => ({
      ...f,
      billingAccountId: this.getEventValue(event),
    }));
    this.notifyChanges();
  }

  onTypeSelected(event: Event) {
    this.filters.update((f) => ({ ...f, type: this.getEventValue(event) }));
    this.notifyChanges();
  }

  onStatusSelected(event: Event) {
    this.filters.update((f) => ({ ...f, status: this.getEventValue(event) }));
    this.notifyChanges();
  }

  private createTypeOptions(): SelectOption[] {
    const options = Object.entries(PAYMENT_TYPE_MAP).map(([shortCode, displayName]) => ({
      label: displayName,
      value: shortCode,
      name: displayName,
    }));
    return options;
  }

  private createStatusOptions(): SelectOption[] {
    const options = Object.entries(PRODUCT_STATUS_MAP).map(([shortCode, displayName]: [string, string]) => ({
      label: displayName,
      value: shortCode,
      name: displayName,
    }));
    return options;
  }

  private getEventValue(event: Event): string {
    return (event.target as HTMLInputElement).value;
  }

  private notifyChanges() {
    this.filtersChanged.emit(this.filters());
  }
}
