<eds-card>
  <eds-select
    #subscriptionSelect
    [placeholder]="'billingAccountId' | translate"
    [value]="activeFilters()?.billingAccountId"
    (option-selected)="onBillingAccountIdSelected($event)"
  >
    <eds-select-option [label]="'viewAllBillingAccountId' | translate" value="" name="all"></eds-select-option>
    @for (option of billingAccountIdOptions(); track option.value) {
      <eds-select-option [label]="option.label" [value]="option.value" [name]="option.name"></eds-select-option>
    }
  </eds-select>

  <eds-select
    #typeSelect
    [placeholder]="'types' | translate"
    [value]="activeFilters()?.type"
    (option-selected)="onTypeSelected($event)"
  >
    <eds-select-option [label]="'viewAllTypes' | translate" value="" name="all"></eds-select-option>
    @for (option of typeOptions; track option.value) {
      <eds-select-option [label]="option.label" [value]="option.value" [name]="option.name"></eds-select-option>
    }
  </eds-select>

  <eds-select
    #statusSelect
    [placeholder]="'status' | translate"
    [value]="activeFilters()?.status"
    (option-selected)="onStatusSelected($event)"
  >
    <eds-select-option [label]="'viewAllStatuses' | translate" value="" name="all"></eds-select-option>
    @for (option of statusOptions; track option.value) {
      <eds-select-option [label]="option.label" [value]="option.value" [name]="option.name"></eds-select-option>
    }
  </eds-select>
</eds-card>
