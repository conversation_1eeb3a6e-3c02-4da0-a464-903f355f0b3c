import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { SwiperOptions } from '../../../model/swiper.model';
import { CarouselItem } from '../../../model/carousel-item.model';
import { SwiperComponent } from '../../swiper';
import { InvoiceCardComponent } from '../invoice-card/invoice-card.component';

@Component({
  selector: 'widget-invoice-carousel',
  templateUrl: './invoice-carousel.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./invoice-carousel.component.scss'],
  imports: [SwiperComponent, InvoiceCardComponent],
})
export class InvoiceCarouselComponent {
  items = input<CarouselItem[]>([]);

  swiperConfig = input<SwiperOptions>({
    spaceBetween: 16,
    slidesPerView: 1,
    loop: false,
    pagination: true,
  });

  pagination = input<boolean>(true);

  config = computed<SwiperOptions>(() => ({
    ...this.swiperConfig(),
    pagination: this.pagination(),
  }));
}
