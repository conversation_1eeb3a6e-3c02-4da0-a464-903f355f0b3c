:host {
  --eds-invoice-overview-alert-icon-size: var(--invoice-overview-alert-icon-size, var(--eds-sizing-400));
  --eds-invoice-overview-display: var(--invoice-overview-display, flex);
  --eds-invoice-overview-gap: var(--invoice-overview-gap, var(--eds-spacing-400));
}
.base {
  display: var(--eds-invoice-overview-display);
  flex-direction: column;
  gap: var(--eds-invoice-overview-gap);
}

.alert {
  &::part(wrapper) {
    align-items: center;
  }

  &::part(icon) {
    width: var(--eds-invoice-overview-alert-icon-size);
    height: var(--eds-invoice-overview-alert-icon-size);
  }

  &.no-description {
    &::part(content) {
      display: none;
    }

    &::part(title) {
      --eds-font-size-heading-sm: var(--eds-font-size-body-md);
      --eds-heading-font-weight: regular;
    }
  }
}
