<div class="base">
  <div class="header">
    <div class="title">
      <eds-heading as="h3" size="xl" [text]="title()"></eds-heading>
      <eds-heading as="h3" size="xl" [text]="amount()"></eds-heading>
    </div>
    <div class="information">
      <eds-text size="sm" [text]="paymentDueDateLabel()"></eds-text>
      <eds-text size="sm" [text]="paymentDueDate()"></eds-text>
    </div>
  </div>
  <div class="buttons">
    @if (!isPaymentCompleted() && paymentLink()) {
      <eds-button [href]="paymentLink()" appearance="secondary">
        {{ paymentLinkText() }}
      </eds-button>
    }
    @if (detailLink()) {
      <eds-button [href]="detailLink()" appearance="default">
        {{ detailLinkText() }}
      </eds-button>
    }
  </div>
</div>
