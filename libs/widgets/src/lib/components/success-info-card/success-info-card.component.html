<eds-card>
  <div class="base">
    <eds-icon class="icon" name="checkmarkCircleThin"></eds-icon>

    <div class="description">
      <eds-heading class="description-title" as="h3" size="md" [text]="description()"></eds-heading>
    </div>

    <eds-button class="button" appearance="secondary" size="default" (button-click)="onAction()">
      {{ buttonText() }}
    </eds-button>
  </div>
</eds-card>
