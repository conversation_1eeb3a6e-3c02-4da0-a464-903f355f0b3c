import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';

@Component({
  selector: 'widget-success-info-card',
  templateUrl: './success-info-card.component.html',
  styleUrls: ['./success-info-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SuccessInfoCardComponent {
  description = input<string>('');
  buttonText = input<string>('');

  action = output<void>();

  onAction() {
    this.action.emit();
  }
}
