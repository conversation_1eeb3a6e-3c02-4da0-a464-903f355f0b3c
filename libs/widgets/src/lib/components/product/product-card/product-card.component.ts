import { Component, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import type { ProductSummary } from '../../../model';
import { Router } from '@angular/router';
import { eProduct } from '@libs/types';
import ProductStatusShortCodes = eProduct.ProductStatusShortCodes;

@Component({
  selector: 'widget-product-card',
  templateUrl: './product-card.component.html',
  styleUrls: ['./product-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ProductCardComponent {
  private router = inject(Router);

  product = input<Partial<ProductSummary>>();

  goToDetail() {
    if (this.product().status !== ProductStatusShortCodes.CNCL) {
      this.router.navigate([this.product().detailLink]);
    }
  }

  get statusShortCodes(): typeof ProductStatusShortCodes {
    return ProductStatusShortCodes;
  }
}
