<a
  part="base"
  [class]="product().statusTagAppearance"
  (click)="goToDetail()"
  [style.cursor]="product().status !== statusShortCodes.CNCL ? 'pointer' : ''"
>
  @if (product().owner) {
    <eds-text part="owner" [text]="product().owner" as="p" size="sm" weight="regular"></eds-text>
  }
  <div part="info">
    @if (product().iconName || product().name || product().description) {
      <eds-media-object
        part="details"
        [iconName]="product().iconName"
        [text]="product().name"
        [description]="product().description"
        [upperText]="product().upperText"
      >
      </eds-media-object>
    }
    @if (product().statusTagAppearance && product().statusText) {
      <div part="status">
        <eds-tag [content]="product().statusText" [appearance]="product().statusTagAppearance"></eds-tag>
        <div class="date" [class]="product().statusTagAppearance">
          <eds-icon name="calendar"></eds-icon>
          <eds-text [text]="product().activationDate" as="span" size="sm"></eds-text>
        </div>
      </div>
    }
  </div>

  @if (product().devices?.length) {
    <div part="devices">
      @for (device of product().devices; track device.name) {
        @if (!device.isByod) {
          <div part="device">
            <eds-media-object
              part="device-details"
              [text]="device.name"
              [src]="device.imagePath || null"
              [iconName]="device.imagePath ? null : 'smartphone'"
              [alt]="device.name"
            ></eds-media-object>
          </div>
        }
      }
    </div>
  }
</a>
