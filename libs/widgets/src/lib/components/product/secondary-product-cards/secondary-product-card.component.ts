import { Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { DataListItem } from '@eds/components';

@Component({
  selector: 'widget-secondary-product-card',
  styleUrls: ['./secondary-product-card.component.scss'],
  templateUrl: './secondary-product-card.component.html',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SecondaryProductCardComponent {
  productCardItemList = input<DataListItem[]>();
}
