:host {
  --eds-segmented-area-gap: var(--segmented-area-gap, var(--eds-spacing-600));
  --eds-segmented-area-content-gap: var(--segmented-area-content-gap, var(--eds-spacing-600));
  --eds-segmented-area-segment-background: var(--segmented-area-segment-background, var(--eds-colors-base-50));
}

:host ::ng-deep {
  [part='base'] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-segmented-area-gap);
  }

  [part='segment'] {
    --eds-segmented-control-background: var(--eds-segmented-area-segment-background);
  }

  [part='content'] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-segmented-area-content-gap);
  }
}

.load-more-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--eds-spacing-400);
  width: 100%;
  height: calc(var(--eds-size-multiplier) * 18);
  background-color: var(--eds-colors-body);
  border: 1px solid var(--eds-border-color-default);
  padding: var(--eds-spacing-600);
  border-radius: var(--eds-radius-400);
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--eds-colors-primary-light);
  }

  &.loading {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.include-box {
  margin-left: auto;
}

.top-info {
  display: flex;
  justify-content: space-between;
  gap: var(--eds-spacing-400);
}
