import { ChangeDetectionStrategy, Component, computed, input, signal } from '@angular/core';

export type IconSize = { width: number; height: number };

@Component({
  template: '',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AtomIconComponent {
  size = input<IconSize | number>(24);

  classes = input('');

  mutableClasses = signal('');

  computedClasses = computed(() => this.mutableClasses() || this.classes());

  computedSize = computed<IconSize>(() => {
    if (typeof this.size() === 'number') {
      return {
        width: this.size(),
        height: this.size(),
      } as IconSize;
    }
    return this.size() as IconSize;
  });
}
