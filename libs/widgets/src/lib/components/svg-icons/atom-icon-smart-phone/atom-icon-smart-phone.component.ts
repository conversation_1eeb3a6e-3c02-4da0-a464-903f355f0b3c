import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { AtomIconComponent } from '../atom-icon';

@Component({
  selector: 'atom-icon-smart-phone',
  templateUrl: './atom-icon-smart-phone.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: AtomIconComponent,
      useExisting: forwardRef(() => AtomIconSmartPhoneComponent),
    },
  ],
})
export class AtomIconSmartPhoneComponent extends AtomIconComponent {}
