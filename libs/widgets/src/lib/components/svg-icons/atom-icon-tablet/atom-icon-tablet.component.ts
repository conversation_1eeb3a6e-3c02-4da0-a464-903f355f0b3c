import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { AtomIconComponent } from '../atom-icon';

@Component({
  selector: 'atom-icon-tablet',
  templateUrl: './atom-icon-tablet.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: AtomIconComponent,
      useExisting: forwardRef(() => AtomIconTabletComponent),
    },
  ],
})
export class AtomIconTabletComponent extends AtomIconComponent {}
