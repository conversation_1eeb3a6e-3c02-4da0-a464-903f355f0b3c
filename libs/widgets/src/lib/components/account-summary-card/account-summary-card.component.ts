import { CUSTOM_ELEMENTS_SCHEMA, ChangeDetectionStrategy, Component, inject, input, output } from '@angular/core';
import { TranslatePipe, TranslateService } from '@libs/plugins';
import { AccountSubscriptionsComponent } from '../account-subscriptions';
import { getColorByProductStatus } from '@libs/core';
import { BillingAccountInfo, eProduct } from '@libs/types';

export interface AccountDetailItem {
  label: string;
  value: string;
}

@Component({
  selector: 'widget-account-summary-card',
  templateUrl: './account-summary-card.component.html',
  styleUrls: ['./account-summary-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe, AccountSubscriptionsComponent],
})
export class AccountSummaryCardComponent {
  private translateService = inject(TranslateService);
  getColorByProductStatus = getColorByProductStatus;
  title = input<string>('');

  details = input<AccountDetailItem[]>([]);
  statusText = input<string | undefined>();
  statusAppearance = input<eProduct.ProductStatusShortCodes>(eProduct.ProductStatusShortCodes.ACTV);
  subscriptions = input<BillingAccountInfo[]>([]);

  cardClick = output<string>();

  getDisplayStatusText(): string {

    if (this.statusAppearance() === eProduct.ProductStatusShortCodes.PASS) {
      return this.translateService.translate('deactivated').toUpperCase();
    }

    return this.statusText()?.toUpperCase();
  }
}
