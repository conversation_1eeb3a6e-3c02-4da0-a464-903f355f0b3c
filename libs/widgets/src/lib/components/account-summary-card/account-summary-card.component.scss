.info-card-container {
  cursor: pointer;
}

.info-card-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-card-body {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-card-details-container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem; /* Detay satırları arası boşluk */
}

.info-card-detail-item {
  display: flex;
  gap: 0.5rem; /* Etiket ve değer arası boşluk */
}

.detail-value {
  color: var(--eds-colors-text-light);
}

.line {
  margin-bottom: 1rem;
  border: 1px solid #efeff6;
}

.warning-alert::part(wrapper) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 0.5rem !important; /* 8px */
}

.warning-alert::part(icon) {
  width: 24px !important;
  height: 24px !important;
}

.warning-alert::part(head) {
  width: auto !important;
}

eds-card {
  --eds-card-overflow: visible;
}
