<div class="info-card-container">
  <eds-card>
    <div class="info-card-content">
      <div (click)="cardClick.emit(details()[0].value)">
        <div class="info-card-header">
          <div class="info-card-status">
            @if (statusText()) {
              <eds-tag
                [content]="getDisplayStatusText()"
                [appearance]="getColorByProductStatus(statusAppearance())"
              ></eds-tag>
            }
          </div>
          <eds-icon name="arrowRight" class="info-card-arrow"></eds-icon>
        </div>

        <div class="info-card-body">
          <eds-heading size="md" [text]="title()"></eds-heading>

          @if (details().length > 0) {
            <div class="info-card-details-container">
              @for (detail of details(); track detail.label) {
                <div class="info-card-detail-item">
                  <eds-text
                    size="lg"
                    weight="medium"
                    text="{{ (detail.label | translate) + ':' }}"
                    class="detail-value"
                  ></eds-text>
                  <eds-text size="lg" weight="medium" [text]="detail.value"></eds-text>
                </div>
              }
            </div>
          }
        </div>
      </div>
      <div class="card-footer">
        @if (subscriptions() && subscriptions().length > 0) {
          <div class="line"></div>
          <widget-account-subscriptions [subscriptions]="subscriptions()"></widget-account-subscriptions>
        }
      </div>
    </div>
  </eds-card>
</div>
