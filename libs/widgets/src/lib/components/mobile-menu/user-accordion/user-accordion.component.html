@if (loggedIn()) {
  <eds-accordion [caption]="name()" [isOpen]="true">
    <div class="user-info" slot="header">
      <div class="info">
        <eds-icon name="user"></eds-icon>
        <eds-text [text]="name()"></eds-text>
      </div>
      <eds-icon name="arrowDown"></eds-icon>
    </div>
    <eds-heading class="greeting" size="xs" [text]="'greeting' | translate: { name: name() }"></eds-heading>
    <div class="content">
      <!--
      <div class="key-functions">
        <eds-text [text]="'KEY FUNCTIONS'"></eds-text>
        @for (function of keyFunctions(); track function.icon) {
          <div class="function" [routerLink]="function.link">
            <eds-icon [name]="function.icon"></eds-icon>
            <eds-text align="center" [text]="function.text"></eds-text>
          </div>
        }
      </div>
      -->
      <div class="invoices">
        <!--        <div class="invoices-header">
          <eds-text [text]="'MY INVOICE'"></eds-text>
          &lt;!&ndash;<eds-button appearance="link" size="compact" shouldFitContainer iconTrailing="arrowRight">See All</eds-button>&ndash;&gt;
        </div>
        <div class="current-amount">
          <eds-text size="sm" weight="medium" [text]="'Current Amount'"></eds-text>
          <div class="amount">
            <div class="date">
              <eds-text size="sm" [text]="'invoice cut-off date'"></eds-text>
              <eds-text size="md" weight="medium" [text]="'20.06.2025'"></eds-text>
              &lt;!&ndash; TODO DÜZELTİLECEK MOCK ŞUAN &ndash;&gt;
            </div>
            <eds-heading size="sm" [text]="currentInvoice().amount"></eds-heading>
          </div>
        </div>-->
        @if (invoices()?.length > 0) {
          <div class="invoices-list">
            <div class="alert">
              <eds-icon name="alertCircle"></eds-icon>
              <eds-text size="sm" weight="medium" [text]="'You have one unpaid bill.'"></eds-text>
            </div>
            @for (invoice of invoices(); track invoice.id) {
              <div class="invoice">
                <eds-text size="sm" weight="medium" [text]="invoice.month"></eds-text>
                <div class="amount">
                  <div class="date">
                    <eds-text size="sm" [text]="'payment due date'"></eds-text>
                    <eds-text size="md" weight="medium" [text]="invoice.date"></eds-text>
                  </div>
                  <eds-heading size="sm" [text]="invoice.amount"></eds-heading>
                </div>
              </div>
            }
            <!--
            @if (invoices().length > 0) {
              <eds-button appearance="default" shouldFitContainer iconTrailing="arrowRight">Pay Now</eds-button>
            }-->
          </div>
        }
      </div>
    </div>
    <eds-button
      appearance="secondary"
      shouldFitContainer
      iconTrailing="arrowRight"
      (button-click)="redirectToOverview()"
      >My Overview</eds-button
    >
    <eds-button appearance="link" shouldFitContainer (button-click)="logoutEmitter()">Logout</eds-button>
  </eds-accordion>
} @else {
  <eds-button
    class="login-button"
    appearance="default"
    shouldFitContainer
    iconLeading="user"
    iconTrailing="straightArrowRight"
    (button-click)="loginEmitter()"
    >Login</eds-button
  >
}
