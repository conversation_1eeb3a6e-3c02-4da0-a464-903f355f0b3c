import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, inject, input, output } from '@angular/core';
import { Router } from '@angular/router';
import { TranslatePipe } from '@libs/plugins';
import { MobileMenuUserSummary } from '../../../model';

@Component({
  selector: 'widget-user-accordion',
  templateUrl: './user-accordion.component.html',
  styleUrls: ['./user-accordion.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe],
})
export class UserAccordionComponent {
  loggedIn = input();
  name = input();
  keyFunctions = input<MobileMenuUserSummary.KeyFunction[]>();
  currentInvoice = input<MobileMenuUserSummary.CurrentInvoice>();
  invoices = input<MobileMenuUserSummary.Invoice[]>();
  login = output();
  logout = output();
  private router = inject(Router);

  loginEmitter = () => this.login.emit();
  logoutEmitter = () => this.logout.emit();

  redirectToOverview() {
    this.router.navigate(['/my/overview']);
  }
}
