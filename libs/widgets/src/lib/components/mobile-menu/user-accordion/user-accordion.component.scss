.login-button {
  &::part(base) {
    justify-content: unset;
    background-color: var(--eds-colors-surface-default);
    border: var(--eds-stroke-025) solid var(--eds-border-color-light);
  }

  &::part(wrapper) {
    width: 100%;
  }

  &::part(label) {
    display: flex;
    width: 100%;
  }
}

eds-accordion {
  --eds-accordion-expanded-background-color: var(--eds-colors-surface-default);
  --eds-accordion-header-text-color: var(--eds-colors-text-default);
  --eds-accordion-header-icon-color: var(--eds-colors-icon-light);
  --eds-accordion-header-expanded-text-color: var(--eds-colors-text-default);
  --eds-accordion-header-expanded-icon-color: var(--eds-colors-icon-light);
  --eds-accordion-border-properties: var(--eds-stroke-025) solid var(--eds-border-color-light);

  &::part(content) {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);
  }

  &::part(base) {
    height: 100%;
  }

  .user-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--eds-spacing-100);
    flex: 1;

    .info {
      display: flex;
      align-items: center;
      gap: var(--eds-spacing-100);
    }

    eds-icon[name='arrowDown'] {
      color: var(--eds-colors-icon-light);
    }
  }

  .greeting {
    padding-block-end: var(--eds-spacing-400);
    border-block-end: var(--eds-stroke-025) solid var(--eds-border-color-light);
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);
    overflow-y: auto;
    flex-shrink: 1;

    &::-webkit-scrollbar {
      display: none;
    }

    .key-functions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--eds-spacing-200);

      eds-text {
        grid-column: span 2;
      }

      .function {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: var(--eds-spacing-100);
        padding: var(--eds-spacing-300) var(--eds-spacing-200);
        background-color: var(--eds-colors-body);
        border-radius: var(--eds-radius-200);
        cursor: pointer;

        eds-icon {
          width: var(--eds-sizing-600);
          height: var(--eds-sizing-600);
        }
      }
    }

    .invoices {
      display: flex;
      flex-direction: column;
      gap: var(--eds-spacing-200);

      .invoices-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: var(--eds-spacing-200);
      }

      .current-amount {
        display: flex;
        flex-direction: column;
        gap: var(--eds-spacing-400);
        border: var(--eds-stroke-025) solid var(--eds-border-color-light);
        border-radius: var(--eds-radius-200);
        padding: var(--eds-spacing-400);

        .amount {
          display: flex;
          align-items: flex-end;
          justify-content: space-between;
          flex-wrap: wrap;
          gap: var(--eds-spacing-400);

          .date {
            display: flex;
            flex-direction: column;
            gap: var(--eds-spacing-100);
          }
        }
      }

      .invoices-list {
        display: flex;
        flex-direction: column;
        gap: var(--eds-spacing-400);
        border: var(--eds-stroke-025) solid var(--eds-border-color-light);
        border-radius: var(--eds-radius-200);
        padding: var(--eds-spacing-400);

        .invoice {
          display: flex;
          flex-direction: column;
          gap: var(--eds-spacing-400);
          border: var(--eds-stroke-025) solid var(--eds-border-color-light);
          border-radius: var(--eds-radius-200);
          padding: var(--eds-spacing-400);
          .amount {
            display: flex;
            align-items: flex-end;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: var(--eds-spacing-400);

            .date {
              display: flex;
              flex-direction: column;
              gap: var(--eds-spacing-100);
            }
          }
        }

        .alert {
          display: flex;
          align-items: center;
          gap: var(--eds-spacing-200);

          eds-icon {
            width: var(--eds-sizing-400);
            height: var(--eds-sizing-400);
          }

          eds-icon,
          eds-text {
            color: var(--eds-colors-danger-default);
          }
        }
      }
    }
  }
}
