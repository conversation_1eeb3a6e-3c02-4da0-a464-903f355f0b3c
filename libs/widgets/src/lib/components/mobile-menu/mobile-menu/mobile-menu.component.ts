import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  OnDestroy,
  NgZone,
  model,
  input,
  inject,
  signal,
} from '@angular/core';
import { User } from '@libs/types';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-mobile-menu',
  templateUrl: './mobile-menu.component.html',
  styleUrls: ['./mobile-menu.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe],
})
export class MobileMenuComponent implements OnDestroy {
  private ngZone = inject(NgZone);

  mobileUserTypeMenu = input<boolean>(true);

  isMenuOpen = signal(false);

  userType = model<User.UserType>(User.UserType.PERSONAL);
  languagePreferences = model<boolean>();

  readonly UserType = User.UserType;

  private handleResize = () => {
    this.ngZone.run(() => {
      this.closeMenu();
    });
  };

  ngOnDestroy(): void {
    this.removeResizeListener();
    document.body.removeAttribute('style');
  }

  private addResizeListener(): void {
    this.ngZone.runOutsideAngular(() => {
      window.addEventListener('resize', this.handleResize);
    });
  }

  private removeResizeListener(): void {
    window.removeEventListener('resize', this.handleResize);
  }

  toggleMenu(): void {
    this.isMenuOpen.update((isOpen) => !isOpen);

    if (this.isMenuOpen()) {
      document.body.style.overflow = 'hidden';
      document.body.style.maxHeight = '100svh';
      this.addResizeListener();
    } else {
      this.removeResizeListener();
    }
  }

  closeMenu(): void {
    if (this.isMenuOpen()) {
      this.isMenuOpen.set(false);
      this.removeResizeListener();
    }
    document.body.removeAttribute('style');
    this.languagePreferences.set(false);
  }

  changeUserType(type: User.UserType): void {
    this.userType.set(type);
  }

  openLanguagePreference(): void {
    this.languagePreferences.set(true);
  }
}
