<div class="base" #base>
  <eds-button
    class="hamburger-menu"
    appearance="subtle"
    iconOnly
    iconLeading="menu"
    (button-click)="toggleMenu()"
  ></eds-button>
  <div class="menu" [class.open]="isMenuOpen()">
    <div class="menu-header">
      <div class="actions">
        <eds-button
          class="language-item"
          appearance="subtle"
          iconOnly
          iconLeading="internet"
          (button-click)="openLanguagePreference()"
        ></eds-button>
        <ng-content select="[searchElement]"></ng-content>
        <eds-button appearance="subtle" iconOnly iconLeading="cancel" (button-click)="closeMenu()"></eds-button>
      </div>
      @if (mobileUserTypeMenu()) {
        <div class="user-type">
          <eds-button
            appearance="subtle"
            size="compact"
            shouldFitContainer
            (button-click)="changeUserType(UserType.PERSONAL)"
            [class.active]="userType() === UserType.PERSONAL"
            >{{ 'personal' | translate }}</eds-button
          >
          <eds-button
            appearance="subtle"
            size="compact"
            shouldFitContainer
            (button-click)="changeUserType(UserType.BUSINESS)"
            [class.active]="userType() === UserType.BUSINESS"
            >{{ 'business' | translate }}</eds-button
          >
        </div>
      }
    </div>
    <div class="language-container">
      <ng-content select="[region]"></ng-content>
    </div>
    <div class="menu-content">
      <div class="navigation">
        <ng-content select="[navigation]"></ng-content>
      </div>
      <div class="login">
        <ng-content select="[login]"></ng-content>
      </div>
    </div>
  </div>
</div>
