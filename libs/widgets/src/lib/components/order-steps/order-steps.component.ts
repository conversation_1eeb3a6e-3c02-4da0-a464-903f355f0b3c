import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { OrderStep } from '../../model/order-step.model';

@Component({
  selector: 'widget-order-steps',
  templateUrl: './order-steps.component.html',
  styleUrl: './order-steps.component.scss',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe],
})
export class OrderStepsComponent {
  orderSteps = input<OrderStep[]>();
}
