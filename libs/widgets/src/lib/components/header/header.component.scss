:host {
  --eds-header-padding: var(--header-padding, var(--eds-spacing-400));
  --eds-header-profile-item-height: var(--header-profile-item-height, var(--eds-sizing-800));
  --eds-header-width: var(--header-width, 100%);
  --eds-header-background-color: var(--header-background-color, var(--eds-colors-surface-level-1));
  --eds-header-border-properties: var(
    --header-border-properties,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-header-content-gap: var(--header-content-gap, var(--eds-spacing-600));
  --eds-header-actions-gap: var(--header-actions-gap, var(--eds-spacing-200));
  --eds-header-container-size: var(--header-container-size, calc(var(--eds-size-multiplier) * 288));
  --eds-header-profile-item-gap: var(--header-profile-item-gap, var(--eds-spacing-200));
  --eds-header-profile-item-padding: var(--header-profile-item-padding, var(--eds-spacing-200) var(--eds-spacing-400));
  --eds-header-profile-item-background-color: var(
    --header-profile-item-background-color,
    var(--eds-colors-surface-default)
  );
  --eds-header-profile-item-text-color: var(--header-profile-item-text-color, var(--eds-colors-text-light));
  --eds-header-profile-item-border-radius: var(--header-profile-item-border-radius, var(--eds-sizing-200));
  --eds-header-profile-item-border: var(
    --header-profile-item-border,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-header-brand-logo-width: var(--header-brand-logo-width, auto);
  --eds-header-brand-logo-height: var(--header-brand-logo-height, calc(var(--eds-size-multiplier) * 7));
  --eds-header-language-item-display: var(--header-language-item-display, flex);

  width: var(--eds-header-width);
}

.base {
  --header-search-width: 0;
  --header-search-overflow: hidden;

  position: relative;
  display: grid;
  place-items: center;
  grid-template-rows: var(--header-language-preference-row) auto auto;
  width: 100%;
  background: var(--eds-header-background-color);
  border-bottom: var(--eds-header-border-properties);

  .language-preference,
  .language-preference-container {
    width: 100%;
    height: 100%;
    overflow: var(--header-language-preference-overflow);
  }
}

.content {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: var(--eds-header-container-size);
  padding: var(--eds-header-padding);
  gap: var(--eds-header-content-gap);

  .actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--eds-header-actions-gap);
    flex: 1;
  }
}

.brand {
  flex-shrink: 0;
  eds-image::part(image) {
    display: block;
    width: var(--eds-header-brand-logo-width);
    height: var(--eds-header-brand-logo-height);
  }
}

eds-link::part(label) {
  color: var(--eds-header-brand-name-text-color);
}

.language-item {
  display: var(--eds-header-language-item-display);
}

@media (min-width: 834px) {
  :host {
    --eds-header-padding: var(--header-padding, var(--eds-spacing-600));
    --eds-header-actions-gap: var(--header-actions-gap, var(--eds-spacing-400));
    --eds-header-profile-item-height: var(--header-profile-item-height, calc(var(--eds-size-multiplier) * 10));
  }

  .actions {
    position: relative;
    gap: var(--eds-header-actions-gap);
  }
}

@media (min-width: 1440px) {
  .brand eds-image::part(image) {
    --eds-header-brand-logo-height: var(--header-brand-logo-height, var(--eds-sizing-800));
  }
}
