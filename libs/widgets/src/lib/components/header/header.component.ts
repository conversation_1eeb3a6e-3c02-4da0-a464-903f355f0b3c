import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, model } from '@angular/core';

@Component({
  selector: 'widget-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HeaderComponent {
  loggedIn = input(false);
  name = input('');
  logoHref = input('');
  logoSrc = input('');
  brandName = input('');
  languagePreferences = model<boolean>();

  openLanguagePreference(): void {
    this.languagePreferences.set(true);
  }
}
