import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  input,
  output,
  signal,
} from '@angular/core';

@Component({
  selector: 'widget-item-delete-action',
  templateUrl: './item-delete-action.component.html',
  styleUrls: ['./item-delete-action.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ItemDeleteActionComponent {
  deleteConfirmationText = input<string>();
  triggerCancel = input<boolean>(false);
  buttonText = input<string>();

  deleteItem = output();
  onDeleteAction = output();
  onCancelDeleteAction = output();

  deleteActionTriggered = signal(false);

  constructor() {
    effect(() => {
      if (this.triggerCancel()) {
        this.deleteActionTriggered.set(false);
      }
    });
  }

  deleteAction() {
    this.deleteActionTriggered.set(true);
    this.onDeleteAction.emit();
  }

  confirmDeleteAction() {
    this.deleteActionTriggered.set(false);
    this.deleteItem.emit();
  }

  cancelDeleteAction() {
    this.deleteActionTriggered.set(false);
    this.onCancelDeleteAction.emit();
  }
}
