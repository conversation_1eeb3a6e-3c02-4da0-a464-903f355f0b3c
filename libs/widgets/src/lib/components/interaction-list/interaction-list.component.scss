:host {
  --eds-action-list-display: var(--action-list-display, flex);
  --eds-action-list-align-items: var(--action-list-align-items, center);
  --eds-action-list-justify-content: var(--action-list-justify-content, space-between);

  --eds-action-list-padding: var(--action-list-padding, var(--eds-spacing-300) var(--eds-spacing-400));
  --eds-action-item-icon-color: var(--action-item-icon-color, var(--eds-colors-text-light));
  --eds-action-list-icon-size: var(--action-list-icon-size, var(--eds-sizing-600));
  --eds-action-list-border-properties: var(
    --action-list-border-properties,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-action-list-border-radius: var(--action-list-border-radius, var(--eds-radius-300));

  --eds-action-element-gap: var(--action-element-gap, var(--eds-spacing-200));
  --eds-action-element-label-gap: var(--action-element-label-gap, var(--eds-spacing-200));
  --eds-action-border-properties: var(
    --action-border-properties,
    var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-action-border-width: var(--action-border-width, 0 0 var(--eds-stroke-025) 0);

  --eds-action-list-show-more-button-background-color: var(
    --action-list-show-more-button-background-color,
    var(--eds-border-color-default)
  );
  --eds-action-list-show-more-button-padding: var(--action-list-show-more-button-padding, var(--eds-spacing-300) 0);
  --eds-action-list-show-more-button-gap: var(--action-list-show-more-button-gap, var(--eds-spacing-100));

  --eds-action-item-text-color: var(--action-item-text-color, var(--eds-colors-text-default));
  --eds-action-item-text-decoration: var(--action-item-text-decoration, none);

  --eds-action-show-more-btn-icon-rotate: var(--action-show-more-icon-rotate, var(--eds-rotate-icon-base));
  --eds-action-show-more-btn-icon-size: var(--action-show-more-btn-icon-size, var(--eds-sizing-400));

  --eds-action-show-more-btn-color: var(--action-show-more-btn-color, var(--eds-colors-text-dark));
  --eds-action-show-more-btn-decoration: var(--action-show-more-btn-decoration, none);

  --eds-action-item-elembefore-color: var(--action-item-elembefore-color, var(--eds-colors-secondary-default));
}

.action-list {
  display: flex;
  flex-direction: column;
  border: var(--eds-action-list-border-properties);
  border-radius: var(--eds-action-list-border-radius);
  overflow: hidden;
  cursor: pointer;
}

.action {
  display: var(--eds-action-list-display);
  align-items: var(--eds-action-list-align-items);
  justify-content: var(--eds-action-list-justify-content);
  text-decoration: none;
  gap: var(--eds-action-element-gap);
  padding: var(--eds-action-list-padding);
  border: var(--eds-action-border-properties);
  border-width: var(--eds-action-border-width);
  transition: var(--eds-transition-property-base) var(--eds-transition-duration-base)
    var(--eds-transition-timing-function-base);

  &:hover {
    background-color: var(--eds-colors-primary-lighter);
  }

  &:active {
    background-color: var(--eds-colors-primary-dark);

    --eds-action-item-icon-color: var(--eds-white);
    --eds-action-item-text-color: var(--eds-white);
  }
}

.action:last-of-type {
  border-bottom: none;
}

.action eds-text::part(base) {
  color: var(--eds-action-item-text-color);
  text-decoration: var(--eds-action-item-text-decoration);
}

.action eds-icon {
  color: var(--eds-action-item-icon-color);
  width: var(--eds-action-list-icon-size);
  height: var(--eds-action-list-icon-size);
  flex-shrink: 0;
}

[part='label'] {
  display: flex;
  align-items: center;
  gap: var(--eds-action-element-label-gap);
  color: var(--eds-action-item-text-color);
}

[part='elemBefore'] {
  color: var(--eds-action-item-elembefore-color);
}

.action-show-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--eds-action-list-show-more-button-gap);
  padding: var(--eds-action-list-show-more-button-padding);
  background: var(--eds-action-list-show-more-button-background-color);
  cursor: pointer;
}

.action-show-more eds-text::part(base) {
  color: var(--eds-action-show-more-btn-color);
  text-decoration: var(--eds-action-show-more-btn-decoration);
}

.action-show-more eds-icon {
  width: var(--eds-action-list-icon-size);
  height: var(--eds-action-list-icon-size);
}

.action-show-more.open eds-icon {
  transform: var(--eds-action-show-more-btn-icon-rotate);
}
