import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-terms-and-conditions',
  templateUrl: './terms-and-conditions.component.html',
  styleUrls: ['./terms-and-conditions.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe],
})
export class TermsAndConditionsComponent {
  text = input<string>('');
  escapedText = computed(() => {
    return this.text().replace(/\r?\n/g, '<br>');
  });

  onAcceptClick = output<void>();
}
