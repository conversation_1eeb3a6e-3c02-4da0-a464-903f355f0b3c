:host {
  --eds-offer-card-content-direction: var(--offer-card-content-direction, column);
  --eds-offer-card-border-radius: var(--offer-card-border-radius, var(--eds-radius-600));
  --eds-offer-card-border-properties: var(
    --offer-card-border-properties,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-offer-card-content-flex-properties: var(--offer-card-content-flex-properties, 0 0 50%);
  --eds-offer-card-image-flex-properties: var(--offer-card-image-flex-properties, 0 0 50%);
  --eds-offer-card-image-height: var(--offer-card-image-height, 147px);
  --eds-offer-card-image-overflow: var(--offer-card-image-overflow, hidden);
  --eds-offer-card-content-gap: var(--offer-card-content-gap, var(--eds-spacing-200));
  --eds-offer-card-content-padding: var(--offer-card-content-padding, var(--eds-spacing-400));
  --eds-offer-card-heading-line-clamp: var(--offer-card-heading-line-clamp, 1);
  --eds-offer-card-link-spacing: var(--offer-card-link-spacing, var(--eds-spacing-200));
  --eds-offer-card-content-background-color: var(
    --offer-card-content-background-color,
    var(--eds-colors-surface-default)
  );
}

@media (min-width: 834px) {
  :host {
    --eds-offer-card-image-height: var(--offer-card-image-height, 0);
    --eds-offer-card-content-direction: var(--offer-card-content-direction, row);
    --eds-offer-card-image-overflow: var(--offer-card-image-overflow, visible);
  }
}

.base {
  display: flex;
  flex-direction: var(--eds-offer-card-content-direction);
  overflow: hidden;
  border-radius: var(--eds-offer-card-border-radius);
  border: var(--eds-offer-card-border-properties);
}

.image {
  background-image: conic-gradient(
    from 180deg at 0% 100%,
    var(--eds-color-neutral-200) 0deg,
    var(--eds-color-neutral-900) 360deg
  );
  max-height: var(--eds-offer-card-image-height);
  overflow: var(--eds-offer-card-image-overflow);
  flex: var(--eds-offer-card-image-flex-properties);
  flex-shrink: 0;
}

.content {
  display: flex;
  flex-direction: column;
  gap: var(--eds-offer-card-content-gap);
  padding: var(--eds-offer-card-content-padding);
  flex: var(--eds-offer-card-content-flex-properties);
  background-color: var(--eds-offer-card-content-background-color);
}

.title {
  -webkit-line-clamp: var(--eds-offer-card-heading-line-clamp);
  -webkit-box-orient: vertical;
  line-clamp: var(--eds-offer-card-heading-line-clamp);
  display: -webkit-box;
  overflow: hidden;
}
