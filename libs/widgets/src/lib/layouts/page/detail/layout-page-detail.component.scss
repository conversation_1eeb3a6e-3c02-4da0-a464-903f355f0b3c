:host {
  --eds-page-layout-detail-display: var(--page-layout-detail-display, flex);
  --eds-page-layout-detail-align-items: var(--page-layout-detail-align-items, center);
  --eds-page-layout-detail-flex-direction: var(--page-layout-detail-flex-direction, column);
  --eds-page-layout-detail-padding-bottom: var(
    --page-layout-detail-padding-bottom,
    calc(var(--eds-size-multiplier) * 24)
  );
  --eds-page-layout-detail-min-height: var(--page-layout-detail-min-height, 100svh);
  --eds-page-layout-detail-background-color: var(
    --page-layout-detail-background-color,
    var(--eds-colors-surface-level-1)
  );
  --eds-page-layout-detail-gap: var(--page-layout-detail-gap, var(--eds-spacing-800));

  --eds-page-layout-detail-heading-display: var(--page-layout-detail-heading-display, grid);
  --eds-page-layout-detail-heading-align-items: var(--page-layout-detail-heading-align-items, start);
  --eds-page-layout-detail-heading-grid-template: var(
    --page-layout-detail-heading-grid-template,
    none / minmax(0, calc(var(--eds-size-multiplier) * 138))
  );
  --eds-page-layout-detail-heading-padding: var(--page-layout-detail-heading-padding, 0 var(--eds-spacing-400));
  --eds-page-layout-detail-heading-gap: var(--page-layout-detail-heading-gap, var(--eds-spacing-800));

  --eds-page-layout-detail-header-position: var(--page-layout-detail-header-position, sticky);
  --eds-page-layout-detail-header-z-index: var(--page-layout-detail-header-z-index, 1000);
  --eds-page-layout-detail-header-width: var(--page-layout-detail-header-width, 100%);
  --eds-page-layout-detail-header-top: var(--page-layout-detail-header-top, 0);

  --eds-page-layout-detail-page-heading-content-gap: var(
    --page-layout-detail-page-heading-content-gap,
    var(--eds-spacing-100)
  );
  --eds-page-layout-detail-subheading-color: var(--page-layout-detail-subheading-color, var(--eds-colors-text-dark));
  --eds-page-layout-detail-subheading-font-weight: var(
    --page-layout-detail-subheading-font-weight,
    var(--eds-font-weight-medium)
  );

  --eds-page-layout-detail-content-display: var(--page-layout-detail-content-display, grid);
  --eds-page-layout-detail-content-flex-direction: var(--page-layout-detail-content-flex-direction, column);
  --eds-page-layout-detail-content-gap: var(--page-layout-detail-content-gap, var(--eds-spacing-600));
  --eds-page-layout-detail-content-padding: var(--page-layout-detail-content-padding, 0 var(--eds-spacing-400));
  --eds-page-layout-detail-content-overflow: var(--page-layout-detail-content-overflow, hidden);
  --eds-page-layout-detail-content-grid-template: var(
    --page-layout-detail-content-grid-template,
    none / minmax(0, calc(var(--eds-size-multiplier) * 138))
  );
  --eds-page-layout-detail-content-justify-content: var(--page-layout-detail-content-justify-content, unset);
  --eds-page-layout-detail-content-align-items: var(--page-layout-detail-content-align-items, unset);
  --eds-page-layout-detail-content-justify-items: var(--page-layout-detail-content-justify-items, unset);

  --eds-page-layout-detail-nav-position: var(--page-layout-detail-nav-position, fixed);
  --eds-page-layout-detail-nav-background-color: var(
    --page-layout-detail-nav-background-color,
    var(--eds-colors-surface-level-1)
  );
  --eds-page-layout-detail-nav-padding: var(
    --page-layout-detail-nav-padding,
    var(--eds-spacing-200) var(--eds-spacing-400)
  );
  --eds-page-layout-detail-nav-border-top: var(
    --page-layout-detail-nav-border-top,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
}

@media (max-width: 834px) {
  :host {
    --eds-page-layout-detail-gap: var(--page-layout-detail-gap, var(--eds-spacing-600));
    --eds-page-layout-detail-heading-gap: var(--page-layout-detail-heading-gap, var(--eds-spacing-600));
  }
}

@media (min-width: 1440px) {
  :host {
    --eds-page-layout-detail-heading-grid-template: var(
      --page-layout-default-heading-grid-template,
      none / 1fr calc(var(--eds-size-multiplier) * 138) 1fr
    );
  }
}

.base {
  position: relative;
  display: var(--eds-page-layout-detail-display);
  align-items: var(--eds-page-layout-detail-align-items);
  flex-direction: var(--eds-page-layout-detail-flex-direction);
  background-color: var(--eds-page-layout-detail-background-color);
  min-height: var(--eds-page-layout-detail-min-height);
  padding-bottom: var(--eds-page-layout-detail-padding-bottom);
  gap: var(--eds-page-layout-detail-gap);
}

.header {
  position: var(--eds-page-layout-detail-header-position);
  top: var(--eds-page-layout-detail-header-top);
  z-index: var(--eds-page-layout-detail-header-z-index);
  width: var(--eds-page-layout-detail-header-width);
}

.heading {
  display: var(--eds-page-layout-detail-heading-display);
  align-items: var(--eds-page-layout-detail-heading-align-items);
  grid-template: var(--eds-page-layout-detail-heading-grid-template);
  gap: var(--eds-page-layout-detail-heading-gap);
  padding: var(--eds-page-layout-detail-heading-padding);
}

eds-page-heading {
  --eds-page-heading-subheading-color: var(--eds-page-layout-detail-subheading-color);
  --eds-page-heading-subheading-font-weight: var(--eds-page-layout-detail-subheading-font-weight);
  --eds-page-heading-content-gap: var(--eds-page-layout-detail-page-heading-content-gap);
}

.content-wrapper {
  display: var(--eds-page-layout-detail-content-display);
  flex-direction: var(--eds-page-layout-detail-content-flex-direction);
  grid-template: var(--eds-page-layout-detail-content-grid-template);
  justify-content: var(--eds-page-layout-detail-content-justify-content);
  align-items: var(--eds-page-layout-detail-content-align-items);
  justify-items: var(--eds-page-layout-detail-content-justify-items);
  gap: var(--eds-page-layout-detail-content-gap);
  overflow: var(--eds-page-layout-detail-content-overflow);
  padding: var(--eds-page-layout-detail-content-padding);
}

.navigation {
  position: var(--eds-page-layout-detail-nav-position);
  inset: auto 0 0 0;
  z-index: 900;
  background-color: var(--eds-page-layout-detail-nav-background-color);
  padding: var(--eds-page-layout-detail-nav-padding);
  border-top: var(--eds-page-layout-detail-nav-border-top);
}

@media (min-width: 1440px) {
  .navigation {
    display: none;
  }
}
