:host {
  --eds-page-layout-membership-display: var(--page-layout-membership-display, flex);
  --eds-page-layout-membership-align-items: var(--page-layout-membership-align-items, center);
  --eds-page-layout-membership-flex-direction: var(--page-layout-membership-flex-direction, column);
  --eds-page-layout-membership-min-height: var(--page-layout-membership-min-height, 100svh);
  --eds-page-layout-membership-background-color: var(
    --page-layout-membership-background-color,
    var(--eds-colors-surface-level-1)
  );

  --eds-page-layout-membership-header-position: var(--page-layout-membership-header-position, static);
  --eds-page-layout-membership-header-z-index: var(--page-layout-membership-header-z-index, 1000);
  --eds-page-layout-membership-header-width: var(--page-layout-membership-header-width, 100%);
  --eds-page-layout-membership-header-top: var(--page-layout-membership-header-top, 0);

  --eds-page-layout-membership-wrapper-display: var(--page-layout-membership-wrapper-display, grid);
  --eds-page-layout-membership-wrapper-grid-template: var(
    --page-layout-membership-wrapper-grid-template,
    none / minmax(0, calc(var(--eds-size-multiplier) * 100))
  );
  --eds-page-layout-membership-wrapper-align-items: var(--page-layout-membership-wrapper-align-items, center);
  --eds-page-layout-membership-wrapper-justify-content: var(--page-layout-membership-wrapper-justify-content, center);
  --eds-page-layout-membership-wrapper-padding: var(--page-layout-membership-wrapper-padding, var(--eds-spacing-400));

  --eds-page-layout-membership-header-container-size: var(
    --page-layout-membership-header-container-size,
    calc(var(--eds-size-multiplier) * 288)
  );
}

@media (min-width: 834px) {
  :host {
    --eds-page-layout-membership-wrapper-padding: var(--page-layout-membership-wrapper-padding, var(--eds-spacing-600));
  }
}

@media (min-width: 1440px) {
}

.base {
  position: relative;
  display: var(--eds-page-layout-membership-display);
  align-items: var(--eds-page-layout-membership-align-items);
  flex-direction: var(--eds-page-layout-membership-flex-direction);
  background-color: var(--eds-page-layout-membership-background-color);
  min-height: var(--eds-page-layout-membership-min-height);
}

.header {
  position: var(--eds-page-layout-membership-header-position);
  top: var(--eds-page-layout-membership-header-top);
  z-index: var(--eds-page-layout-membership-header-z-index);
  width: var(--eds-page-layout-membership-header-width);
}

.page-layout-heading-wrapper {
  max-width: var(--eds-page-layout-membership-header-container-size);
  width: 100%;
  padding-block: var(--eds-spacing-400);
}

.layout-wrapper {
  display: var(--eds-page-layout-membership-wrapper-display);
  align-items: var(--eds-page-layout-membership-wrapper-align-items);
  padding: var(--eds-page-layout-membership-wrapper-padding);
  grid-template: var(--eds-page-layout-membership-wrapper-grid-template);
  flex: 1;
}
