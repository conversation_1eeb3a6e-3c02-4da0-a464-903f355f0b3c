:host {
  --eds-cms-content-layout-default-display: var(--cms-content-layout-default-display, grid);
  --eds-cms-content-layout-default-overflow: var(--cms-content-layout-default-overflow, hidden);
  --eds-cms-content-layout-default-grid-template-columns: var(
    --cms-content-layout-default-grid-template-columns,
    minmax(0, 100%)
  );
}
.base {
  display: var(--eds-cms-content-layout-default-display);
  grid-template-columns: var(--eds-cms-content-layout-default-grid-template-columns);
  overflow: var(--eds-cms-content-layout-default-overflow);
  container-name: cms-content-default;
  container-type: inline-size;

  ::ng-deep section {
    display: grid;
    grid-template-columns: minmax(0, calc(var(--eds-size-multiplier) * 288));
    justify-content: center;
    padding: calc(var(--eds-size-multiplier) * 8) var(--eds-spacing-400);
    grid-column: 1 / -1;

    @media (min-width: 1440px) {
      padding: calc(var(--eds-size-multiplier) * 16);
    }

    @container cms-content-default (max-width: 520px) {
      padding: var(--eds-spacing-400);
    }
  }

  ::ng-deep section:nth-child(2n) {
    background-color: var(--eds-colors-body);
  }
}
