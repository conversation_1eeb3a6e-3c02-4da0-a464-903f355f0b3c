import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  OnInit,
  output,
  ChangeDetectorRef,
  inject,
} from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  createAl<PERSON>beticMask,
  createIbanMask,
  DEFAULT_IBAN_COUNTRY_CODE,
  FormFieldComponent,
  IBAN_CONFIGURATIONS,
  IBAN_COUNTRY_CODE_LENGTH,
  TranslatePipe,
  BSSValidators,
} from '@libs/plugins';
import { BankAccountFormFields } from './bank-account-form.type';
import { select } from '@ngxs/store';
import { LoaderState } from '@libs/core';

@Component({
  selector: 'widget-bank-account-form',
  templateUrl: './bank-account-form.component.html',
  styleUrls: ['./bank-account-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, TranslatePipe, FormFieldComponent],
})
export class BankAccountFormComponent implements OnInit {
  private cdr = inject(ChangeDetectorRef);

  addBankAccount = output<BankAccountFormFields>();
  loading = select(LoaderState.getLoading);
  form = new FormGroup({
    nameOnBankAccount: new FormControl('', [Validators.required]),
    bankName: new FormControl('', [Validators.required]),
    iban: new FormControl('', [
      Validators.required,
      BSSValidators.regex(IBAN_CONFIGURATIONS.TR.regex, 'invalidFormat'),
    ]),
  });

  ibanMask = createIbanMask();
  alphabeticMask = createAlphabeticMask();

  submit(): void {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      this.addBankAccount.emit(this.form.value as BankAccountFormFields);
    }
  }

  ngOnInit(): void {
    this.form.get('iban')?.valueChanges.subscribe((value: string | null) => {
      const rawValue = value || '';
      let effectiveCountryCode = DEFAULT_IBAN_COUNTRY_CODE;

      if (rawValue.length >= IBAN_COUNTRY_CODE_LENGTH) {
        const potentialCode = rawValue.slice(0, IBAN_COUNTRY_CODE_LENGTH).toUpperCase();
        if (Object.prototype.hasOwnProperty.call(IBAN_CONFIGURATIONS, potentialCode)) {
          effectiveCountryCode = potentialCode as keyof typeof IBAN_CONFIGURATIONS;
        }
      }
      this.applyIbanSettings(effectiveCountryCode);
    });
  }

  private applyIbanSettings(countryCode: keyof typeof IBAN_CONFIGURATIONS): void {
    const ibanConfig = IBAN_CONFIGURATIONS[countryCode] || IBAN_CONFIGURATIONS.TR;

    this.ibanMask = createIbanMask(countryCode);

    const ibanControl = this.form.get('iban');
    if (ibanControl) {
      ibanControl.setValidators([Validators.required, BSSValidators.regex(ibanConfig.regex, 'invalidFormat')]);
      ibanControl.updateValueAndValidity({ emitEvent: false });
    }
    this.cdr.markForCheck();
  }
}
