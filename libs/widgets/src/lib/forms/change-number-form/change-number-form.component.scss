.base {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);

  @media (min-width: 834px) {
    gap: var(--eds-spacing-600);
  }
}

.search-number {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
}

.search-number-title {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-200);
}

.search-number-input {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--eds-spacing-200);
}

.available-number {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);

  eds-button {
    display: flex;

    @media (min-width: 834px) {
      display: none;
    }
  }
}

.available-number-text {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  row-gap: var(--eds-spacing-200);

  eds-button {
    display: none;

    @media (min-width: 834px) {
      display: flex;
    }
  }
}

.available-number-input::part(items) {
  display: grid;
  gap: var(--eds-spacing-200);

  @media (min-width: 834px) {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--eds-spacing-400);
  }
}

eds-radio {
  border-radius: var(--radius-radius-200, 8px);
  border: 1px solid var(--colors-border-default, #dedeed);
  background: var(--colors-surface-default, #fff);
  padding: var(--spacing-space-300, 12px) var(--spacing-space-400, 16px) var(--spacing-space-300, 12px)
    var(--spacing-space-300, 12px);
}

.available-number-icon {
  color: var(--blue-500, #236fc2);
  font-feature-settings:
    'liga' off,
    'clig' off;
  width: 16px;
  height: 16px;
}
.save-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-space-0, 0px);
  align-self: stretch;
}

.success-icon {
  width: 10px;
  height: 10px;
  color: var(--eds-color-green-500, #0d8a52);
  font-size: 10px;
}

.fail-icon {
  width: 10px;
  height: 10px;
  color: var(--eds-color-red-500, #df1b1b);
  font-size: 10px;
}

.freeTextSection {
  display: flex;
  align-content: center;
}

.icon-column {
  flex-shrink: 0;
  width: 24px;
}

.form {
  width: 100%;
  padding-right: 5px;
}

.error {
  display: flex;
  align-items: center;
  gap: var(--eds-spacing-100);
}

.error-message {
  --eds-text-color: var(--eds-colors-danger-default);
}

.error-icon {
  width: var(--eds-sizing-400);
  height: var(--eds-sizing-400);
  flex-shrink: 0;
  color: var(--eds-colors-danger-default);
}
