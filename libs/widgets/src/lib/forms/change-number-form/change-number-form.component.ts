import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  input,
  model,
  output,
  signal,
} from '@angular/core';
import { ChangeNumberForm, FreeTextNumberForm, Radio, SelectOption } from '../../model';
import { createPhoneNumberMask, FormFieldComponent, TranslatePipe, TranslateService } from '@libs/plugins';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { injectDestroy, RadioGroupDirective, toObservable } from '@libs/core';
import { ResultCode, SearchMsisdnRequest, ValidateSimCard } from '@libs/types';
import { debounceTime, takeUntil } from 'rxjs';
import { Checkbox } from '@eds/components';
import { CurrentState, LovSearchMsisdnAction, LovState, QuoteState } from '@libs/bss';
import { select, Store } from '@ngxs/store';

@Component({
  selector: 'widget-change-number-form',
  templateUrl: './change-number-form.component.html',
  styleUrls: ['./change-number-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, FormsModule, RadioGroupDirective, ReactiveFormsModule, FormFieldComponent],
})
export class ChangeNumberFormComponent {
  private store = inject(Store);
  private translateService = inject(TranslateService);
  private destroy$ = injectDestroy();
  nxxDisabled = signal<boolean>(true);

  phoneNumberMask = createPhoneNumberMask();

  form = new FormGroup({
    phoneNumber: new FormControl('', [Validators.minLength(10)]),
    searchValue: new FormControl(''),
  });

  changeNumberForm = signal<ChangeNumberForm>({ npa: '', nxx: '', number: '' });
  freeTextNumberForm = signal<FreeTextNumberForm>({ number: '', freeTextInput: false });

  npaItems = input<SelectOption[]>([]);
  nxxItems = input<SelectOption[]>([]);
  msisdnOptions = input<Radio[]>([]);

  npaSelectClick = output<string>();
  searchNumber = output<ChangeNumberForm>();
  searchFreeTextNumber = output<FreeTextNumberForm>();
  msisdnSelectClick = output<string>();
  validateError = signal<string>('');
  notSuitableMsisdn = signal(false);

  protected searchValue = signal<Event>(null);
  protected freeTextSearchValue = signal<Event>(null);
  showFreeTextInput = signal(false);
  protected searchValue$ = toObservable(this.searchValue);
  protected freeTextSearchValue$ = toObservable(this.freeTextSearchValue);
  validateResponse: ValidateSimCard.Response;
  msisdnList = select(LovState.misdnList);

  constructor() {
    effect(() => {
      this.validateError.set('');

      if (this.nxxItems()?.length) {
        this.nxxDisabled.set(false);
      }

      this.setFirstNpaNpx();
    });
    this.searchValue$.pipe(debounceTime(400)).subscribe((value) => {
      if (!value) {
        return;
      }
      this.filterNumbers(value);
    });
    this.freeTextSearchValue$.pipe(debounceTime(400)).subscribe((value) => {
      if (!value) {
        return;
      }
      this.searchFreeNumbers(value);
    });
    this.store
      .select(QuoteState.validateSimCardResponse)
      .pipe(takeUntil(this.destroy$))
      .subscribe((response) => {
        this.validateResponse = response?.data;

        if (response?.operationResult?.resultCode === ResultCode.Error) {
          if (!this.showFreeTextInput()) {
            this.getValidateErrorInfo(response?.operationResult?.operationResultCode);
          } else {
            this.form.controls.phoneNumber.setErrors({ [response?.operationResult?.operationResultCode]: true });
          }
        }

        if (this.showFreeTextInput() && response?.operationResult?.resultCode === ResultCode.Success) {
          const request = {
            capacityDemandAmount: 1,
            pattern: this.freeTextNumberForm().number.slice(0, 10),
            customerId: this.store.selectSnapshot(CurrentState.customerId),
            resourceValueOffset: 0,
          };

          this.store.dispatch(new LovSearchMsisdnAction(request)).subscribe({
            next: () => {
              this.notSuitableMsisdn.set(false);
            },
            error: (err) => {
              this.notSuitableMsisdn.set(true);
              this.form.controls.phoneNumber.setErrors({ [err?.error?.code ?? err?.error?.status]: true });
              this.form.markAllAsTouched();
            },
          });
        }

        if (response?.data?.state === 'done') {
          this.numberSelect.update(() => this.freeTextNumberForm().number);
          this.msisdnSelectClick.emit(this.freeTextNumberForm().number);
        }
      });
  }

  onNpaSelected = output<string>();
  onNxxSelected = output<SearchMsisdnRequest>();
  numberSelect = model<string>();
  save = output<string>();

  setFirstNpaNpx() {
    if (!this.changeNumberForm().npa && !this.changeNumberForm().nxx) {
      this.changeNumberForm.update((value) => ({
        ...value,
        npa: this.store.selectSnapshot(LovState.npaItems)?.firsItem?.val,
        nxx: this.store.selectSnapshot(LovState.nxxItems)?.firsItem?.val,
        number: '',
      }));
    }
  }

  selectNpa(event: Event) {
    const value = (event.target as HTMLInputElement).value;
    this.changeNumberForm.set({ ...this.changeNumberForm(), npa: value, nxx: null });
    this.npaSelectClick.emit(this.changeNumberForm().npa);
  }

  selectNxx(event: Event) {
    const value = (event.target as HTMLInputElement).value;
    this.changeNumberForm.set({ ...this.changeNumberForm(), nxx: value });
    this.searchNumber.emit(this.changeNumberForm());
  }

  showFreeTextInputMethod(event: Event) {
    this.showFreeTextInput.set((event.target as Checkbox).checked);
    this.freeTextNumberForm.set({ number: '', freeTextInput: this.showFreeTextInput() });
    this.numberSelect.update(() => null);
  }

  filterNumbers(event: Event) {
    const value = (event?.target as HTMLInputElement)?.value || '';
    this.changeNumberForm.set({ ...this.changeNumberForm(), number: value });
    this.searchNumber.emit(this.changeNumberForm());
  }

  searchFreeNumbers(event: Event) {
    const value = (event?.target as HTMLInputElement)?.value.replace(/\D/g, '').slice(0, 10) || '';
    if (value.length > 9) {
      this.freeTextNumberForm.set({ ...this.freeTextNumberForm(), number: value });
      this.form.patchValue({ phoneNumber: value });
      this.searchFreeTextNumber.emit(this.freeTextNumberForm());
    } else {
      this.validateResponse = null;
      this.numberSelect.update(() => null);
      this.msisdnSelectClick.emit(null);
    }
  }

  otherNumbers() {
    this.searchNumber.emit(this.changeNumberForm());
  }

  selectMsisdn(event: Event) {
    const value = (event.target as HTMLInputElement).value;
    this.msisdnSelectClick.emit(value);
  }

  getValidateErrorInfo(errorCode: string) {
    return this.validateError.set(this.translateService.translate(`error.${errorCode}`));
  }

  checkMsisdnSuitableSave() {
    if (this.showFreeTextInput()) {
      return this.notSuitableMsisdn() || !this.freeTextNumberForm().number?.length;
    }

    return !this.numberSelect()?.length;
  }

  onSave() {
    this.save.emit(this.numberSelect());
  }
}
