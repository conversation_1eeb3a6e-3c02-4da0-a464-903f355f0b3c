<div class="base">
  <div class="search-number">
    <div class="search-number-title">
      <eds-heading size="sm" [text]="'searchNumber' | translate"></eds-heading>
      <eds-text [text]="'searchNumberDescription' | translate" as="p" size="lg" weight="regular"></eds-text>
    </div>

    @if (freeTextNumberForm().freeTextInput) {
      <div class="freeTextSection">
        <form [formGroup]="form" class="form">
          <widget-form-field
            class="phonenumber"
            formControlName="phoneNumber"
            [placeholder]="'phoneNumberPlaceholder' | translate"
            [options]="{ type: 'tel' }"
            [mask]="phoneNumberMask"
            (input)="freeTextSearchValue.set($event)"
          >
          </widget-form-field>
        </form>
        <div class="icon-column">
          @if (
            this.validateResponse?.state === 'done' && this.form.value.phoneNumber.length > 9 && this.validateResponse
          ) {
            <eds-icon name="information-circle-icon" class="success-icon" size="sm"></eds-icon>
          } @else if (
            this.freeTextNumberForm().number.length > 9 &&
            this.form.value.phoneNumber.length > 9 &&
            this.validateResponse
          ) {
            <eds-icon name="cancel" class="fail-icon" size="sm"></eds-icon>
          }
        </div>
      </div>
    } @else {
      <div class="search-number-input-container">
        <div class="search-number-input">
          <eds-select
            id="npa"
            name="npa"
            [value]="changeNumberForm().npa"
            appearance="default"
            (option-selected)="selectNpa($event)"
          >
            @for (item of npaItems(); track item.value) {
              <eds-select-option
                [label]="item.label"
                [value]="item.value"
                [name]="item.name"
                [isSelected]="item.isSelected"
                [isDisabled]="item.isDisabled"
              ></eds-select-option>
            }
          </eds-select>

          <eds-select
            id="nxx"
            name="nxx"
            [isDisabled]="nxxDisabled()"
            [value]="changeNumberForm().nxx"
            appearance="default"
            (option-selected)="selectNxx($event)"
          >
            @for (item of nxxItems(); track item.value) {
              <eds-select-option
                [label]="item.label"
                [value]="item.value"
                [name]="item.name"
                [isSelected]="item.isSelected"
                [isDisabled]="item.isDisabled"
              ></eds-select-option>
            }
          </eds-select>
          <form [formGroup]="form" class="form">
            <widget-form-field
              maxlength="4"
              id="filter"
              mask="0000"
              [options]="{
                iconLeading: 'search',
              }"
              formControlName="searchValue"
              (input)="searchValue.set($event)"
            >
            </widget-form-field>
          </form>
        </div>
        @if (validateError()) {
          <div class="error">
            <eds-icon class="error-icon" name="alertCircle"></eds-icon>
            <eds-text class="error-message" as="p" [text]="validateError()" weight="regular"></eds-text>
          </div>
        }
      </div>
    }
    <eds-checkbox
      id="freeTextInputShow"
      name="freeTextInputShow"
      [value]="changeNumberForm().freeTextInput"
      (change)="showFreeTextInputMethod($event)"
    >
      <label for="freeTextInputShow">{{ 'iWantToEnterMyOwnNumber' | translate }}</label>
    </eds-checkbox>
  </div>

  @if (msisdnOptions()?.length && !showFreeTextInput()) {
    <div class="available-number">
      <div class="available-number-text">
        <eds-heading size="sm" [text]="'availableNumber' | translate"></eds-heading>

        <eds-button (button-click)="otherNumbers()" appearance="link" icontrailing="reloadArrow">
          {{ 'otherNumbers' | translate }}
        </eds-button>
      </div>

      <eds-radio-group [(ngModel)]="numberSelect" class="available-number-input" (radio-change)="selectMsisdn($event)">
        @for (item of msisdnOptions(); track item.value) {
          <eds-radio [id]="'option-' + item.id" [value]="item.value">
            <label [for]="'option-' + item.id">{{ item.label }}</label>
          </eds-radio>
        }
      </eds-radio-group>
      <eds-button (button-click)="otherNumbers()" appearance="link" shouldFitContainer icontrailing="reloadArrow">
        {{ 'otherNumbers' | translate }}
      </eds-button>
    </div>
  }

  <div class="save-container">
    <eds-button (button-click)="onSave()" appearance="secondary" size="default" [disabled]="checkMsisdnSuitableSave()">
      {{ 'save' | translate }}
    </eds-button>
  </div>
</div>
