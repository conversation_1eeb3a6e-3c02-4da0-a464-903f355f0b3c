<form [formGroup]="editCustomerForm()" class="form">
  <widget-form-field
    [label]="'customerId' | translate"
    formControlName="customerId"
    [isDisabled]="true"
  ></widget-form-field>

  <widget-form-field
    [label]="'userName' | translate"
    formControlName="userName"
    [isDisabled]="true"
  ></widget-form-field>

  <widget-form-field
    [label]="'firstName' | translate"
    [placeholder]="'John'"
    formControlName="firstName"
    [mask]="alphabeticMask"
  ></widget-form-field>

  <widget-form-field
    [label]="'lastName' | translate"
    [placeholder]="'Doe'"
    formControlName="lastName"
    [mask]="alphabeticMask"
  ></widget-form-field>

  <widget-form-field
    [label]="'birthDate' | translate"
    formControlName="birthDate"
    [placeholder]="'birthDatePlaceholder' | translate"
    [options]="{
      iconTrailing: 'calendar',
      options: {
        minDate: minDate,
        maxDate: maxDate(),
        dateFormat: defaultDateFormat,
        autoClose: true,
      },
    }"
    type="date"
    [mask]="dateMask()"
  ></widget-form-field>

  <widget-form-field
    [label]="'occupation' | translate"
    formControlName="occupation"
    [placeholder]="'occupationPlaceholder' | translate"
    type="select"
    [options]="{ options: occupationTypes() }"
  ></widget-form-field>

  <widget-form-field
    [label]="'language' | translate"
    formControlName="langShortCode"
    [placeholder]="'selectALanguage' | translate"
    type="select"
    [options]="{ options: languageTypes() }"
  ></widget-form-field>

  <div class="actions">
    <eds-button [appearance]="'default'" shouldFitContainer (button-click)="onCancel.emit()">
      {{ 'cancel' | translate }}
    </eds-button>
    <eds-button [appearance]="'primary'" shouldFitContainer (button-click)="submit()">
      {{ 'save' | translate }}
    </eds-button>
  </div>
</form>
