<form class="base" [formGroup]="form">
  <widget-form-field
    [label]="'firstName' | translate"
    [placeholder]="'John'"
    formControlName="firstName"
    [mask]="alphabeticMask"
  ></widget-form-field>

  <widget-form-field
    [label]="'lastName' | translate"
    [placeholder]="'Doe'"
    formControlName="lastName"
    [mask]="alphabeticMask"
  ></widget-form-field>

  <widget-form-field
    [label]="'email' | translate"
    [placeholder]="'<EMAIL>'"
    formControlName="email"
    maxLength="64"
    type="email"
  ></widget-form-field>

  <widget-form-field
    [label]="'password' | translate"
    formControlName="password"
    [placeholder]="'Choose password'"
    type="password"
  >
  </widget-form-field>

  <widget-form-field
    [label]="'birthDate' | translate"
    formControlName="birthDate"
    [placeholder]="'birthDatePlaceholder' | translate"
    [options]="{
      iconTrailing: 'calendar',
      options: {
        minDate: minDate,
        maxDate: maxDate,
        dateFormat: registerDateFormat,
        autoClose: true,
      },
    }"
    type="date"
    [mask]="dateMask"
  >
  </widget-form-field>

  <widget-phonenumber-temp
    [countryOptions]="countryOptions()"
    (formEmitter)="setPhoneNumber($event)"
    [required]="true"
    [errors]="form.get('phoneNumber')?.touched ? form.get('phoneNumber')?.errors : []"
  ></widget-phonenumber-temp>

  <widget-form-field
    [label]="'language' | translate"
    formControlName="langShortCode"
    [placeholder]="'selectALanguage' | translate"
    type="select"
    [options]="{ options: languageTypes() }"
  >
  </widget-form-field>

  <eds-checkbox-group class="optionalAgreements" (click)="togglePrivacySpecification($event)">
    @for (spec of partyPrivacySpecification(); track spec.id; let i = $index) {
      <div class="agreement-container">
        <eds-checkbox
          [id]="'spec-' + spec.id"
          [value]="spec.value"
          [checked]="getCheckboxValue(i)"
          [isRequired]="spec.isRequired"
          [invalid]="
            isFormGroupTouched() &&
            form
              .get('privacySpecs')
              ?.get('spec-' + spec.id)
              ?.get('isChecked')?.errors?.['required']
          "
          (change)="onCheckboxChange($event, i)"
        >
          <div class="agreement-label">
            <label
              [for]="'spec-' + spec.id"
              [class.agreement-link]="spec.isClickable"
              (click)="spec.isClickable && termsOfUseClick(spec.id)"
            >
              {{ spec.name }}
            </label>
            @if (spec.label) {
              <eds-text size="sm" [text]="spec.label"></eds-text>
            }
          </div>
        </eds-checkbox>
        @if (spec.isRequired && !getCheckboxValue(i) && isFormGroupTouched()) {
          <widget-form-field-error-message [errors]="form.errors"></widget-form-field-error-message>
        }
      </div>
    }
  </eds-checkbox-group>

  <eds-button
    class="button"
    appearance="secondary"
    size="default"
    shouldFitContainer="true"
    (button-click)="register()"
  >
    {{ 'createAccount' | translate }}
  </eds-button>

  <div class="haveAnAccount">
    <eds-text class="login" as="p" size="md" [text]="'alreadyHaveAnAccount' | translate"></eds-text>
    <eds-button appearance="link" size="compact" (button-click)="goToLoginClick()">
      {{ 'login' | translate }}
    </eds-button>
  </div>
</form>
