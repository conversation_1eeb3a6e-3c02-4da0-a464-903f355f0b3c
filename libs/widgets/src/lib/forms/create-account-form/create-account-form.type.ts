import { FormControl } from '@angular/forms';

export interface CreateAccountFormFields {
  firstName: FormControl<string>;
  lastName: FormControl<string>;
  birthDate: FormControl<string | null>;
  email: FormControl<string>;
  username: FormControl<string>;
  password: FormControl<string>;
  passwordConfirm: FormControl<string>;
  country: FormControl<string>;
  langShortCode: FormControl<string>;
  phoneNumber: FormControl<string>;
  announcementConsent: FormControl<boolean>;
  newsletterConsent: FormControl<boolean>;
  recaptcha: FormControl<boolean>;
}
