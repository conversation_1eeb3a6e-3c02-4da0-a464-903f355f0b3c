form {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: var(--eds-spacing-400);

  .actions {
    grid-column: span 2;
  }

  widget-form-field {
    grid-column: span 2;
  }

  @media (min-width: 834px) {
    widget-form-field:nth-child(3) {
      grid-column: span 1;
    }

    widget-form-field:nth-child(4) {
      grid-column: span 1;
    }
  }

  .network {
    grid-column: span 2;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--eds-spacing-100);

    eds-image {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--eds-colors-surface-default);
      flex-shrink: 0;
      width: calc(var(--eds-size-multiplier) * 15);
      height: calc(var(--eds-size-multiplier) * 10);
      padding: var(--eds-spacing-100);
      border-radius: var(--eds-radius-200);
      border: 1px solid var(--eds-border-color-default);
    }
  }
}
