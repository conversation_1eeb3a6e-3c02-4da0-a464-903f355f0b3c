import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  OnInit,
  output,
} from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  createAlphabeticMask,
  createCreditCardExpireDateMask,
  createCreditCardMask,
  createCvcMask,
  FormFieldComponent,
  TranslatePipe,
  BSSValidators,
} from '@libs/plugins';
import { CreditCardFormFields } from './credit-card-form.type';
import { select } from '@ngxs/store';
import { LoaderState } from '@libs/core';

export type CardNetwork = 'visa' | 'mastercard' | 'amex' | null;

@Component({
  selector: 'widget-credit-card-form',
  templateUrl: './credit-card-form.component.html',
  styleUrls: ['./credit-card-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, TranslatePipe, FormFieldComponent],
})
export class CreditCardFormComponent implements OnInit {
  private cdr = inject(ChangeDetectorRef);

  addCreditCard = output<CreditCardFormFields>();
  currentNetwork: CardNetwork = null;
  loading = select(LoaderState.getLoading);
  form = new FormGroup({
    creditCardNumber: new FormControl('', [Validators.minLength(16), Validators.required]),
    cardHolder: new FormControl('', [Validators.required]),
    exprDate: new FormControl('', [Validators.required, BSSValidators.creditCardExpireDate]),
    cvc: new FormControl('', [Validators.required, Validators.minLength(3), Validators.maxLength(3)]),
  });

  creditCardMask = createCreditCardMask();
  exprDateMask = createCreditCardExpireDateMask();
  cvcMask = createCvcMask();
  alphabeticMask = createAlphabeticMask();

  ngOnInit() {
    const initialValue = this.form.get('creditCardNumber')?.value;
    if (initialValue) {
      this.currentNetwork = this.identifyCardNetwork(initialValue);
      this.cdr.markForCheck();
    }

    this.form.get('creditCardNumber')?.valueChanges.subscribe((cardNumber) => {
      this.currentNetwork = this.identifyCardNetwork(cardNumber);
      this.cdr.markForCheck();
    });
  }

  identifyCardNetwork(cardNumber: string | null): CardNetwork {
    if (!cardNumber) {
      return null;
    }
    //buranın, apilerin dış sisteme açılana kadar hep 'amex' dönmesi gerekiyor.
    return 'amex';
    const digitsOnly = cardNumber.replace(/\D/g, '');

    if (/^4/.test(digitsOnly)) {
      return 'visa';
    }

    if (/^5[1-5]/.test(digitsOnly) || /^2[2-7][0-9]{2}/.test(digitsOnly)) {
      return 'mastercard';
    }

    if (/^3[47]/.test(digitsOnly)) {
      return 'amex';
    }

    return null;
  }

  submit() {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      this.addCreditCard.emit(this.form.value);
    }
  }
}
