import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output, signal } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { City, Country, SelectOption, State } from '@libs/types';
import { ManageAddressForm, ManageAddressFormValues } from './manage-address-form.type';
import { LowerCasePipe } from '@angular/common';

@Component({
  selector: 'widget-manage-address-form',
  templateUrl: './manage-address-form.component.html',
  styleUrls: ['./manage-address-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, TranslatePipe, FormFieldComponent, LowerCasePipe],
})
export class ManageAddressFormComponent {
  addressForm = input.required<FormGroup<ManageAddressForm>>();
  isPrimaryLocked = input<boolean>(false);
  onSubmit = output<ManageAddressFormValues>();
  onCancel = output<void>();

  countryOptions = input<SelectOption[]>([]);
  stateOptions = input<SelectOption[]>([]);
  cityOptions = input<SelectOption[]>([]);

  countryChangeKey = signal<number>(0);
  onCountrySelected = output<number>();
  onStateSelected = output<number>();

  selectCountry(value: Country.Country) {
    this.addressForm().patchValue({ country: value, state: null, city: null });
    this.countryChangeKey.update((key) => key + 1);
    this.onCountrySelected.emit(value.id);
  }

  selectProvince(value: State.State) {
    this.addressForm().patchValue({ state: value, city: null });
    this.onStateSelected.emit(+value.id);
  }

  selectCity(value: City.City) {
    this.addressForm().patchValue({ city: value });
  }

  submit() {
    this.addressForm().markAllAsTouched();

    if (this.addressForm().valid) {
      this.onSubmit.emit(this.addressForm().getRawValue() as ManageAddressFormValues);
    }
  }

  cancel() {
    this.onCancel.emit();
  }
}
