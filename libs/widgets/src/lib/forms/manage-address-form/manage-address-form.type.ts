import { FormControl } from '@angular/forms';
import { City, Country, State } from '@libs/types';

export interface ManageAddressForm {
  addressLabel: FormControl<string | null>;
  country: FormControl<Country.Country | null>;
  state: FormControl<State.State | null>;
  city: FormControl<City.City | null>;
  addressDescription: FormControl<string | null>;
  postalCode: FormControl<string | null>;
  isPrimary: FormControl<boolean>;
  addressTypeId: FormControl<number | null>;
}

export interface ManageAddressFormValues {
  addressLabel: string;
  country: Country.Country;
  state: State.State;
  city: City.City;
  addressDescription: string;
  postalCode: string;
  isPrimary: boolean;
}
