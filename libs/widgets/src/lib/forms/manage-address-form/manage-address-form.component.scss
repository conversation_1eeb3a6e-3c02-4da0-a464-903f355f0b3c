.form {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);

  .province-city-area {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--eds-spacing-400);
  }

  .checkbox-container {
    display: flex;
    align-items: center;
    gap: var(--eds-spacing-200);

    eds-tooltip {
      &::part(tooltip) {
        width: 16.5rem;
      }

      .tooltip-text {
        color: var(--eds-color-primary-500);
      }
    }

    .tooltip-icon {
      width: var(--eds-sizing-500);
      height: var(--eds-sizing-500);
    }
  }
}
