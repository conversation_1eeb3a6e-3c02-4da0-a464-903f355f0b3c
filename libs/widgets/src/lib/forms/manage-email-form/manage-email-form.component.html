<form [formGroup]="emailForm()" class="form">
  <div class="input-container">
    <widget-form-field
      [label]="'email' | translate"
      [placeholder]="'emailPlaceholder' | translate"
      formControlName="email"
      type="email"
      maxLength="254"
    ></widget-form-field>

    <div class="checkbox-container">
      <eds-checkbox
        id="isPrimary"
        name="isPrimary"
        [attr.checked]="isPrimaryLocked() ? true : emailForm().get('isPrimary')?.value ? 'checked' : null"
        (change)="emailForm().get('isPrimary')?.setValue(!emailForm().get('isPrimary')?.value)"
        [isDisabled]="isPrimaryLocked()"
      >
        <label for="isPrimary">
          {{ 'setAsPrimary' | translate: { communicationType: 'email' | translate | lowercase } }}
        </label>
      </eds-checkbox>
      <eds-tooltip>
        <eds-icon class="tooltip-icon" slot="trigger" name="informationCircle"></eds-icon>
        <div slot="content">
          <eds-text class="tooltip-text" size="sm" [text]="'setAsPrimaryTooltip' | translate"></eds-text>
        </div>
      </eds-tooltip>
    </div>
  </div>

  @if (privacyList().length > 0) {
    <div class="communication-preferences">
      <eds-heading as="h4" size="sm" weight="medium" [text]="'setCommunicationPreferences' | translate"></eds-heading>

      <div class="preferences-container" formArrayName="privacySpecs">
        @for (spec of filteredPrivacyList(); track spec.sortId; let i = $index) {
          <div class="preference-item-container" [formGroupName]="i">
            <div class="preference-label">
              <eds-text size="lg" [text]="spec.name"></eds-text>
              @if (spec.description) {
                <eds-text class="preference-description" size="sm" [text]="spec.description"></eds-text>
              }
            </div>
            <div [class]="{ 'multiple-items': spec.items.length > 1 }" formArrayName="items">
              @for (item of spec.items; track item.partyPrivacyItemId; let j = $index) {
                <div [formGroupName]="j">
                  <eds-toggle [id]="'spec-' + spec.sortId + '-' + j" formControlName="isChecked">
                    @if (spec.items.length > 1) {
                      <eds-text
                        [text]="'notificationChannelTypes.' + item.notificationChannelType | translate"
                      ></eds-text>
                    }
                  </eds-toggle>
                </div>
              }
            </div>
          </div>
        }
      </div>
    </div>
  }
</form>
