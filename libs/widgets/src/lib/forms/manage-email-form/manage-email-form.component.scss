.form {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-800);

  .input-container {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);

    .checkbox-container {
      display: flex;
      align-items: center;
      gap: var(--eds-spacing-200);

      eds-tooltip {
        &::part(tooltip) {
          width: 17rem;
        }

        .tooltip-text {
          color: var(--eds-colors-text-light);
        }
      }

      .tooltip-icon {
        width: var(--eds-sizing-500);
        height: var(--eds-sizing-500);
      }
    }
  }

  .communication-preferences {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);

    .preferences-container {
      display: flex;
      flex-direction: column;
      gap: var(--eds-spacing-500);

      .preference-item-container {
        display: flex;
        align-items: flex-start;
        gap: var(--eds-spacing-600);
      }

      .multiple-items {
        display: flex;
        flex-direction: column;
        gap: var(--eds-spacing-100);
      }

      .preference-label {
        display: flex;
        flex: 1;
        flex-direction: column;

        .preference-description {
          color: var(--eds-colors-text-light);
        }
      }
    }
  }
}
