import { FormArray, FormControl, FormGroup } from '@angular/forms';

export interface ManageEmailForm {
  email: FormControl<string | null>;
  isPrimary: FormControl<boolean | null>;
  privacySpecs: FormArray<
    FormGroup<{
      isChecked: FormControl<boolean | null>;
      partyPrivacySpecId: FormControl<number | null>;
      items: FormArray<
        FormGroup<{
          isChecked: FormControl<boolean | null>;
          partyPrivacyItemId: FormControl<number | null>;
        }>
      >;
    }>
  >;
}

export type ManageEmailFormValues = {
  email?: string | null;
  isPrimary?: boolean | null;
  privacySpecs?: {
    isChecked?: boolean | null;
    partyPrivacySpecId?: number | null;
    items?: {
      isChecked?: boolean | null;
      partyPrivacyItemId?: number | null;
    }[];
  }[];
};
