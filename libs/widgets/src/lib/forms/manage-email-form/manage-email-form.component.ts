import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, effect, input } from '@angular/core';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { ManageEmailForm } from './manage-email-form.type';
import { CapturedPartyPrivacy, Notification } from '@libs/types';
import { LowerCasePipe } from '@angular/common';
import { ToggleDirective } from '@libs/core';

@Component({
  selector: 'widget-manage-email-form',
  templateUrl: './manage-email-form.component.html',
  styleUrls: ['./manage-email-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, TranslatePipe, FormFieldComponent, LowerCasePipe, ToggleDirective],
})
export class ManageEmailFormComponent {
  emailForm = input.required<FormGroup<ManageEmailForm>>();
  privacyList = input.required<CapturedPartyPrivacy.Content[]>();
  isPrimaryLocked = input<boolean>(false);
  isEdit = input<boolean>(false);

  filteredPrivacyList = computed(() => {
    return this.privacyList().map((spec) => {
      const allEmailItems =
        spec.items?.filter((item) => item.notificationChannelType === Notification.NotificationChannelTypes.EMAIL) ??
        [];
      const emailItems = this.isEdit() ? allEmailItems : allEmailItems.slice(0, 1);
      return { ...spec, items: emailItems };
    });
  });

  constructor() {
    effect(() => {
      this.initializeForm();
    });
  }

  get privacySpecsArray(): FormArray {
    return this.emailForm().get('privacySpecs') as FormArray;
  }

  initializeForm(): void {
    this.privacySpecsArray.clear();
    this.filteredPrivacyList().forEach((spec) => {
      const itemsFormArray = new FormArray(
        spec.items.map(
          (item) =>
            new FormGroup({
              isChecked: new FormControl(item?.authorizedFlag ?? false, { nonNullable: true }),
              partyPrivacyItemId: new FormControl(item.partyPrivacyItemId),
            }),
        ),
      );

      if (itemsFormArray.length > 0) {
        this.privacySpecsArray.push(
          new FormGroup({
            partyPrivacySpecId: new FormControl(spec.partyPrivacySpecId),
            items: itemsFormArray,
          }),
        );
      }
    });
  }
}
