import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, effect, input } from '@angular/core';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { ManagePhoneNumberForm } from './manage-phone-number-form.type';
import { CapturedPartyPrivacy, Notification, SelectOption } from '@libs/types';
import { PhoneNumberComponent } from '../../components/phonenumber/phone-number.component';
import { LowerCasePipe } from '@angular/common';
import { ToggleDirective } from '@libs/core';

@Component({
  selector: 'widget-manage-phone-number-form',
  templateUrl: './manage-phone-number-form.component.html',
  styleUrls: ['./manage-phone-number-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    ReactiveFormsModule,
    TranslatePipe,
    FormFieldComponent,
    PhoneNumberComponent,
    LowerCasePipe,
    ToggleDirective,
  ],
})
export class ManagePhoneNumberFormComponent {
  phoneNumberForm = input.required<FormGroup<ManagePhoneNumberForm>>();
  privacyList = input.required<CapturedPartyPrivacy.Content[]>();
  isPrimaryLocked = input<boolean>(false);
  isEdit = input<boolean>(false);
  countryOptionsForPhoneNumber = input<SelectOption[]>([]);
  phoneTypeOptions = input<SelectOption[]>([]);

  filteredPrivacyList = computed(() => {
    return this.privacyList().map((spec) => {
      const allPhoneNumberItems =
        spec.items?.filter((item) => item.contactMediumType === Notification.NotificationChannelTypes.SMS) ?? [];
      const phoneNumberItems = this.isEdit() ? allPhoneNumberItems : allPhoneNumberItems.slice(0, 1);
      return { ...spec, items: phoneNumberItems };
    });
  });

  constructor() {
    effect(() => {
      if (this.isEdit() && this.phoneTypeOptions().length > 0) {
        const phoneType = this.phoneNumberForm().get('phoneType')?.value;
        if (phoneType) {
          const phoneTypeToSet = this.phoneTypeOptions().find((o) => o.value === phoneType);
          if (this.phoneNumberForm().get('phoneType')?.value !== phoneTypeToSet?.value) {
            this.phoneNumberForm()
              .get('phoneType')
              ?.setValue(phoneTypeToSet?.value as string, { emitEvent: false });
          }
        }
      }
    });

    effect(() => {
      this.initializeForm();
    });
  }

  get privacySpecsArray(): FormArray {
    return this.phoneNumberForm().get('privacySpecs') as FormArray;
  }

  initializeForm(): void {
    this.privacySpecsArray.clear();
    this.filteredPrivacyList().forEach((spec) => {
      const itemsFormArray = new FormArray(
        spec.items.map(
          (item) =>
            new FormGroup({
              isChecked: new FormControl(item?.authorizedFlag ?? false, { nonNullable: true }),
              partyPrivacyItemId: new FormControl(item.partyPrivacyItemId),
            }),
        ),
      );

      if (itemsFormArray.length > 0) {
        this.privacySpecsArray.push(
          new FormGroup({
            partyPrivacySpecId: new FormControl(spec.partyPrivacySpecId),
            items: itemsFormArray,
          }),
        );
      }
    });
  }
}
