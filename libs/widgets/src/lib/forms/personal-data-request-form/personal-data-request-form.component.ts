import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { SelectOption } from '@libs/types';
import { PersonalDataRequestFormGroup } from './personal-data-request-form.type';
import { FormFieldErrorMessageComponent } from '@libs/plugins';

@Component({
  selector: 'widget-personal-data-request-form',
  templateUrl: './personal-data-request-form.component.html',
  styleUrls: ['./personal-data-request-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, TranslatePipe, FormFieldComponent, FormFieldErrorMessageComponent],
})
export class PersonalDataRequestFormComponent {
  personalDataRequestForm = input.required<FormGroup<PersonalDataRequestFormGroup>>();
  emailOptions = input<SelectOption[]>([]);
  formatOptions = input<SelectOption[]>([]);

  onShareDataApprovalChange(event: Event) {
    const value = (event.target as HTMLInputElement).checked;
    this.personalDataRequestForm().get('shareDataApproval')?.setValue(value);

    if (!value) {
      this.personalDataRequestForm().setErrors({
        required: true,
      });
    }
  }
}
