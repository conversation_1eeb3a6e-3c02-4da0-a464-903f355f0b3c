<form [formGroup]="personalDataRequestForm()" class="form">
  <widget-form-field
    [label]="'email' | translate"
    formControlName="email"
    [placeholder]="'selectEmailPlaceholder' | translate"
    type="select"
    [options]="{ options: emailOptions() }"
  ></widget-form-field>

  <widget-form-field
    [label]="'format' | translate"
    formControlName="format"
    [placeholder]="'selectFormatPlaceholder' | translate"
    type="select"
    [options]="{ options: formatOptions() }"
  ></widget-form-field>

  <div class="share-data-approval">
    <eds-checkbox
      id="shareDataApproval"
      name="shareDataApproval"
      (change)="onShareDataApprovalChange($event)"
      [isRequired]="true"
    >
      <label for="shareDataApproval">
        {{ 'shareDataApprovalLabel' | translate }}
      </label>
    </eds-checkbox>
    @if (personalDataRequestForm().touched && !personalDataRequestForm().get('shareDataApproval')?.value) {
      <widget-form-field-error-message [errors]="personalDataRequestForm().errors"></widget-form-field-error-message>
    }
  </div>
</form>
