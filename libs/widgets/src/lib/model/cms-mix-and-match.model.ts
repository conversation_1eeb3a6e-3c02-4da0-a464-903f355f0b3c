export interface CmsMixAndMatchDescription {
  image: string;
  title: string;
  description: string;
}

export interface CmsMixAndMatchPlanBenefit {
  title: string;
  body: { value: string };
  image: { mediaImage: { url: string } };
}

export interface CmsMixAndMatchMixer {
  label: string;
  value: number | string;
  min: number;
  max: number;
  step?: number;
  customValues: object;
  unit: string;
  icon: string;
  disabled: boolean;
  showButtons: boolean;
  showValue: boolean;
  unlimitedOffset: number;
}

export interface CmsMixAndMatch {
  currency: string;
  description: CmsMixAndMatchDescription[];
  mixer: CmsMixAndMatchMixer[];
}
