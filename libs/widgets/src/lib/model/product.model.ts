import { DataListItem, TagAppearances } from '@eds/components';
import { eProduct, Product } from '@libs/types';

export interface BundledDevice {
  type?: string;
  name: string;
  imagePath?: string;
  fallbackImagePath?: string;
  isByod?: boolean;
}

export interface ProductSummary {
  iconName: string;
  name: string;
  description?: string;
  owner?: string;
  detailLink: string;
  category: string;
  status?: eProduct.ProductStatusShortCodes;
  statusText?: string;
  upperText?: string;
  statusTagAppearance?: TagAppearances;
  activationDate?: string;
  devices: BundledDevice[];
}

export interface ProductSummaryData {
  id: string;
  title: string;
  data: Partial<ProductSummary>[];
}

export interface ProductCardProps {
  id?: string;
  title: string;
  productId: number;
  dataListItems: DataListItem[];
  productDetail: Product.ProductDetail;
}
