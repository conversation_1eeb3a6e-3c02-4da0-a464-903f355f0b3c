import { FormControl } from '@angular/forms';
import { City, Country, State } from '@libs/types';

export interface AddNewAddressForm {
  addressLabel: FormControl<string | null>;
  country: FormControl<Country.Country | null>;
  state: FormControl<State.State | null>;
  city: FormControl<City.City | null>;
  address: FormControl<string | null>;
  postalCode: FormControl<string | null>;
  fullName: FormControl<string | null>;
  phoneNumber: FormControl<string | null>;
  addressDescription: FormControl<string | null>;
}

export enum eAddressSelectionMode {
  EXIST_ADDRESS = 'EXIST_ADDRESS',
  NEW_ADDRESS = 'NEW_ADDRESS',
}
