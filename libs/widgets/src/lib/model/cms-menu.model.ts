export interface CMSMenuPromotion {
  title: string;
  description: string;
  image: string;
  href: string;
}

export interface CMSMenuItem {
  label: string;
  items?: CMSMenuItem[];
  href?: string;
  promotion?: CMSMenuPromotion;
  isExternal?: boolean;
  icon?: string;
}

export interface CMSNavigationSection {
  name: string;
  items: CMSNavigationItem[];
}

export interface CMSNavigationItem {
  title: string;
  description: string | null;
  url: string;
  internal: boolean;
  expanded: boolean;
  attributes: {
    class: string | null;
  };
  extras: NavigationExtras;
  children: CMSNavigationItem[];
}

interface NavigationExtras {
  icon?: string;
}
