import { PaymentMethodData } from '@libs/bss';
import { ePayment } from '@libs/types';

export type SavedPaymentMethod = {
  id?: string | number;
  name: string;
  nameOnBankAccount?: string;
  value: string | number;
  isChecked?: boolean;
  isDisabled?: boolean;
  isInvalid?: boolean;
  networkLogo: string;
  cardNumber: string;
  cardExpiry: string;
  isRemovable: boolean;
  default?: boolean;
};

export type PaymentMethodSelectionData = {
  type: ePayment.PaymentMethodType;
  id: string | number;
  data?: PaymentMethodData;
};

export enum PaymentMethodSelectionTypes {
  AUTHORIZED = 'pre_authorized',
  PAY_NOW = 'pay_now',
}
