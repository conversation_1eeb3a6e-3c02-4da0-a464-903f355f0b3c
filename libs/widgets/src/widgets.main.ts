import { provideZonelessChangeDetection } from '@angular/core';
import { createApplication } from '@angular/platform-browser';
import { createCustomElement } from '@angular/elements';
import { WIDGETS } from './widgets';

createApplication({
  providers: [provideZonelessChangeDetection()],
})
  .then((app) => {
    Object.entries(WIDGETS).map(([key, value]) => {
      const component = createCustomElement(value, { injector: app.injector });
      customElements.define(key, component);
    });
  })
  .catch((err) => console.error(err));
