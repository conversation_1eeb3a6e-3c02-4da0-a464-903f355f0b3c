import {
  AccordionGroupComponent,
  ActionListComponent,
  CheckoutCardComponent,
  CmsBannerComponent,
  CmsBlogCardComponent,
  CmsDeviceImagesComponent,
  CmsExclusivePerksComponent,
  CmsFaqComponent,
  CmsFeaturesStripComponent,
  CmsHeaderComponent,
  CmsLatestArticlesComponent,
  CmsPlanCardComponent,
  CmsPlanCatalogComponent,
  CmsSubOfferComponent,
  CmsSubOffersComponent,
  EmptyStateComponent,
  ErrorComponent,
  FooterComponent,
  HeaderComponent,
  HomeBannerComponent,
  HomeNavigationBarComponent,
  IconComponent,
  InteractionListComponent,
  InvoiceCardComponent,
  InvoiceCarouselComponent,
  InvoiceOverviewComponent,
  LanguagePreferenceComponent,
  MiniShoppingCartComponent,
  NavigationComponent,
  OfferCardComponent,
  OfferCarouselComponent,
  OfferDeviceCardComponent,
  OfferPlanCardComponent,
  OfferPriceComponent,
  PageHeadingComponent,
  PaymentCreditCheckComponent,
  PaymentMethodComponent,
  ProductCardComponent,
  ProductSummaryComponent,
  SearchComponent,
  SegmentAreaComponent,
  SelectiveUsageSummaryComponent,
  StepperComponent,
  SuccessIconComponent,
  SwiperComponent,
  TopOffersComponent,
  UsageSummaryComponent,
  MobileMenuComponent,
  HeaderNavigationComponent,
  CmsCtaSupportComponent,
  CmsMixAndMatchComponent,
  PlanChangeCartSummaryComponent,
} from './lib/components';

import {
  CmsLayoutContentDefaultComponent,
  CmsLayoutPageDefaultComponent,
  LayoutPageDefaultComponent,
  LayoutPageDetailComponent,
} from './lib/layouts';
import {
  OrderCardComponent,
  OrderAndQuoteSummaryComponent,
  OrderFilterComponent,
  QuoteCardComponent,
  SuborderCardComponent,
} from './lib/components/orders-and-quotes';

export const WIDGETS = {
  'widget-accordion-group': AccordionGroupComponent,
  'widget-action-list': ActionListComponent,
  'widget-interaction-list': InteractionListComponent,
  'widget-empty-state': EmptyStateComponent,
  'widget-error': ErrorComponent,
  'widget-footer': FooterComponent,
  'widget-header': HeaderComponent,
  'widget-icon': IconComponent,
  'widget-invoice-card': InvoiceCardComponent,
  'widget-invoice-carousel': InvoiceCarouselComponent,
  'widget-invoice-overview': InvoiceOverviewComponent,
  'widget-language-preference': LanguagePreferenceComponent,
  'widget-navigation': NavigationComponent,
  'widget-offer-card': OfferCardComponent,
  'widget-offer-carousel': OfferCarouselComponent,
  'widget-page-heading': PageHeadingComponent,
  'widget-cms-plan-card': CmsPlanCardComponent,
  'widget-product-card': ProductCardComponent,
  'widget-order-card': OrderCardComponent,
  'widget-product-summary': ProductSummaryComponent,
  'widget-order-and-quote-summary': OrderAndQuoteSummaryComponent,
  'widget-order-filter': OrderFilterComponent,
  'widget-quote-card': QuoteCardComponent,
  'widget-suborder-card': SuborderCardComponent,
  'widget-search': SearchComponent,
  'widget-segment-area': SegmentAreaComponent,
  'widget-swiper': SwiperComponent,
  'widget-selective-usage-summary': SelectiveUsageSummaryComponent,
  'widget-usage-summary': UsageSummaryComponent,
  'widget-cms-header': CmsHeaderComponent,
  'widget-cms-sub-offer': CmsSubOfferComponent,
  'layout-default': LayoutPageDefaultComponent,
  'layout-detail': LayoutPageDetailComponent,
  'layout-cms-default': CmsLayoutPageDefaultComponent,
  'layout-cms-content-default': CmsLayoutContentDefaultComponent,
  'widget-cms-plan-catalog': CmsPlanCatalogComponent,
  'widget-cms-banner': CmsBannerComponent,
  'widget-cms-features-strip': CmsFeaturesStripComponent,
  'widget-cms-sub-offers': CmsSubOffersComponent,
  'widget-cms-faq': CmsFaqComponent,
  'widget-stepper': StepperComponent,
  'widget-checkout-card': CheckoutCardComponent,
  'widget-offer-plan-card': OfferPlanCardComponent,
  'widget-offer-device-card': OfferDeviceCardComponent,
  'widget-offer-price': OfferPriceComponent,
  'widget-payment-credit-check': PaymentCreditCheckComponent,
  'widget-payment-method': PaymentMethodComponent,
  'widget-cms-device-images': CmsDeviceImagesComponent,
  'widget-mini-shopping-cart': MiniShoppingCartComponent,
  'widget-cms-blog-card': CmsBlogCardComponent,
  'widget-cms-exclusive-perks': CmsExclusivePerksComponent,
  'widget-cms-latest-articles': CmsLatestArticlesComponent,
  'widget-cms-top-offers': TopOffersComponent,
  'widget-cms-home-banner': HomeBannerComponent,
  'widget-success-icon': SuccessIconComponent,
  'widget-mobile-menu': MobileMenuComponent,
  'widget-home-navigation-bar': HomeNavigationBarComponent,
  'widget-header-navigation': HeaderNavigationComponent,
  'widget-cms-cta-support': CmsCtaSupportComponent,
  'widget-cms-mix-and-match': CmsMixAndMatchComponent,
  'widget-plan-change-cart-summary': PlanChangeCartSummaryComponent,
};
