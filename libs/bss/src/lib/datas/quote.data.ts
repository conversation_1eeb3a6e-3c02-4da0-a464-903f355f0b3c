import {
  AbstractOfferInstance,
  AbstractQuote,
  CatalogGroupContractType,
  Characteristic,
  CustomerOrder,
  CustomerOrderItemActionTypeEnum,
  eBusinessFlow,
  eProduct,
  OfferInstanceCharEnum,
  OfferInstanceKeyEnum,
  OfferInstanceProductTypeEnum,
  PaymentInfo,
  QuoteCharEnum,
  QuotePrice,
  ServiceSpecKeyEnum,
} from '@libs/types';
import { PlanData } from './plan.data';
import { DataList, Delivery } from '@libs/widgets';
import { formatAddress } from '@libs/core';

export class QuoteData {
  private defaults: AbstractQuote;
  private priceDetail: QuotePrice.QuotePriceDetail;

  constructor(defaults?: AbstractQuote, priceDetail?: QuotePrice.QuotePriceDetail) {
    this.defaults = defaults;
    this.priceDetail = priceDetail;
  }

  get quote(): AbstractQuote {
    return this.defaults;
  }

  get price(): QuotePrice.QuotePriceDetail {
    return this.priceDetail;
  }

  get bundleOffers(): AbstractOfferInstance[] {
    return this.getOfferInstancesByKey(OfferInstanceKeyEnum.MOBILE_PLAN_BUNDLE) ?? [];
  }

  get currentPlanData(): AbstractOfferInstance {
    return this.getOfferInstancesByKey(OfferInstanceKeyEnum.DATA_BUCKET)?.find(Boolean);
  }

  get currentPlanSms(): AbstractOfferInstance {
    return this.getOfferInstancesByKey(OfferInstanceKeyEnum.SMS_BUCKET)?.find(Boolean);
  }

  get currentPlanVoice(): AbstractOfferInstance {
    return this.getOfferInstancesByKey(OfferInstanceKeyEnum.VOICE_BUCKET)?.find(Boolean);
  }

  get bundleOfferIds(): string[] {
    return this.bundleOffers.map((item) => item.offerId?.toString());
  }

  get deactivationBundleOffer(): AbstractOfferInstance {
    return this.bundleOffers.find((item) => item.actionCode === CustomerOrderItemActionTypeEnum.DEACTIVATION);
  }

  get activationBundleOffers(): AbstractOfferInstance[] {
    return this.bundleOffers.filter((item) => item.actionCode === CustomerOrderItemActionTypeEnum.ACTIVATION);
  }

  get prepaidBundleOffers(): AbstractOfferInstance[] {
    return this.bundleOffers.filter((item) => item.saleType === CatalogGroupContractType.PREPAID);
  }

  get postpaidBundleOffers(): AbstractOfferInstance[] {
    return this.bundleOffers.filter((item) => item.saleType === CatalogGroupContractType.POSTPAID);
  }

  get planChangeUrl(): string {
    if (this.prepaidBundleOffers.length > 0) {
      return 'offer-list-prepaid';
    }
    return 'offer-list-postpaid';
  }

  get purchaseAddonUrl(): string {
    return 'purchase-addon';
  }

  get bundleShipmentOfferIds(): number[] {
    return this.bundleOffers.map((item) => item?.specItemIds?.[ServiceSpecKeyEnum.SHIPMENT_CFS]).filter(Boolean);
  }

  get paymentInformation(): PaymentInfo {
    return this.defaults?.paymentInfo;
  }

  get nonBYODBundleOffers(): AbstractOfferInstance[] {
    return this.bundleOffers.filter((item) => !item?.offerInstances?.[eProduct.ProductConsts.MOBILE_BYOD]);
  }

  get paymentInfoBillingAddress() {
    return this.defaults?.paymentInfo?.billingAddress || null;
  }

  get paymentReference() {
    return this.defaults?.paymentInfo?.paymentReference;
  }

  get hasPaymentReferenceRowId(): boolean {
    return !!this.defaults?.paymentInfo?.paymentReference?.rowId;
  }

  get paymentMethod() {
    return this.defaults?.paymentInfo?.type;
  }

  get payBillAmountValue(): number {
    return Number(this.getQuoteCharByKey(QuoteCharEnum.PAY_BILL_AMOUNT).value) ?? null;
  }

  getQuoteCharByKey(key: OfferInstanceCharEnum | QuoteCharEnum): Characteristic {
    return this.defaults?.quoteChars[key] ?? null;
  }

  get lastCurrentOrderItemId() {
    return this.quote.offerInstances?.mobilePlanBundle?.find(Boolean)?.customerOrderItemId; // TODO
  }

  get source() {
    return this.quote.source || {};
  }

  lastCurrentOrderItemIdBy(flowSpecCode?: eBusinessFlow.Specification) {
    if (
      flowSpecCode === eBusinessFlow.Specification.PURCHASE_ADDON ||
      flowSpecCode === eBusinessFlow.Specification.NEW_ADDON
    ) {
      return this.quote.offerInstances.dataAddon[0].customerOrderItemId;
    }
    return this.quote.offerInstances?.mobilePlanBundle?.find(Boolean)?.customerOrderItemId;
  }

  get familyCategoryName(): string {
    return this.bundleOffers.find(Boolean)?.familyCategoryName;
  }

  get plans(): AbstractOfferInstance[] {
    const offerInstances = this.getOfferInstancesByKey(OfferInstanceKeyEnum.MOBILE_PLAN_BUNDLE);

    return offerInstances.map((offer) => {
      const device = this.deviceOffer(offer.customerOrderItemId);
      return new PlanData(offer, device, this.priceDetail).plan;
    });
  }

  get rawPlans(): PlanData[] {
    const offerInstances = () => {
      switch (this.defaults?.flowSpecCode) {
        case eBusinessFlow.Specification.PACKAGE_CHANGE:
          return this.activationBundleOffers;
        case eBusinessFlow.Specification.PURCHASE_ADDON:
        case eBusinessFlow.Specification.NEW_ADDON:
        case eBusinessFlow.Specification.DEACTIVATE_ADDON:
          return this.getOfferInstancesByKey(OfferInstanceKeyEnum.DATA_ADDON);
        default:
          return this.getOfferInstancesByKey(OfferInstanceKeyEnum.MOBILE_PLAN_BUNDLE);
      }
    };

    switch (this.defaults?.flowSpecCode) {
      case eBusinessFlow.Specification.PURCHASE_ADDON:
      case eBusinessFlow.Specification.NEW_ADDON:
      case eBusinessFlow.Specification.DEACTIVATE_ADDON:
        return offerInstances().map((offer) => {
          return new PlanData(null, null, null, offer);
        });
      default:
        return offerInstances().map((offer) => {
          const device = this.deviceOffer(offer.customerOrderItemId);
          return new PlanData(offer, device, this.priceDetail);
        });
    }
  }

  get addonsDataList(): DataList {
    return {
      items: this.rawPlans
        ?.filter((item) => item?.addon)
        .map((item) => item.addon)
        .map((addon) => ({
          className: '',
          key: addon.offerName,
          value: addon.offerChargeType,
        })),
    };
  }

  get planSaleType(): CatalogGroupContractType {
    return this.bundleOffers[0]?.saleType;
  }

  get currentBusinessFlowSpecShortCode(): eBusinessFlow.Specification {
    return this.defaults?.flowSpecCode;
  }

  get customerOrderId(): number {
    return this.defaults?.customerOrderId;
  }

  get isPlanChange(): boolean {
    return this.defaults?.flowSpecCode === eBusinessFlow.Specification.PACKAGE_CHANGE;
  }

  get isPurchaseAddon(): boolean {
    return this.defaults?.flowSpecCode === eBusinessFlow.Specification.PURCHASE_ADDON;
  }

  getOfferInstancesByKey(key: OfferInstanceKeyEnum): AbstractOfferInstance[] {
    return this.defaults?.offerInstances[key] ?? [];
  }

  findOffer(customerOrderItemId: number, offers: AbstractOfferInstance[]): AbstractOfferInstance {
    return offers.filter((offer) => offer.customerOrderItemId === customerOrderItemId)?.find(Boolean);
  }

  deviceOffer(planCustomerOrderItemId: number): AbstractOfferInstance {
    const offerInstances = this.findOffer(planCustomerOrderItemId, this.bundleOffers);
    return offerInstances?.offerInstances[OfferInstanceKeyEnum.MOBILE_DEVICE]
      ?.filter((device) => device.productType === OfferInstanceProductTypeEnum.DEVICE)
      ?.find(Boolean);
  }

  findPlan(customerOrderItemId: number): PlanData {
    const offerInstances = this.getOfferInstancesByKey(OfferInstanceKeyEnum.MOBILE_PLAN_BUNDLE);
    const plan = offerInstances?.find((offer) => offer.customerOrderItemId === customerOrderItemId);

    if (!plan) {
      return null;
    }
    return new PlanData(plan, this.deviceOffer(plan.customerOrderItemId), this.priceDetail);
  }

  getOfferInstanceOfBundleOffer(offerKey: OfferInstanceKeyEnum): AbstractOfferInstance {
    return this.bundleOffers.flatMap((item) => item?.offerInstances?.[offerKey]).find(Boolean);
  }

  hasShipment(): boolean {
    return this.bundleShipmentOfferIds.length > 0;
  }

  isSuitablePackageChange(productOfferId: number) {
    const mobilePlanOfferInstances = this.getOfferInstancesByKey(OfferInstanceKeyEnum.MOBILE_PLAN_BUNDLE);

    return mobilePlanOfferInstances.find((offerInstance) => offerInstance.offerId === productOfferId);
  }

  get isOrderFullyCompleted(): boolean {
    return this.quote.statusShortCode === CustomerOrder.OrderStatus.FULL_FINISHED;
  }

  get isOrderInPreparation(): boolean {
    return this.quote.statusShortCode === CustomerOrder.OrderStatus.ESB;
  }

  invoiceAccount(): Delivery.InvoiceAccountSummaryCard {
    // Get billing information
    const billingAddress = this.paymentInfoBillingAddress;
    const billingInfo = this.paymentInformation?.billingInfo?.find(Boolean);
    const billingAccountId = billingInfo?.selectedInvoicingAccountKey?.accountId;

    // Determine if we should use invoice account or billing address label
    const isPostpaid = (this.postpaidBundleOffers?.length || 0) > 0;
    const label = isPostpaid ? 'invoiceAccount' : 'billingAddress';

    return {
      label,
      title: formatAddress(billingAddress),
      contactPerson: billingAddress?.addressContact || '',
      phoneNo: billingAddress?.phoneNumber || '',
      invoiceId: billingAccountId,
    };
  }
}
