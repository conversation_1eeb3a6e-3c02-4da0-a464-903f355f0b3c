export namespace Terms {
  export interface PartyPrivacyDocumentResponse {
    templateId: number;
    resolvedTemplateBody: string;
    multiLanguage: MultiLanguageData;
    shortCode: string;
    name: string;
    descr: string;
  }

  export interface OfferTermsRequest {
    keyProductOffers: { productOfferId: number }[];
  }

  export enum ClauseShortCode {
    REGISTER_CLAUSE_CODE = 'register',
  }

  export interface Clause {
    shortCode: ClauseShortCode;
    templateBody: string;
    externalId: number;
    language: string;
    priority: number;
    multiLanguageData: MultiLanguageData[];
  }

  interface MultiLanguageData {
    tmplBody: string;
    id: number;
    language: string;
    isActive: number;
    name: string;
    en: LanguageData;
  }

  export interface LanguageData {
    name: string;
    tmplBody: string;
  }
}
