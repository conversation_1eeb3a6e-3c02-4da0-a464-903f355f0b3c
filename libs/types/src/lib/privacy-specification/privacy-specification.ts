import { eCustomer } from './eCustomer';
import { ePrivacySpecification } from './ePrivacySpecification';
import { Lov } from '../lov';

export namespace PrivacySpecification {
  export interface PartyPrivacySpecificationRequest {
    // contactMediumTypeList: ContactMediumType.ContactMediumType[];
    contactMediumTypeList: Lov.LovResult[];
    businessInteractionSpecification: string;
    customerType: eCustomer.CustomerTypeShortCode;
    roleType: ePrivacySpecification.RoleType;
    levelType?: string;
  }

  export type PartyPrivacySpecificationResponse = PartyPrivacySpecification[];

  export interface PartyPrivacySpecification {
    id: number;
    shortCode: string;
    sortId: number;
    name: string;
    description?: string;
    authorized: boolean;
    canOptOut: boolean;
    authorizedRequiredToPerformInteraction: boolean;
    hasAdditionalDocument: boolean;
    partyPrivacyId?: number;
    partyPrivacyTypeId?: number;
  }
}
