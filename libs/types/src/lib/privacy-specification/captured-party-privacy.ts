import { <PERSON><PERSON><PERSON><PERSON><PERSON>ontent, GeneralParameterList, GeneralParameterKeyCustomer } from '../dto';
import { GeneralStatus } from '../common';
import { ContactMediumType } from '../lov';
import { GeneralType } from '../common';
import { Notification } from '../notification';

export namespace CapturedPartyPrivacy {
  export type Content = GeneralParameterContent<
    Partial<{
      description: string;
      items: Item[];
      name: string;
      partyPrivacyId?: number;
      partyPrivacySpecId: number;
      id: number;
      shortCode: string;
      sortId: string | number;
      hasAdditionalDocument: boolean;
      authorizedRequiredToPerformInteraction: boolean;
    }>
  >;

  export type ConsentItemStatus = 'AUTHORIZED' | 'UNAUTHORIZED' | 'UNKNOWN';

  export enum ConsentItemStatusEnum {
    AUTHORIZED = 'AUTHORIZED',
    UNAUTHORIZED = 'UNAUTHORIZED',
    UNKNOWN = 'UNKNOWN',
  }

  export type Item = GeneralParameterContent<
    Partial<{
      partyPrivacyId?: number; // ui tarafında eklenmiştir.
      authorizedFlag: boolean;
      canOptOut: boolean;
      contactDataExtension: string;
      contactMediumData: string;
      contactDataPrefix: string;
      contactMediumId: number;
      contactMediumType: string;
      externalNotificationChannelType: string;
      consentItemStatus: ConsentItemStatus;
      consentAcquisitionType: string;
      notificationChannelType: Notification.NotificationChannelTypes;
      notificationChannelTypeName: string;
      partyPrivacyItemId: number;
      startDate: Date | number;
      mergedContactData: string;
    }>
  >;

  export interface PartyPrivacyDefaultRequest {
    partyRoleId?: number;
    keyCustomer?: GeneralParameterKeyCustomer;
  }

  export interface CustomerOrderBsnInterSpec {
    bsnInterSpecId: number;
    shortCode: string;
    name: string;
    description: string;
    isVsbl: number;
  }

  export interface PartyPrivacySpecValidStrategy {
    partyPrivSpecVldStrgyId: number;
    partyPrivSpecId: number;
    partyPrivSpec: PartyPrivacySpec;
    bsnInterSpecId: number;
    bsnInterSpec: CustomerOrderBsnInterSpec;
    operShortCode: string;
    expireDateStId: number;
    validForStrategy: GeneralType;
    vldForStrgyId: number;
    vldForStrgyVal: string;
  }

  export interface PartyPrivacySpecContactMediumTypeRelation {
    id: number;
    partyPrivacySpecId: number;
    contactMediumTypeId: number;
    contactMediumTypeShortCode: string;
  }

  export interface PartyPrivacySpecNotificationTopicRelation {
    id: number;
    partyPrivacySpecId: number;
    notificationTopicId: number;
    notificationTopicShortCode: string;
  }

  export interface PartyPrivacySpecPartyRoleTypeRelation {
    id: number;
    partyPrivacySpecId: number;
    partyRoleTypeId: number;
    partyRoleTypeShortCode: string;
    defaultDefinitionAllowed: number;
  }

  export interface PartyPrivacySpecNotificationChannelConfig {
    boundedContactMediumTypeIdList: number[];
    canOptOut: boolean;
    canUnsub: boolean;
    contactMediumRelated: boolean;
    defaultStatus: GeneralStatus;
    id: number;
    notificationChannel: Notification.KeyNotificationChannel;
    partyPrivacySpecValidStrategyList: PartyPrivacySpecValidStrategy[];
    partyPrivSpecId: number;
  }

  export interface PartyPrivacySpecCharVal extends GeneralParameterList {
    partyPrivacySpecCharValId: number;
    charId: number;
    charValId: number;
    charShortCode: string;
    charValShortCode: string;
    charName: string;
    charValName: string;
    val: string;
  }

  export interface PartyPrivacySpecProductOfferRelation {
    id: number;
    partyPrivacySpecId: number;
    productOfferId: number;
  }

  export interface PartyPrivacySpecCustomerTypeRelation {
    id: number;
    partyPrivacySpecId: number;
    customerTypeId: number;
    customerTypeShortCode: string;
  }

  export interface PartyPrivacySpecChar extends GeneralParameterList {
    partyPrivacySpecCharId: number;
    charId: string;
    name: string;
    shortCode: string;
    charValList: PartyPrivacySpecCharVal;
  }

  export interface PartyPrivacySpec extends GeneralParameterList {
    charList: PartyPrivacySpecChar[];
    contactMediumRelated: boolean;
    contactMediumTypeRelationList: PartyPrivacySpecContactMediumTypeRelation[];
    customerTypeRelationList: PartyPrivacySpecCustomerTypeRelation[];
    defaultDefinitionAllowed: boolean;
    defaultStatus: GeneralStatus;
    description: string;
    id?: number;
    levelType: GeneralType;
    manageableAtNotificationChannelLevel: boolean;
    name: string;
    notificationChannelList: PartyPrivacySpecNotificationChannelConfig[];
    notificationTopicRelationList: PartyPrivacySpecNotificationTopicRelation[];
    partyPrivacySpecId: number;
    partyPrivacyType: GeneralType;
    partyPrivacyUsageType: GeneralType;
    partyRoleTypeRelationList: PartyPrivacySpecPartyRoleTypeRelation[];
    productOfferRelationList: PartyPrivacySpecProductOfferRelation[];
    shortCode: string;
    sortId: number;
  }

  export interface PartyPrivacyDefault extends GeneralParameterList {
    partyPrivacySpec: PartyPrivacySpec;
    contactMediumType: ContactMediumType.ContactMediumType;
    status: GeneralStatus;
    authorizedFlag: boolean;
    consentItemStatus: ConsentItemStatus; // added for ui
  }

  export interface PartyPrivacyDefaultSaveRequest extends PartyPrivacyDefaultRequest {
    partyPrivacyDefaultList: PartyPrivacyDefault[];
  }

  export interface Result {
    // customerId is pathVariable
    customerId?: number;
    customerTypeId: number;
    partyRoleId: number;
    dataType?: string;
    partyRoleTypeId: number;
    dataRowId?: number;
    privacyList: Content[];
    businessInteractionShortCode?: string;
    // token is pathVariable
    token?: string;
  }
}
