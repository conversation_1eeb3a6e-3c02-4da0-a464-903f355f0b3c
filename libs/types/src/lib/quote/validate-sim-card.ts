import { Characteristic } from '@libs/types';

export namespace ValidateSimCard {
  export type RequestWrapper = {
    request: Request;
    customerOrderItemId: number;
  };

  export interface Request {
    charShortCode: string;
    charVal: string;
    simCardOfferId: number;
    customerId: number;
    customerOrderId: number;
  }

  export interface Response {
    relatedParty: unknown;
    resourceQualificationItem: ValidateSimCard.ResourceQualificationItem[];
    resourceQualificationDate: Date;
    id: string;
    state: string;
    qualificationResult: string;
    characteristic?: Characteristic[];
  }

  export interface ResourceQualificationItem {
    service: Service;
    phoneNumberVL: PhoneNumberVL;
    id: string;
    state: string;
    expirationDate: Date;
    qualificationItemResult: string;
  }

  export interface PhoneNumberVL {
    characteristic: Characteristic[];
    id: string;
    resourceType: string;
    value: string;
  }

  export interface Service {
    serviceSpecification: ServiceSpecification;
    serviceCharacteristic: Characteristic[];
  }

  export interface ServiceSpecification {
    id: string;
    name: string;
  }
}
