export namespace BusinessInteractionReasonType {
  export interface BusinessInteractionReasonType {
    id: number;
    shortCode: string;
    parentBusinessInteractionReasonTypeId: number;
    parentBusinessInteractionReasonTypeShortCode: string;
    businessInteractionTypeId: number;
    externalShortCode: string;
    resourceKey: string;
    prio: number;
    isActive: number;
    name: string;
    description: string;
    multiLanguage: Record<string, { name: string; description: string }>;
  }
}
