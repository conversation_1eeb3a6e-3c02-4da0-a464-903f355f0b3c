export namespace eBusinessFlow {
  export enum Specification {
    ADD_DEVICE = 'ADD_DEVICE',
    BUNDLE_CHANGE = 'BUNDLE_CHANGE',
    CHANNEL_SWAP = 'CHANNEL_SWAP',
    CUST_CREATE = 'CUST_CREATE',
    DEACTIVATE_ADDON = 'DEACTIVATE_ADDON',
    DEACTIVATION = 'DEACTIVATION',
    DEACTIVATION_B2B = 'DEACTIVATION_B2B',
    DEACTV_BILLING_ACCOUNT_B2B = 'DEACTV_BILLING_ACCOUNT_B2B',
    DELIVERY_GROUP = 'DELIVERY_GROUP',
    DELIVERY_ORDER = 'DELIVERY_ORDER',
    DEVICE_CHANGE = 'DEVICE_CHANGE',
    DEVICE_UPGRADE = 'DEVICE_UPGRADE',
    END_USER_INVITATION = 'END_USER_INVITATION',
    FDN_CHANGE = 'FDN_CHANGE',
    FRAUD_BAR = 'FRAUD_BAR',
    FRAUD_UNBAR = 'FRAUD_UNBAR',
    LINE_LEVEL_B2B_ACTIVATION = 'LINE_LEVEL_B2B_ACTIVATION',
    LINE_LEVEL_B2B_DEACTIVATION = 'LINE_LEVEL_B2B_DEACTIVATION',
    LOST_BAR = 'LOST_BAR',
    LOST_UNBAR = 'LOST_UNBAR',
    MAIN_ORDER = 'MAIN_ORDER',
    MANAGE_ADDON = 'MANAGE_ADDON',
    MANAGE_ADDON_HIER = 'MANAGE_ADDON_HIER',
    MOVE = 'MOVE',
    MSISDN_CHANGE = 'MSISDN_CHANGE',
    OPTION_CHANGE = 'OPTION_CHANGE',
    ORDER_CANCEL = 'ORDER_CANCEL',
    PACKAGE_CHANGE = 'PACKAGE_CHANGE',
    PACKAGE_CHANGE_B2B = 'PACKAGE_CHANGE_B2B',
    PAYMENT_NOTIFICATION = 'PAYMENT_NOTIFICATION',
    PAY_BILL = 'PAY_BILL',
    PLAN_BUNDLE = 'PLAN_BUNDLE',
    PLAN_BUNDLE_B2B = 'PLAN_BUNDLE_B2B',
    POST2PRE = 'POST2PRE',
    POST_PAID = 'POST_PAID',
    PRE2POST = 'PRE2POST',
    PRE_PAID = 'PRE_PAID',
    PURCHASE_ADDON = 'PURCHASE_ADDON',
    NEW_ADDON = 'NEW_ADDON',
    USAGE_HISTORY = 'USAGE_HISTORY',
    REACTIVATION = 'REACTIVATION',
    REAL_SALE = 'REAL_SALE',
    REMOVE_DEVICE = 'REMOVE_DEVICE',
    REMOVE_END_USER = 'REMOVE_END_USER',
    REMOVE_SERVICE = 'REMOVE_SERVICE',
    SERVICE_PAUSE = 'SERVICE_PAUSE',
    SIMCARD_CHANGE = 'SIMCARD_CHANGE',
    SIMCARD_DELIVERY = 'SIMCARD_DELIVERY',
    TOP_UP = 'TOP_UP',
    TRANSFER_OWNERSHIP = 'TRANSFER_OWNERSHIP',
    VIEW_CUST = 'VIEW_CUST',
    ESIM_ACTIVATION = 'ESIM_ACTIVATION',
  }

  export enum WorkflowStateType {
    CART_SUMMARY = 'cartSummary',
    PRE_VALIDATION = 'preValidation',
    PLAN_SELECTION = 'planSelection',
    PRODUCT_CONF = 'productConf',
    DEFAULT_DELIVERY_ADDRESS_SELECTION = 'defaultDeliveryAddressSelection',
    DELIVERY = 'delivery',
    INVOICE_SELECT = 'invoiceSelect',
    BILLING_ADDRESS = 'billingAddress',
    CREDIT_CHECK = 'creditCheck',
    PAYMENT_BILLING = 'paymentBilling',
    ORDER_REVIEW = 'orderReview',
    ORDER_SUBMIT = 'orderSubmit',
    ORDER_SUMMARY = 'orderSummary',

    BILL_PAYMENT = 'billPayment',

    ACCOUNT_CONFIGURATION = 'accountConfiguration',
    ADDON_SELECTION = 'addonSelection',
    ADDRESS_SELECTION = 'addressSelect',
    ADD_LINE = 'addLine',
    AMOUNT_SELECTION = 'amountSelection',
    CUSTOMER_SELECTION = 'customerSelect',
    DELIVERY_CONFIGURED = 'deliveryConfigured',
    DEVICE_SELECTION = 'deviceSelection',
    DEVICE_SELECTION_B2B = 'deviceSelectionb2b',
    INITIALIZE = 'initialize',
    INSTALLATION_SELECTION = 'installationSelection',
    NUMBER_RESERVATION = 'numberReservation',
    OFFER_SELECTION = 'offerSelection',
    ORDER_COMPLETE = 'orderComplete',
    ORDER_INITIALIZE = 'orderInitialize',
    PHONE_NUMBER = 'phoneNumber',
    PLAN_DETAILS = 'planDetails',
    PLAN_SELECTION_B2B = 'planSelectionb2b',
    REASON_SELECTION = 'reasonSelection',
    RESERVE_NEW_NUMBER = 'reserveNewNumber',
    SERVICE_PAUSE = 'servicePause',
    SHIPMENT = 'shipment',
    SIMCARD_DELIVERY = 'simCardDelivery',
    SIMCARD_SELECTION_B2B = 'simcardSelectionb2b',
    SIM_CARD_CONFIG = 'simCardConfig',
    TARGET_WALLET_SELECTION = 'targetWalletSelection',
    WAITING_FOR_DELIVERYGROUP = 'waitingForDeliveryGroup',
    WAITING_NUMBER_RESERVATION = 'waitingNumberReservation',
    ESIM_ACTIVATION = 'eSIMActivation',
  }

  export enum Levels {
    PRODUCT_DETAIL_PLAN_CARD = 'productDetailPlanCard',
    PRODUCT_DETAIL_PRIMARY_PRODUCT_CARD = 'productDetailPriProductCard',
    PRODUCT_DETAIL_SECONDARY_PRODUCT_CARD = 'productDetailSIMProductCard',
    PRODUCT_DETAIL_USAGE_SUMMARY = 'productDetailUsageSummary',
    USAGE_SUMMARY = 'USAGE_SUMMARY_BI',
    PRODUCT_DETAIL_QUICK_ACTIONS = 'productDetailQuickActions',
    CUST_ACCT = 'CUST_ACCT',
    BILL_ACCT = 'BILL_ACCT',
    CUST_ADDRESS = 'CUST_ADDRESS',
    CUST_CONTACT_MEDIUM = 'CUST_CONTACT_MEDIUM',
    CUST_DEMOGRAPHIC_INFO = 'CUST_DEMOGRAPHIC_INFO',
  }
}

export const enum WorkflowStateSpec {
  ATTACH_DOCUMENTS = 'attachDocuments',
  CREATE_CUSTOMER = 'createCustomer',
  IMEI_ENTRY = 'imeiEntry',
  MONTHLY_PAYMENT = 'monthlyPayment',
  NUMBER_SELECTION = 'numberSelection',
  OPTION_SELECTION = 'optionSelection',
  ORDER_CONFIRMATION = 'orderConfirmation',
  ORDER_INITIALIZE = 'orderInitialize',
  ORDER_SUBMIT = 'orderSubmit',
  ORDER_SUMMARY = 'orderSummary',
  PAYMENT_BILLING = 'paymentBilling',
  PRE_VALIDATION = 'preValidation',
  SCORING_ACTION = 'scoringAction',
  SIMCARD_CONFIGURATION = 'simCardConfiguration',
}
