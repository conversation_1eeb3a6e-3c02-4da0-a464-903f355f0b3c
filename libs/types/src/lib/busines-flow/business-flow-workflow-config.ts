import { eBusinessFlow, WorkflowStateSpec } from './business-flow.enum';
import { ProductData } from '@libs/bss';
import { Product } from '../product';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type ContextDataType<T extends eBusinessFlow.Specification> = T extends keyof ContextData ? ContextData[T] : any;

export interface ContextData {
  [eBusinessFlow.Specification.PURCHASE_ADDON]?: ProductData;
  [eBusinessFlow.Specification.PACKAGE_CHANGE]?: ProductData;
  [eBusinessFlow.Specification.SIMCARD_CHANGE]?: Product.ProductDetail;
}

export interface BusinessFlowWorkflowConfig {
  workflowStateSpecName: string;
  workflowStateSpecShortCode: eBusinessFlow.WorkflowStateType;
  visible: boolean;
  sortId: number;
}

export interface BusinessFlowWorkflowConfigExtended extends BusinessFlowWorkflowConfig {
  label: string;
  routerLink: string;
}

export interface BusinessWorkflowRouteConfigItem {
  routerLink: string;
  translationKey: string;
}

export type BusinessWorkflowRouteConfig = Partial<Record<WorkflowStateSpec, BusinessWorkflowRouteConfigItem>>;
