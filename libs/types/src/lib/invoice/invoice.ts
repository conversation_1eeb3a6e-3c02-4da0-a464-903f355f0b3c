import { eInvoice } from './eInvoice';

export namespace Invoice {
  export interface CustomerInvoiceDetail {
    billingAccountId: number;
    accountName: string;
    amount: number;
    paymentStatus: string;
    invoiceDate: Date;
    dueDate: Date;
    currencyCode: string;
  }

  export interface GetInvoiceInfoQueryParams {
    billingAccountId: number;
    startDate?: string;
    startDueDate?: string;
    endDate?: string;
    endDueDate?: string;
    paymentStatus?: string;
    status?: string;
    paymentNumber?: number;
    invoiceNumber?: number;
    isOrganizationalCustomer?: boolean;
  }

  export interface GetInvoiceInfoResponse {
    invoiceInfos: InvoiceInfoItem[];
  }

  export interface InvoiceInfo {
    invoiceDefinition: InvoiceDefinitionType[];
    summary: InvoiceInfoSummaryType;
  }

  export interface InvoiceInfoItem {
    invoiceInfo: InvoiceInfo[];
  }

  export interface InvoiceDefinitionType {
    billingAccountId: number;
    billingAccountName: string;
    billingPeriod: number;
    invoiceNumber: number;
    invoiceAmount: number;
    invoiceDate: string;
    dueAmount: number;
    dueDate: string;
    openAmount: number;
    totalPayableAmount: number;
    currencyCode: string;
    paymentStatus: string;
    invoiceStatus?: InvoiceStatus; //for FE
    invoiceExecDate: string;
    invoiceDocPath: string;
    detailInvoiceDocPath: string;
    invoiceRunType: string;
    detailDocumentId: string;
    billNo: string;
  }

  export interface InvoiceInfoSummaryType {
    totalDueAmount: number;
    totalOpenAmount: number;
    balance: number;
  }

  export interface InvoiceStatus {
    status: eInvoice.PaymentStatus;
    appearance: string;
    day?: string;
    isOverdue?: boolean;
    isPartiallyPaid?: boolean;
    isPaid?: boolean;
  }

  export interface InvoiceExternalPrice {
    title: string;
    amount: number;
    currencyCode?: string;
  }
  export interface DownloadURIResponse {
    downloadUri: string;
  }
}
