import { Auth } from './auth';
import { InjectionToken } from '@angular/core';
import { FeatureFlagEnum } from './enums';

export namespace Environment {
  export type EnvironmentKey = 'prod' | 'prod-local' | 'local';

  export interface Model extends ExternalEnvironment {
    environment: EnvironmentKey;
    production: boolean;
    app: 'csr' | 'wsc' | 'b2b-wsc';
  }

  export interface DateFormatSet {
    // Angular built-in formatlar - Date + Time
    short: string;
    medium: string;
    long: string;
    full: string;

    // Angular built-in formatlar - Sadece Date
    shortDate: string;
    mediumDate: string;
    longDate: string;
    fullDate: string;

    // Angular built-in formatlar - Sadece Time
    shortTime: string;
    mediumTime: string;
    longTime: string;
    fullTime: string;

    // Diğer custom formatlar için
    [key: string]: string;
  }

  type ExternalEnvironment = Partial<{
    gtm: string;
    ga4: GA4Env;
    languages: LanguageCodes[];
    defaultLanguage: string;
    url: EnvironmentUrls;
    keycloak: KeycloakEnv;
    faro: FaroEnv;
    localization: {
      currency: {
        code: string;
        display: 'code' | 'symbol' | 'symbol-narrow' | string | boolean;
        digits: string;
      };
      date: { [prop in LanguageCodes]?: Partial<DateFormatSet> };
      time: { [prop in LanguageCodes]?: Partial<DateFormatSet> };
    };
    routes: {
      private: '/private';
      public: '/public';
      slug: boolean;
    };
    registerApi: Register;
    backendApiDocs: string;
    isPhase3: boolean;
    featureFlags: FeatureFlags;
  }>;
  export type FeatureFlags = {
    [key in FeatureFlagEnum]?: boolean | undefined;
  };

  export interface EnvironmentUrls extends Record<string, string> {
    app: string;
    api: string;
    eca: string;
    cdn?: string;
    i18n: string;
    csr?: string;
    wsc?: string;
    b2b_wsc?: string;
    css?: string;
    font?: string;
    ws?: string;
    logo: string;
    view_dunning?: string;
    permission: string;
    images?: string;
    wfm?: string;
    dunningUrl?: string;
    commissionModuleLink?: string;
    chatbotUrl?: string;

    crm: string;
    'domain-config': string;
    'mash-up': string;
    cpq: string;
    'pcm-product-catalog': string;
    loyalty: string;
    'loyalty-integrator': string;
    'ntf-history': string;
  }

  export interface KeycloakEnv {
    config: Auth.Keycloak.ConfigOptions;
    init?: {
      checkLoginIframe: boolean;
      onLoad?: 'check-sso' | 'login-required';
      silentCheckSsoRedirectUri?: string;
    };
  }

  export type LanguageCodes = 'en' | 'ar' | 'fr' | 'fr-FR' | 'en-US';

  export interface Register {
    googleReCaptchaPublicKey: string;
    enableReCaptcha: boolean;
    googleMapsApiKey: string;
  }

  export interface FaroEnv {
    collectorUrl: string;
    appName?: string;
    appVersion?: string;
    environment?: string;
  }

  export interface GA4Env {
    trackingId: string;
    enabled?: boolean;
    config?: {
      anonymize_ip?: boolean;
      send_page_view?: boolean;
      custom_map?: Record<string, string>;
      [key: string]: unknown;
    };
  }

  export const ENVIRONMENT = new InjectionToken<Model>('environment');
}
