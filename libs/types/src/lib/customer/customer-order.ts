import { SimplePageRequest } from '../common';
import { Response } from '../http';

export namespace CustomerOrder {
  export type InquireCustomerOrdersParams = Partial<
    {
      createDateFilterInputStartDate: string;
      createDateFilterInputEndDate: string;
      updateDateFilterInputStartDate: string;
      updateDateFilterInputEndDate: string;
      submitDateFilterInputStartDate: string;
      submitDateFilterInputEndDate: string;
      customerId: number;
      customerOrderId: number;
      searchType: string;
      orderTypeShortCode: string;
      billingAccountId: number;
      subscriptionIdentifier: string;
      statusShortCode: string;
      pageName: string;
      submitSaleChannelShortCode: string;
      workOrganizationId: number;
      workOrganizationName: string;
      sortColumn: string;
      sortType: string;
    } & SimplePageRequest
  >;

  export type InquireCustomerOrdersResponse = Response.Result<InquireCustomerOrder>;

  export interface InquireCustomerOrder {
    customerId: number;
    customerOrderId: number;
    businessInteractionSpecification: RecordType;
    businessFlowSpecification: RecordType;
    businessInteractionId: number;
    orderStatus: RecordType;
    createDate: Date;
    createUser: CustomerOrderActionUser;
    updateDate: Date;
    submitDate: Date;
    submittedSaleChannel: RecordType;
    submitUser: CustomerOrderActionUser;
    displaySubOrder: boolean;
    orderDetailAllowed: boolean;
    orderCancelAllowed: boolean;
    orderContinueAllowed: boolean;
    preOrderCancelAllowed: boolean;
    referenceNo: string;
    planDevicePairs: PlanDevicePair[];
  }

  export interface RecordType {
    shortCode: string;
    name: string;
  }

  export interface CustomerOrderActionUser {
    userId: number;
    name: string;
    userName?: string;
  }

  export interface PlanDevicePair {
    planName: string;
    deviceName: string;
    type: string;
  }

  export const enum OrderStatus {
    QUOTE = 'QUOTE',
    ESB = 'ESB',
    FINISHED = 'FINISHED',
    FULL_FINISHED = 'FULL_FINISHED',
    HALF_FINISHED = 'HALF_FINISHED',
    CANCELLED = 'CANCELLED',
    DISPATCHED = 'DISPATCHED',
    REJECTED = 'REJECTED',
    ESB_PENDING = 'ESB_PENDING',
    EXPIRED = 'EXPIRED',
  }

  export enum SegmentType {
    ORDER = 'ORDER',
    QUOTES = 'QUOTES',
  }

  export enum SortType {
    ASC = 'ASC',
    DESC = 'DESC',
  }

  export enum SortColumn {
    SUBMIT_DATE = 'submitDate',
  }

  export enum SearchType {
    ORDER = 'order',
    SUB_ORDER = 'subOrder',
  }
}
