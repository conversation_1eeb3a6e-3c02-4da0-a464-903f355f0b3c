import { eBusinessFlow } from '../busines-flow';
import { RequestResponse } from '../http';

export namespace UsageSummary {
  export interface UsageSummaryRequest {
    billingAccountId: number;
    customerId: number;
    actionReasonCode: eBusinessFlow.Specification;
  }

  export interface UsageSummaryResponse {
    requestResponse: RequestResponse;
    abmProduct: UsageSummary[];
  }

  export interface UsageSummary {
    productId: number;
    packageName: string;
    balanceName: string;
    startDate: string;
    endDate: string;
    packageId: number;
    uomDefId: number;
    uomName: string;
    productKey: number;
    balKey: number;
    maxAmount: string;
    usedAmount: string;
    remainingAmount: string;
    coMaxAmount: string;
    coUsedAmount: string;
    coRemainingAmount: string;
    coEDate: string;
    bucketType: string;
  }
}
