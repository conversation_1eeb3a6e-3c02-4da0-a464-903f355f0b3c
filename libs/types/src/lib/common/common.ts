/**
 * @Deprecated - move these enums semantically to their own file
 */
export namespace eCommon {
  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum ResultCode {
    Success = 'SUCCESS',
    Info = 'INFO',
    Warning = 'WARNING',
    Error = 'ERROR',
    Waiting = 'WAITING',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum OperationResultProps {
    ResultCode = 'resultCode',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum BillingAccountDefaultFormValueEnum {
    BillingCycleVal = 'MONTHLY',
    LanguagePreference = 'en',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum PriceTypes {
    ONETIME = 'ONETIME',
    RECURRING = 'RECURRING',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum PartyTypeShortCode {
    INDV = 'INDV',
    ORG = 'ORG',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum ProductStatusShrtCode {
    PNDG = 'PNDG',
    SPND = 'SPND',
    CNCL = 'CNCL',
    DEL = 'DEL',
    QUOTE = 'QUOTE',
    ACTV = 'ACTV',
    PASS = 'PASS',
    FINISHED = 'FINISHED',
    OPEN = 'OPEN',
    DCTV = 'DCTV',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   * Moved BusinessFlow.Specification
   */
  export enum BusinessFlowSpecificationTypes {}

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum BusinessFlowSpecificationDisplayGroupTypeShortCodes {
    EQUIPMENT = 'EQUIPMENT',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum AddressType {
    COUNTRY = 'countryId',
    STATE = 'stateId',
    CITY = 'cityId',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum ProdChars {
    DEVICE_REPLACE_REASON = 'deviceReplaceReason',
    PERIOD_TP = 'PERIOD_TP', // Period Type
    PERIOD_START_COUNT = 'PERIOD_START_COUNT',
    PERIOD_END_COUNT = 'PERIOD_END_COUNT',
    VALUE = 'VALUE',
    CALL_FORWARD = 'callForward',
    CALL_FORWARD_NUMBER = 'callForwardNumber',
    CALL_WAIT = 'callWait',
    CALL_HOLD = 'callHold',
    CONFERENCE_CALL = 'conferenceCall',
    IS_PRE_ORDER = 'isPreOrder',
    LAUNCH_DATE = 'launchDate',
    PERIOD_LEN = 'PERIOD_LEN',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum QuoteChars {
    SHIPMENT = 'Shipment',
    PORTABILITY_PROVIDER_ID = 'portabilityProviderId',
    PORTABILITY_PROVIDER_NAME = 'portabilityProviderName',
    ACTION_REASON_CODE = 'actionReasonCode',
    TOP_UP = 'topUpAmount',
    PREDEFINED_TOP_UP_AMOUNT_LIST = 'predefinedTopUpAmountList',
    SUBSCRIPTION_IDENTITY_CHAR_NAME = 'subscriptionIdentityCharName',
    SUBSCRIPTION_IDENTITY_CHAR_VALUE = 'subscriptionIdentityCharValue',
    IS_SAME_INVOICE = 'isSameInvoice',
    IS_SAME_INVOICE_YES = 'isSameInvoiceYes',
    IS_SAME_INVOICE_NO = 'isSameInvoiceNo',
    IS_CREATE_NEW_INVOICE = 'isCreateNewInvoice',
    IS_CREATE_NEW_INVOICE_NO = 'isCreateNewInvoiceNo',
    IS_SEPARATE_BILLING_ADDRESS = 'isSeparateBillingAddress',
    IS_SEPARATE_BILLING_ADDRESS_NO = 'isSeparateBillingAddressNo',
    IS_CREATE_NEW_INVOICE_YES = 'isCreateNewInvoiceYes',
    MULTI_PRODUCT_DELIVERY_ORDER = 'multiProductDeliveryOrder',
    IS_SEPARATE_BILLING_ADDRESS_YES = 'isSeparateBillingAddressYes',
    CUSTOMER_CREDIT_SCORE_CONSENT_AUTHORIZED = 'customerCreditScoreConsentAuthorized',
    SERVICE_TYPE = 'serviceType',
    CONTRACT_TYPE = 'contractType',
    PAY_BILL_AMOUNT = 'payBillAmount',
    MSISDN_FOR_PAY_BILL_ANONYM = 'msisdnForPayBillAnonym',
    BIRTH_DATE_FOR_PAY_BILL_ANONYM = 'birthDateForPayBillAnonym',
    FIRST_NAME_FOR_PAY_BILL_ANONYM = 'firstNameForPayBillAnonym',
    LAST_NAME_FOR_PAY_BILL_ANONYM = 'lastNameForPayBillAnonym',
    PAY_BILL_AMOUNT_FOR_PAY_BILL_ANONYM = 'payBillAmountForPayBillAnonym',
    INTERACTION_FEE_PAYMENT_TIME_NEXT_INVOICE = 'interactionFeePaymentTimeNextInvoice',
    SERVICE_SUSPEND_DURATION = 'serviceSuspendDuration',
    SERVICE_PAUSE_DURATION = 'servicePauseDuration',
    FORWARD_DATED = 'forwardDated',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum Deactivation {
    RENTAL = 'rental',
    CURRENT_BILL_CYCLE_END = 'currentBillCycleEnd',
    PLAN_BUNDLE_DEACTIVATION_DATE_SELECTION = 'planBundleDeactivationDateSelection',
    IS_REMAINING_INSTALLMENTS_CHARGED = 'isRemainingInstallmentsCharged',
    IMMEDIATE = 'immediate',
    ADD_ON_DEACTIVATION_DATE_SELECTION = 'addonDeactivationDateSelection',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum ServiceSpecKeyServiceCodes {
    TVBB_INSTALLATION_CFS = 'tvBbInstallationCfs',
    SHIPMENT_CFS = 'shipmentCfs',
    APPOINTMENT_CFS = 'appointmentCfs',
    PICKUP_IN_STORE_CFS = 'pickupInStoreDeliveryCfs',
    SELF_INSTALLATION_CFS = 'selfInstallationCfs',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum ActionCodes {
    ACTIVATION = 'ACTIVATION',
    DEACTIVATION = 'DEACTIVATION',
    REACTIVATION = 'REACTIVATION',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum AccountType {
    BILL_ACCT = 'BILL_ACCT',
    CUST = 'CUST',
    CUST_360_INFO = 'CUST_360_INFO',
    CUST_CONTACT_MEDIUM = 'CUST_CONTACT_MEDIUM',
    CUST_ADDRESS = 'CUST_ADDRESS',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum EmployeeType {
    EMPLOYEE_INFO = 'EMPLOYEE_INFO',
    UPDATE_EMP = 'UPDATE_EMP',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum DeviceType {
    MOBILE_DEVICE = 'mobileDevice',
    SATELLITE_CONNECTION_DEVICE = 'satelliteConnectionDevice',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum PartyPrivacySpecificationRoleType {
    CUST = 'CUST',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum BusinessInteractionLevelType {
    SINGLE_OFFER = 'SINGLE_OFFER',
    PLAN_BUNDLE = 'PLAN_BUNDLE',
    BILL_ACCT = 'BILL_ACCT',
    CUST = 'CUST',
    CUST_ACCT = 'CUST_ACCT',
    SUB_OFFER = 'SUB_OFFER',
    ORG_BILL_ACCT_ADDR = 'ORG_BILL_ACCT_ADDR',
    ORG_BILL_ACCT_PHONE = 'ORG_BILL_ACCT_PHONE',
    ORG_BILL_ACCT_EMAIL = 'ORG_BILL_ACCT_EMAIL',
    ORG_BILL_ACCT_BASIC = 'ORG_BILL_ACCT_BASIC',
    ORG_BILL_ACCT_TAX_EXEMPT = 'ORG_BILL_ACCT_TAX_EXEMPT',
    ORG_CONTACT_COMM_ADDRESS = 'ORG_CONTACT_COMM_ADDRESS',
    ORG_CONTACT_COMM_EMAIL = 'ORG_CONTACT_COMM_EMAIL',
    ORG_CONTACT_COMM_PHONE = 'ORG_CONTACT_COMM_PHONE',
    ORG_PERSONAL_CONTACT_EMAIL = 'ORG_PERSONAL_CONTACT_EMAIL',
    ORG_PERSONAL_CONTACT_PHONE = 'ORG_PERSONAL_CONTACT_PHONE',
    ORG_PERSONAL_CONTACT_ADDRESS = 'ORG_PERSONAL_CONTACT_ADDRESS',
    ORG_CONTACT_BASIC_INFO = 'ORG_CONTACT_BASIC_INFO',
    ORG_CONTACT_DEFINITION = 'ORG_CONTACT_DEFINITION',
    ORG_CUST_CONSENTS = 'ORG_CUST_CONSENTS',
    ORG_CONTR_DTL = 'ORG_CONTR_DTL',
    ORG_CUST_DEMOG_INFO = 'ORG_CUST_DEMOG_INFO',
    ORG_CUST_BASIC_INFO = 'ORG_CUST_BASIC_INFO',
    ORG_CUST_COMM_ADDRESS = 'ORG_CUST_COMM_ADDRESS',
    ORG_CUST_COMM_EMAIL = 'ORG_CUST_COMM_EMAIL',
    ORG_CUST_COMM_WEBSITE = 'ORG_CUST_COMM_WEBSITE',
    ORG_CUST_COMM_SCLMEDIA = 'ORG_CUST_COMM_SCLMEDIA',
    ORG_CUST_COMM_PHONE = 'ORG_CUST_COMM_PHONE',
    ORG_CUST_REG_INFO = 'ORG_CUST_REG_INFO',
    PLAN_BUNDLE_B2B = 'PLAN_BUNDLE_B2B',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum EntityDataType {
    SRVC_SPEC = 110,
    PROD_OFR = 40,
    CUST = 12,
    CUST_ACCT = 13,
    CREDIT_CARD = 44000,
    PARTY = 1,
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum OrganizationBulkOrderStatus {
    DELIVERED = 'delivered',
    ERROR = 'error',
    SUCCESS = 'success',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ProductFamilyShortCodes {
    INTERNET = 'INTERNET',
    ENTERTAINMENT = 'ENTERTAINMENT',
    TMO = 'TMO',
    FIXED_LTE = 'FIXED_LTE',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ProductFamilyCategoryShortCodes {
    DATA_BUCKET = 'dataBucket',
    SMS_BUCKET = 'smsBucket',
    VOICE_BUCKET = 'voiceBucket',
    MOBILE_BROADBAND_PLAN_DATA = 'mobileBroadbandPlanDataFreeUnits',
    FIXED_LTE_VOICE_BUCKET = 'fixedLteVoiceBucket',
    FIXED_LTE_DATA_BUCKET = 'fixedLteDataBucket',
    MOBILE = 'mobile',
    DELIVERY = 'delivery',
    FIBER_INTERNET_DATA_BUCKET = 'fiberInternetDataBucket',
    DATA_ADDON = 'dataAddon',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum QuoteOperationType {
    ADD = 'ADD',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum InvolvementEditorListCreationStrategyType {
    FULL = 'FULL',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ResourceSpecKeyResourceCode {
    MOBILE_PHONE_RS = 'mobilePhoneRs',
    MOBILE_BROADBAND_HOT_SPOTS_RS = 'mobileBroadbandHotspotRs',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum ModuleType {
    CSR = 'CSR',
    WSC = 'WSC',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum BillingAccountStatus {
    ACTV = 'ACTV',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ProductCharType {
    MSISDN = 'msisdn',
    FDN = 'fdn',
    IS_BYOD = 'isBYOD',
    ICCID = 'iccid',
    IS_ESIM_COMPATIBLE = 'isESimCompatible',
    IS_PHYSICAL_SIM_COMPATIBLE = 'isPhysicalSimCompatible',
    COLOR = 'color',
    INSTALLMENT_OPTIONS = 'installmentOptions',
    SIM_TYPE = 'simType',
    SIM_CARD_TYPE = 'simCardType',
    SIM_CARD_CATEGORY = 'simCardCategory',
    RESOLUTION = 'resolution',
    PORTABILITY_CFS = 'portabilityCfs',
    PORTABILITY_PROVIDER_NAME = 'portabilityProviderName',
    ISPREMATCHED = 'isPrematched',
    IMSI = 'imsi',
    SUBSCRIPTION_IDENTITY = 'subscriptionIdentity',
    IS_STANDALONE_PURCHASE = 'isStandalonePurchase',
    MODEL = 'model',
    MOBILE_BRAND = 'mobileBrand',
    CAPACITY = 'capacity',
    IMEI = 'imei',
    SERIAL_NUMBER = 'serialNumber',
    DEVICE_TYPE = 'deviceType',
    LASTNAME = 'lastName',
    FIRSTNAME = 'firstName',
    SERVICE_ADDRESS = 'serviceAddress',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum WellKnownChars {
    DEVICE_REPLACE_REASON = 'deviceReplaceReason',
    DISC_TYPE = 'discType',
    LCA = 'lca',
    LRN = 'lrn',
    MSISDN = 'msisdn',
    NAME = 'name',
    OLD_MSISDN = 'oldMsisdn',
    OLD_FDN = 'oldFdn',
    REUSE_OLD_MSISDN = 'reuseOldMsisdn',
    REUSE_OLD_FDN = 'reuseOldFdn',
    FIXED_LTE_FDN = 'fixedLteFdn',
    FIXED_LTE_DEVICE = 'fixedLteDevice',
    ESIMRS = 'esimRS',
    PORTABILITY_QUALIFICATION_API_ERROR = 'PORTABILITY_QUALIFICATION_API_ERROR',
    IS_EXPLICIT_MSISDN_SELECTED = 'isExplicitMsisdnSelected',
    REMAINING_INSTALLMENTS_CHARGED_TIME = 'remainingInstallmentsChargeTime',
    ACCOUNT_NUMBER = 'accountNumber',
    MOBILE_BROADBAND_BYOD_HOTSPOT = 'mobileBroadbandByodHotspot',
    MOBILE_BROADBAND_HOTSPOT = 'mobileBroadbandHotspot',
    MOBILE_BROADBAND_BUNDLE = 'mobileBroadbandBundle',
    MOBILE_BYOD = 'mobileByod',
    FIXED_LTE_BYOD = 'fixedLteByod',
    MOBILE_DEVICE = 'mobileDevice',
    IMEI = 'imei',
    SERIAL_NUMBER = 'serialNumber',
    COMMITMENT = 'commitment',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum DeviceNameParts {
    CAPACITY = 'capacity',
    MOBILE_BRAND = 'mobileBrand',
    COLOR = 'color',
    MODEL = 'model',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum WellKnownCharValues {
    FIRST_INVOICE = 'FIRST_INVOICE',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum CustomerOrderItemActionType {
    ACTIVATION = 'ACTIVATION',
    MODIFY = 'MODIFY',
    DEACTIVATION = 'DEACTIVATION',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   * Moved to Offer namespace
   */
  export enum OfferChargeType {
    USAGE = '3',
    ONETIME = '1',
    RECURRING = 'RECURRING',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ProdOfferCharGroupShortCodes {
    PLAN_MARKETING_BULLETS = 'planMarketingBullets',
    PLAN_BUCKET_CHARS = 'planBucketChars',
    FIXED_LTE_PLAN_MARKETING_BULLETS = 'fixedLtePlanMarketingBullets',
    PLAN_COMMITMENT_CHARS = 'planCommitmentChars',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum AlternateProductOffering {
    NEW_OFFER = 'newOffer',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ProdOrderTransitionType {
    DOWNSELL = 'DOWNSELL',
    UPSELL = 'UPSELL',
    ACQUISITION = 'acquisition',
    PORTABILITY = 'portability',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum AddressFieldDisplayType {
    LIST = 'LIST',
    STR = 'STR',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum AddressFieldType {
    ALIAS = 'ALIAS',
    ADDR_CNTC = 'ADDR_CNTC',
    CNTRY = 'CNTRY',
    STATE = 'STATE',
    CITY = 'CITY',
    BLDG_NUMBER = 'BLDG_NUMBER',
    PSTL_CODE = 'PSTL_CODE',
    LABEL = 'LABEL',
    TYPE = 'TYPE',
    STRT_NR_SUFFIX = 'STRT_NR_SUFFIX',
    PHONE_NUMBER = 'PHONE_NUMBER',
    DESC2 = 'DESC2',
    DESC = 'DESC',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum TippyTypes {
    DROPDOWN = 'dropdown',
    HEADER_DROPDOWN = 'header-dropdown',
    TOOLTIP = 'tooltip',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum TippyTriggerTypes {
    MOUSE_ENTER_FOCUS = 'mouseenter focus',
    FOCUS_IN = 'focusin',
    MOUSE_ENTER_CLICK = 'mouseenter click',
    CLICK = 'click',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum QuoteType {
    SIM_CARD_CHANGE = 'SIM_CARD_CHANGE',
    MOBILE_NUMBER_CHANGE = 'MOBILE_NUMBER_CHANGE',
    REACTIVATION = 'REACTIVATION',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ServiceAddressSelectionType {
    NEW_ADDRESS = 'NEW_ADDRESS',
    NEW_CUSTOMERS_EXISTING_ADDRESS = 'NEW_CUSTOMERS_EXISTING_ADDRESS',
    EXISTING_SERVICE_ADDRESS = 'EXISTING_SERVICE_ADDRESS',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum PortabilityStatus {
    COMPLETED = 'completed',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum CatalogGroupType {
    PLAN = 'plan',
    DEVICE = 'device',
    ADDON = 'addon',
    STANDALONE_DEVICE = 'standaloneDevice',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum CatalogGroupServiceType {
    MOBILE = 'mobile',
    INTERNET = 'internet',
    MOBILE_BROADBAND = 'mobileBroadband',
    FIBER_INTERNET = 'fiberInternet',
    SATELLITE_CONNECTION = 'satelliteConnection',
    FIXED_LTE = 'fixedLte',
    IS_STANDALONE_DEVICE = 'stndaloneDevice',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum CatalogShortCode {
    SIM_CARD = 'simCard',
    FIXED_LTE_SIM_CARD = 'fixedLteSimCard',
    MOBILE = 'mobile',
    MOBILE_BROADBAND = 'mobileBroadband',
    FIXED_LTE = 'fixedLte',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ProductOfferGroupShortCode {
    FIBER_INTERNET_PREPAID_PLANS = 'fiberInternetPrepaidPlans',
    ORG_CONTRACT_COMMITMENT_TERMS = 'orgContrCmtmntTerms',
    BYOD_DEVICES = 'byodDevices',
    BROADBAND_DEVICES = 'broadbandDevices',
    TABLET_DEVICES = 'tabletDevices',
    STANDART_DEVICES = 'standartDevices',
    SMARTPHONE_DEVICES = 'smartphoneDevices',
    ORG_CONTRACT_PROD_OFR_GRP = 'orgContractProdOfrGrp',
    ORG_CONTR_DEVICES = 'orgContrDevices',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum PriceItemType {
    DISCOUNT_APPLIED_PRICE = 'discountAppliedPrice',
    REGULAR_PRICE = 'regularPrice',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ProductOfferNotCode {
    MIN_CARDINALITY = 'MinCardinality',
    MAX_CARDINALITY = 'MaxCardinality',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum DisplayGroupShortCode {
    EXCLUSIVE_ADDON = 'exclusiveAddon',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum CustomerOrderItemType {
    DEACTIVATED = 'deactivated',
    ACTIVATED = 'activated',
    MODIFIED = 'modified',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum BillingContent {
    SINGLE_ADDRESS = 'singleAddress',
    ADDRESS_SAME = 'addressSame',
    ADDRESS_SEPARATE = 'addressSeparate',
    ADDRESS_SAME_AND_SEPARATE = 'addressSameAndSeparate',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum SimpleProductCharShortCode {
    SECONDARY_DISPLAY_CHARS = 'secondaryDisplayChars',
    TERTIARY_DISPLAY_CHARS = 'tertiaryDisplayChars',
    PRIMARY_DISPLAY_CHARS = 'primaryDisplayChars',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ExternalShortCode {
    ACTIVE = 'Active',
    PENDING = 'Pending',
    TERMINATED = 'Terminated',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum AlertType {
    INFO = 'info',
    SUCCESS = 'success',
    WARNING = 'warning',
    DANGER = 'danger',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum BsnInterLvlShrtCode {
    CUST_DEMOGRAPHIC_INFO = 'customerDemographicInfo',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ContactMediumType {
    PHONE = 'PHONE',
    EMAIL = 'EMAIL',
    ADDRESS = 'ADDRESS',
    SOCIAL_MEDIA = 'SOCIAL_MEDIA',
    WEBSITE = 'WEBSITE',
    CO_WEB_SITE = 'CO_WEB_SITE',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum AddEditEnum {
    'ADD' = '1',
    'EDIT' = '2',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum AccountCreateTypeEnum {
    MAIN_BILLING_ACCOUNT,
    CHILD_BILLING_ACCOUNT,
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum TicketType {
    TICKET_CREATE = 'ticketCreate',
    TICKET_DETAIL = 'ticketDetail',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum DropDownLabelEnum {
    Update_Billing_Account_Name = 'Update Billing Account Name',
    Update_Billing_Account_Info = 'Update Billing Account Info',
    Update_User_Basic_Info = 'Update User Basic Info',
    Update_Party_Basic_Info = 'Update Party Basic Info',
    Edit = 'Edit',
    Delete = 'Delete',
    Download = 'Download',
    Manage = 'Manage',
    Update_Demographic_Information = 'Update Demographic Information',
    Update_Customer_Basic_Information = 'Update Customer Basic Information',
    Update_Registration_Name = 'Update Registration Name',
    Update_Registration_Information = 'Update Registration Information',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum OfferType {
    POCOM = 'POCOM',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum BsnInterSpecShortCode {
    ADDR_CREATE = 'ADDR_CREATE',
    ADDR_UPDATE = 'ADDR_UPDATE',
    ADDR_REMOVE = 'ADDR_REMOVE',
    CREATE_CNTC_MEDIUM = 'CREATE_CNTC_MEDIUM',
    RMV_CNTC_MEDIUM = 'RMV_CNTC_MEDIUM',
    UPDT_CNTC_MEDIUM = 'UPDT_CNTC_MEDIUM',
    ORG_BILL_ACCT_ADDR_UPD = 'ORG_BILL_ACCT_ADDR_UPD',
    ORG_BILL_ACCT_ADDR_DEL = 'ORG_BILL_ACCT_ADDR_DEL',
    ORG_BILL_ACCT_CNTC_MD_UPD = 'ORG_BILL_ACCT_CNTC_MD_UPD',
    ORG_BILL_ACCT_CNTC_MD_DEL = 'ORG_BILL_ACCT_CNTC_MD_DEL',
    ORG_BILL_ACCT_NAME_UPD = 'ORG_BILL_ACCT_NAME_UPD',
    ORG_BILL_ACCT_BASIC_INFO_UPD = 'ORG_BILL_ACCT_BASIC_INFO_UPD',
    ORG_BILL_ACCT_TAX_EXEMPT_UPD = 'ORG_BILL_ACCT_TAX_EXEMPT_UPD',
    ORG_BILL_ACCT_TAX_EXEMPT_DEL = 'ORG_BILL_ACCT_TAX_EXEMPT_DEL',
    ORG_BILL_ACCT_TAX_EXEMPT_DWNLD = 'ORG_BILL_ACCT_TAX_EXEMPT_DWNLD',
    ORG_CONTACT_ADDR_UPD = 'ORG_CONTACT_ADDR_UPD',
    ORG_CONTACT_ADDR_DEL = 'ORG_CONTACT_ADDR_DEL',
    ORG_CONTACT_CNTC_MD_UPD = 'ORG_CONTACT_CNTC_MD_UPD',
    ORG_CONTACT_CNTC_MD_DEL = 'ORG_CONTACT_CNTC_MD_DEL',
    ORG_PERSONAL_ADDR_UPD = 'ORG_PERSONAL_ADDR_UPD',
    ORG_PERSONAL_ADDR_DEL = 'ORG_PERSONAL_ADDR_DEL',
    ORG_PERSONAL_CNTC_MD_UPD = 'ORG_PERSONAL_CNTC_MD_UPD',
    ORG_PERSONAL_CNTC_MD_DEL = 'ORG_PERSONAL_CNTC_MD_DEL',
    ORG_CONTACT_BASIC_INFO_UPD = 'ORG_CONTACT_BASIC_INFO_UPD',
    ORG_CONTACT_DEFINITION_UPD = 'ORG_CONTACT_DEFINITION_UPD',
    ORG_CUST_CONSENTS_UPDATE = 'ORG_CUST_CONSENTS_UPDATE',
    ORG_CONTR_UPD = 'ORG_CONTR_UPD',
    ORG_CONTR_END_DATE_UPD = 'ORG_CONTR_END_DATE_UPD',
    ORG_CONTR_SEC_AMNT_UPD = 'ORG_CONTR_SEC_AMNT_UPD',
    ORG_CONTR_STATUS_UPD = 'ORG_CONTR_STATUS_UPD',
    ORG_CONTR_ISSUE = 'ORG_CONTR_ISSUE',
    ORG_CUST_DEMOG_INFO_UPD = 'ORG_CUST_DEMOG_INFO_UPD',
    ORG_CUST_BASIC_INFO_UPD = 'ORG_CUST_BASIC_INFO_UPD',
    ORG_CUST_NAME_UPD = 'ORG_CUST_NAME_UPD',
    ORG_CUST_ADDR_UPD = 'ORG_CUST_ADDR_UPD',
    ORG_CUST_ADDR_DEL = 'ORG_CUST_ADDR_DEL',
    ORG_CUST_CNTC_MD_UPD = 'ORG_CUST_CNTC_MD_UPD',
    ORG_CUST_CNTC_MD_DEL = 'ORG_CUST_CNTC_MD_DEL',
    ORG_CUST_REG_INFO_UPD = 'ORG_CUST_REG_INFO_UPD',
    CUSTOMER_DEMOGRAPHIC_EDIT = 'customerDemographicEdit',
    CUSTOMER_DEMOGRAPHIC_VIEW = 'customerDemographicView',
    CUSTOMER_PROFILE = 'customerProfile',
    CUSTOMER_DEMOGRAPHIC_INFO = 'customerDemographicInfo',
    UPDT_CUST_DEMOGR_INFO = 'UPDT_CUST_DEMOGR_INFO',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ProductIconClassName {
    PHONE = 'phone',
    RADIO = 'radio',
    MESSAGE = 'message-square',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum UserRole {
    EMP = 'EMP',
    CUST = 'CUST',
    CSR = 'CSR',
    USER = 'USER',
    ANONYM = 'ANONYM',
    ADMIN = 'ADMIN',
    ANON_USER = 'ANON_USER',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ActivityType {
    VIEW_CUST = 'VIEW_CUST',
    BSN_INTER = 'BSN_INTER',
    REAL_SALE = 'REAL_SALE',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum ProductConsts {
    PLAN = 'plan',
    MSISDN = 'msisdn',
    SUB_OFFER = 'subOffer',
    DEVICE = 'device',
    MOBILE_BYOD = 'mobileByod',
    MOBILE = 'mobile',
    ADDON = 'addOn',
    PLAN_BUNDLE = 'planBundle',
    SUBSCRIPTON_IDENTITY = 'subscriptionIdentity',
    ICCID = 'iccid',
    RESOURCE_FACING_SERVICE = 'resourceFacingService',
    COMMITMENT = 'commitment',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum ProductFamilyCategoryEnum {
    SIMCARD = 'simCard',
    MOBILEBYOD = 'mobileByod',
    MOBILE = 'mobile',
    TMO = 'TMO',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum EntityCodeNameEnum {
    ACCT_BILL_PRFL_BILL_HNDL_TP = 'ACCT_BILL_PRFL_BILL_HNDL_TP',
    NTF_TP = 'NTF_TP',
    INV_TP = 'INV_TP',
    ORG_CONTR_TP = 'ORG_CONTR_TP',
    PARTY_OCCP_TP = 'PARTY_OCCP_TP',
    GENDER_TP = 'GENDER_TP',
    UNION = 'UNION',
    CUST_EMPLOYER = 'CUST_EMPLOYER',
    PROD = 'PROD',
    EMP = 'EMP',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum EntityNameEnum {
    ACCT_BILL_PRFL = 'ACCT_BILL_PRFL',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum DocumentShortCode {
    TAX_EXEMPT_PROOF = 'TAX_EXEMPT_PROOF',
    ORG_CUST_LEGAL_DOC = 'ORG_CUST_LEGAL_DOC',
    ORG_CONTR = 'ORG_CONTR',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum AddressUsageTypes {
    LEGAL_ADDR = 'LEGAL_ADDR',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum CompanySizeTypes {
    TOTAL_EMPLOYEE = 'TOTAL_EMPLOYEE',
    CNTC_TYPE = 'CNTC_TYPE',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum B2BCustomerSearchPageType {
    RELATE_CUSTOMER_ACCOUNT_CONTACT = 'relate-customer-account-contact',
    RELATE_BILLING_ACCOUNT_CONTACT = 'relate-billing-account-contact',
    CUSTOMER_SEARCH = 'customer-search',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum CustomerSearchFilterType {
    CUSTOMER = 'customer',
    ORDER = 'order',
    END_USER = 'end-user',
    ORGANIZATIONAL_CUSTOMER = 'organizationalCustomer',
    ORGANIZATIONAL_CUSTOMER_CONTACT = 'organizationalCustomerContact',
    ORGANIZATIONAL_CONTRACT = 'organizationalContract',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum GeneralParameter {
    CUSTOMER_MIN_AGE = 'CUSTOMER_MIN_AGE',
    REQUIRED_LEGAL_DOCUMENT = 'REQUIRED_LEGAL_DOCUMENT',
    DELIVERY_GROUP_TARGET_DATE_MIN_WORKDAY = 'DELIVERY_GROUP_TARGET_DATE_MIN_WORKDAY',
    BULK_ORDER_COPY_UPLOAD_MAX = 'BULK_ORDER_COPY_UPLOAD_MAX',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum AtomFieldTypes {
    TEXT_BOX = 'textbox',
    SELECT_BOX = 'select',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum SatelliteLocation {
    LOCATION1 = 'satelliteLocation1',
    LOCATION2 = 'satelliteLocation2',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum CharGroupGeneralChars {
    PRODUCT_SEARCH_CRITERIA = 'productSearchCriteria',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum CurrentValueDefaultCharValues {
    EMPTY = 'EMPTY',
    NULL = 'NULL',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum UserStatusShrtCode {
    CNCL = 'CNCL',
    DEL = 'DEL',
    ACTV = 'ACTV',
    PASS = 'PASS',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum CustomerProductDataType {
    CUSTOMER_PRODUCT_DATA = 'customerProductData',
    END_USER_PRODUCT_DATA = 'endUserProductData',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum DiscountType {
    PERCENTAGE = 'PERCENTAGE',
    FIXED = 'FIXED',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum GroupChars {
    INCLUDE_OFFER_DISPLAY_GROUP_CHARS = 'includeOfferDisplayGroupChars',
    INCLUDE_EXPO_GROUP_CHARS = 'includeExpoGroupChars',
    BSN_FLOW_SPEC = 'bsnFlowSpec',
    CATALOG_GROUP_TYPE = 'catalogGroupType',
    IS_ORGANIZATION_CONTRACT_RELATED = 'isorganizationContractRelated',
    ORGANIZATION_CONTRACT_ID = 'organizationContractId',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum View360_TabName {
    HOME = 'home',
    GENERAL_INFORMATION = 'general-information',
    PRODUCT_AND_SERVICE = 'product-and-services',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum Product {
    IMEI = 'imei',
    FIRSTNAME = 'firstName',
    LASTNAME = 'lastName',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum PaymentHistoryType {
    BILLING_HISTORY = 'billingHistory',
    PAYMENT_HISTORY = 'paymentHistory',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum CustomerDataRequest {
    CUST_REQUEST_DATA = 'CUST_REQUEST_DATA',
    PERS_DATA_EXP_TYPE = 'PERS_DATA_EXP_TYPE',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum ContactType {
    FULL_PERMISSION = 'fullPermission',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export enum ContactMediumTypes {
    PHONE = 'phoneType',
    EMAIL = 'emailType',
    SOCIAL_MEDIA = 'socialMediaType',
    WEBSITE = 'websiteType',
  }

  /**
   * @Deprecated - move these enums semantically to their own file
   */
  export const enum DeviceRequestType {
    INCLUDE_SEARCHABLE_CHARS = 'includeSearchableChars',
    INCLUDE_OFFER_DISPLAY_GROUP_CHARS = 'includeOfferDisplayGroupChars',
    CATALOG_GROUP_TYPE = 'catalogGroupType',
    INCLUDE_OFFER_LABEL_CHARS = 'includeOfferLabelChars',
    OFFER_LABEL_CHAR = 'offerLabelChar',
  }
}
