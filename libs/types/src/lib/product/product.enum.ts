export namespace eProduct {
  export enum ProductStatusShortCodes {
    PNDG = 'PNDG',
    SPND = 'SPND',
    CNCL = 'CNCL',
    DEL = 'DEL',
    QUOTE = 'QUOTE',
    ACTV = 'ACTV',
    PASS = 'PASS',
    FINISHED = 'FINISHED',
    OPEN = 'OPEN',
    DCTV = 'DCTV',
  }

  export enum CatalogShortCode {
    SIM_CARD = 'simCard',
    FIXED_LTE_SIM_CARD = 'fixedLteSimCard',
    MOBILE = 'mobile',
    MOBILE_BROADBAND = 'mobileBroadband',
    FIXED_LTE = 'fixedLte',
  }

  export enum SimpleProductCharShortCode {
    SECONDARY_DISPLAY_CHARS = 'secondaryDisplayChars',
    TERTIARY_DISPLAY_CHARS = 'tertiaryDisplayChars',
    PRIMARY_DISPLAY_CHARS = 'primaryDisplayChars',
  }

  export const enum ProductConsts {
    PLAN = 'plan',
    MSISDN = 'msisdn',
    SUB_OFFER = 'subOffer',
    DEVICE = 'device',
    MOBILE_BYOD = 'mobileByod',
    MOBILE = 'mobile',
    ADDON = 'addOn',
    PLAN_BUNDLE = 'planBundle',
    ICCID = 'iccid',
    RESOURCE_FACING_SERVICE = 'resourceFacingService',
    COMMITMENT = 'commitment',
    SUBSCRIPTION_IDENTITY = 'subscriptionIdentity',
    BUCKET = 'bucket',
  }

  export enum ProductCharTypes {
    DATA_AMOUNT = 'dataAmount',
    VOICE_AMOUNT = 'voiceAmount',
    SMS_AMOUNT = 'smsAmount',
    UPLOAD_SPEED = 'uploadSpeed',
    DOWNLOAD_SPEED = 'downloadSpeed',
    SPEED = 'speed',
    MSISDN = 'msisdn',
  }
}
