import { GeneralStatus } from '../common';
import { Address } from '../common';
import { GeneralType } from '../common';
import { NoModelYet } from '../common';
import { RequestResponse, Response } from '../http';

export namespace BillingAccount {
  export type IntegrationBillingAccountResponse = Response.Result<BillingAccount>;

  export interface BillingAccount {
    billingAddress: Address;
    billingAccountId: number;
    accountNumber: number;
    accountName: string;
    accountStatus: GeneralType;
    accountSubStatus: NoModelYet[];
    paymentType?: GeneralType;
    parentBillingAccount?: BillingAccount;
    accountSubType?: NoModelYet;
  }

  export interface InvoicingBillingAccount {
    billingAccountId: number;
    customerId: number;
    accountNumber: string;
    accountName: string;
    billingAddress: Address;
    accountStatus: GeneralStatus;
  }

  export interface InquireInstallment {
    requestResponse: RequestResponse;
    installmentInfo: InstallmentInfo[];
  }

  export interface InstallmentInfo {
    billAcctId: number;
    productId: number;
    installmentNumber: number;
    currentInstallment: number;
    amount: number;
    taxCategoryId: string;
    installmentStartDate: Date;
    installmentEndDate: Date;
    totalTaxAmount: number;
  }

  export interface BillingAccountParams {
    accountType?: string;
    customerId?: number;
    offset?: number;
    limit?: number;
  }
}
