import { GeneralType, GeneralStatus } from '../common';
import { AccountSubStatus } from '../common/account-sub-status';
import { Address } from '../common/address';
import { ProductCharValue } from '../product';

export interface BillingAccountInfo {
  billingAccountId?: number;
  accountNumber?: string;
  accountName?: string;
  paymentType?: GeneralType;
  billingAddress?: Address;
  billCycle?: number;
  billHandlingType?: GeneralType;
  separateInvoice?: boolean;
  accountStatus?: GeneralStatus;
  accountSubStatus: AccountSubStatus[];
  childBillingAccounts: BillingAccountInfo[];
  subscriptionIdentities: ProductCharValue[];
  serviceAddress?: Address;
  productStatus?: GeneralStatus;
  productType?: string;
  planName?: string;
}
