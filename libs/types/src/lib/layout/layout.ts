export enum LayoutTypeEnum {
  DEFAULT = 'default',
  DETAIL = 'detail',
  PROFILE = 'profile',
  CMS = 'cms',
  MEMBERSHIP = 'membership',
  CHECKOUT = 'checkout',
  CART = 'cart',
  ACCOUNT = 'account',
}

export enum SubLayoutTypeEnum {
  DEFAULT = 'default',
  DOUBLE_COLUMNS = 'double-columns',
  WITH_ASIDE = 'with-aside',
}

export enum LayoutHandlerEnum {
  SERVICE = 'service',
  ROUTER = 'router',
}

export enum LayoutDetailType {
  PRODUCT = 'PRODUCT',
  ORDER_AND_QUOTE = 'ORDER_AND_QUOTE',
}
