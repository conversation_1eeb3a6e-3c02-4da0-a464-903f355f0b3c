import { BillingInfo } from '../billing/billing-info';
import { Address } from '../common';
import { PaymentReference } from './payment-referance';

export interface PaymentInfo {
  id: number;
  type: string;
  status: string;
  chargedAmount: number;
  chargedTaxAmount: number;
  billingAddress: Address;
  transactionId: number;
  paymentDate: Date;
  paymentReference: PaymentReference;
  billingInfo: BillingInfo[];
}
