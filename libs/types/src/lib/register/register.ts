import { ContactMediumType } from '../lov';
import { Address } from '../common';
import { PrivacySpecification } from '../privacy-specification';
import { Checkbox } from '@libs/widgets';

export namespace Register {
  export interface RegisterRequest {
    address: Address;
    birthDate: number | Date | string;
    consentEmail: boolean;
    consentSms: boolean;
    contactMedium: ContactMediumType.ContactMediumDTO[];
    customerTypeShortCode: string;
    email: string;
    employeeNumber: string;
    firstName: string;
    genderShortCode?: string;
    langShortCode: string;
    lastName: string;
    legacyCustomerId?: string[];
    maidenName: string;
    partyTypeShortCode: string;
    secretKeyword: string;
    unionShortCode?: string;
    password?: string;
    announcementConsent: boolean;
    newsletterConsent: boolean;
    idpVerifyEmailUserId: string;
    idpVerifyEmailRedirectUri: string;
    passtoken: string;
    partyPrivacySpecList: PrivacySpecification.PartyPrivacySpecification[];
  }

  export interface RegisterResponse {
    custId: number;
    name: string;
    surname: string;
  }

  export interface FormFieldValue {
    firstName: string;
    lastName: string;
    email: string;
    birthDate: string;
    password: string;
    langShortCode: string;
    country: string;
    phoneNumber: string;
    partyPrivacySpecList: Checkbox[];
  }
}
