FROM nginx:1.29
ARG APP_FOLDER=dist/wsc

ARG GROUP_NAME="etiya_group"
ARG USER_NAME="etiya_app"

RUN addgroup ${GROUP_NAME}
RUN useradd ${USER_NAME} -G ${GROUP_NAME}

RUN apt update && apt  install -y procps

RUN rm /etc/nginx/conf.d/default.conf

COPY --chown=$USER_NAME:$GROUP_NAME ${APP_FOLDER} /usr/share/nginx/html

RUN chown -R $USER_NAME:$GROUP_NAME /var/cache/nginx && \
        chown -R $USER_NAME:$GROUP_NAME /var/log/nginx && \
        chown -R $USER_NAME:$GROUP_NAME /etc/nginx/conf.d

RUN touch /var/run/nginx.pid && \
        chown -R $USER_NAME:$GROUP_NAME /var/run/nginx.pid

USER ${USER_NAME}

EXPOSE 8080
CMD ["nginx", "-g", "daemon off;"]
